<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预案版本历史 - 应急管理系统</title>
    <!-- 直接引入样式 -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/common.css">
    <style>
        .diff-added {
            background-color: #dcfce7;
        }
        .diff-removed {
            background-color: #fee2e2;
            text-decoration: line-through;
        }
        .sidebar-menu-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            color: #4b5563;
            transition: all 0.2s;
        }
        
        .sidebar-menu-item:hover, .sidebar-menu-item.active {
            background-color: #f3f4f6;
            color: #1f2937;
        }
        
        .sidebar-menu-item.active {
            border-left: 3px solid #2563eb;
        }
        
        .main-content {
            transition: margin-left 0.3s;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Navbar Placeholder -->
    <header>
        <h1>广西公路水路安全畅通与应急处置系统</h1>
        <nav class="tab-navigation">
            <a href="index.html" class="tab-button">总览</a>
            <a href="my_check_tasks.html" class="tab-button">风险隐患管理</a>
            <a href="plan_list.html" class="tab-button active">应急处置</a>
            <button class="tab-button" onclick="alert('应急演练功能暂未开放');">应急演练</button>
        </nav>
    </header>

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area - Removed 'main-content' class -->
        <main id="main-content" class="flex-grow pb-8 px-6 bg-gray-100 min-h-screen">
            <div class="py-6">
                <!-- 页面标题部分 -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800">预案版本历史</h2>
                        <p class="text-gray-600 mt-1">某区低温雨雪冰冻灾害应急预案</p>
                    </div>
                    <div>
                        <a href="plan_list.html" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                            <i class="fas fa-arrow-left mr-2"></i> 返回预案列表
                        </a>
                    </div>
                </div>
                
                <!-- 版本历史列表 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-800">版本历史记录</h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">版本号</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">修订人</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">修订时间</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">修订内容摘要</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="bg-blue-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">v1.2</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张三</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-11-15</td>
                                    <td class="px-6 py-4 text-sm text-gray-500">更新了事件分级与响应条件，增加了应急处置措施</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                        <span class="text-blue-600 font-medium">当前版本</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">v1.1</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李四</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-08</td>
                                    <td class="px-6 py-4 text-sm text-gray-500">完善了组织体系，调整了应急响应流程</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3">查看</button>
                                        <button class="text-green-600 hover:text-green-900 compare-btn" data-version="v1.1">对比</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">v1.0</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王五</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-09-20</td>
                                    <td class="px-6 py-4 text-sm text-gray-500">初始版本</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3">查看</button>
                                        <button class="text-green-600 hover:text-green-900 compare-btn" data-version="v1.0">对比</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 版本比对区域 (初始隐藏) -->
                <div id="version-diff" class="bg-white rounded-lg shadow-md overflow-hidden mb-6 hidden">
                    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-800">版本比对: v1.2 vs <span id="compare-version">v1.1</span></h3>
                        <button id="close-diff" class="text-gray-500 hover:text-gray-700 focus:outline-none">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <!-- 比对内容标签页 -->
                    <div class="p-4 bg-gray-50 border-b border-gray-200">
                        <div class="flex space-x-4">
                            <button class="diff-tab-btn px-3 py-2 text-sm font-medium text-blue-600 bg-white rounded-md shadow-sm" data-tab="basic-info-diff">基本信息 & 总则</button>
                            <button class="diff-tab-btn px-3 py-2 text-sm font-medium text-gray-700 hover:bg-white hover:shadow-sm rounded-md" data-tab="organization-diff">组织体系</button>
                            <button class="diff-tab-btn px-3 py-2 text-sm font-medium text-gray-700 hover:bg-white hover:shadow-sm rounded-md" data-tab="response-diff">应急响应</button>
                        </div>
                    </div>
                    
                    <!-- 比对内容区域 -->
                    <div class="p-6">
                        <!-- 基本信息比对 -->
                        <div id="basic-info-diff" class="diff-tab-content">
                            <div class="mb-6">
                                <h4 class="text-base font-medium text-gray-800 mb-2">事件分级与响应条件</h4>
                                <div class="border border-gray-200 rounded-md p-4 mb-4">
                                    <div class="flex items-center mb-2">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Ⅰ级 - 特别重大</span>
                                    </div>
                                    <div class="mb-2 diff-removed">
                                        <p>全区范围内出现持续性强降雪、低温、道路结冰等灾害，造成交通、电力、通信等基础设施损毁，或造成人员伤亡。</p>
                                    </div>
                                    <div class="mb-2 diff-added">
                                        <p>全区范围内出现<strong>大范围</strong>持续性强降雪、低温、道路结冰等灾害，造成交通、电力、通信等基础设施<strong>大面积</strong>损毁，或造成<strong>重大</strong>人员伤亡。</p>
                                    </div>
                                </div>
                                
                                <div class="border border-gray-200 rounded-md p-4">
                                    <div class="flex items-center mb-2">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-orange-100 text-orange-800">II级 - 重大</span>
                                    </div>
                                    <div class="mb-2 diff-removed">
                                        <p>局部区域出现强降雪、低温、冰冻灾害，造成部分交通阻断、电力中断等影响。</p>
                                    </div>
                                    <div class="mb-2 diff-added">
                                        <p><strong>区域性</strong>强降雪、低温、冰冻灾害，造成交通阻断、电力中断等影响，<strong>部分地区受灾严重</strong>。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 组织体系比对 -->
                        <div id="organization-diff" class="diff-tab-content hidden">
                            <p class="text-gray-500 text-center py-4">组织体系变更内容...</p>
                        </div>
                        
                        <!-- 应急响应比对 -->
                        <div id="response-diff" class="diff-tab-content hidden">
                            <p class="text-gray-500 text-center py-4">应急响应变更内容...</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Kept page-specific logic for version comparison and diff tabs
        document.addEventListener('DOMContentLoaded', function() {
            // 版本对比功能
            const compareButtons = document.querySelectorAll('.compare-btn');
            const versionDiff = document.getElementById('version-diff');
            const closeBtn = document.getElementById('close-diff');
            const compareVersion = document.getElementById('compare-version');
            
            compareButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const version = button.getAttribute('data-version');
                    compareVersion.textContent = version;
                    versionDiff.classList.remove('hidden');
                    // 滚动到比对区域
                    versionDiff.scrollIntoView({behavior: 'smooth'});
                });
            });
            
            closeBtn.addEventListener('click', () => {
                versionDiff.classList.add('hidden');
            });
            
            // 比对内容标签页切换
            const diffTabButtons = document.querySelectorAll('.diff-tab-btn');
            const diffTabContents = document.querySelectorAll('.diff-tab-content');
            
            diffTabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // 重置所有标签按钮样式
                    diffTabButtons.forEach(btn => {
                        btn.classList.remove('text-blue-600', 'bg-white', 'shadow-sm');
                        btn.classList.add('text-gray-700', 'hover:bg-white', 'hover:shadow-sm');
                    });
                    
                    // 激活当前标签按钮
                    button.classList.remove('text-gray-700', 'hover:bg-white', 'hover:shadow-sm');
                    button.classList.add('text-blue-600', 'bg-white', 'shadow-sm');
                    
                    // 隐藏所有内容区域
                    diffTabContents.forEach(content => {
                        content.classList.add('hidden');
                    });
                    
                    // 显示对应的内容区域
                    const tabId = button.getAttribute('data-tab');
                    document.getElementById(tabId).classList.remove('hidden');
                });
            });
        });
    </script>
    <script src="js/navbarComponent.js"></script>
    <script src="js/sidebarComponent_emergency.js"></script>
    <script src="js/loadComponents.js"></script>
</body>
</html> 