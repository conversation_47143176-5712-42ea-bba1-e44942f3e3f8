<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>气象预警与通知 - 应急管理系统</title>
    <!-- 引入样式 -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/unified_header.css">
    <!-- 添加 Element Plus 样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
         html, body {
            height: 100%;
            margin: 0;
        }
         body {
            font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        /* 进度条样式 */
        .progress-bar-container {
            background-color: #e9ecef;
            border-radius: .25rem;
            height: 1.25rem; /* 调整高度以便容纳文字 */
            display: flex; /* 使用flex布局使文字居中 */
            align-items: center; /* 垂直居中 */
            overflow: hidden; /* 确保内部进度条不会超出容器 */
            position: relative; /* 为文字定位提供基准 */
        }
        .progress-bar {
            background-color: #007bff;
            height: 100%;
            line-height: 1.25rem; /* 与容器高度一致 */
            color: white;
            text-align: center;
            white-space: nowrap; /* 防止文字换行 */
            transition: width .6s ease;
            display: flex;
            align-items: center;
            justify-content: center; /* 水平居中 */
            font-size: 0.8rem; /* 调整字体大小 */
        }
        .progress-bar-text { /* 用于显示在进度条外部的文本，以防进度条太短 */
            position: absolute;
            width: 100%;
            text-align: center;
            color: #495057; /* 与背景形成对比 */
            font-size: 0.8rem;
            line-height: 1.25rem;
            z-index: 1; /* 确保在进度条之上 */
        }
        .progress-bar.bg-success { background-color: #28a745; } /* 绿色，表示完成 */
        .progress-bar.bg-warning { background-color: #ffc107; } /* 黄色，表示进行中 */
        .progress-bar.bg-info { background-color: #17a2b8; } /* 蓝色，表示其他状态 */

        /* 地图容器样式 */
        .map-container {
            position: relative;
            width: 100%;
            height: 400px;
            margin-bottom: 20px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .map-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 图例样式 */
        .map-legend {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 5px;
            padding: 8px 12px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.2);
            z-index: 10;
            font-size: 12px;
            width: 90%;
            max-width: 900px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: flex-start;
        }
        .legend-section {
            flex: 1;
            padding: 0 10px;
            border-right: 1px solid #dee2e6;
        }
        .legend-section:last-child {
            border-right: none;
        }
        .legend-title {
            font-weight: bold;
            margin-bottom: 6px;
            font-size: 13px;
            color: #0056b3;
            text-align: center;
        }
        .legend-items {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 4px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 2px;
        }
        .legend-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 5px;
            border: 1px solid rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 8px;
            background-color: rgba(108, 117, 125, 0.8);
        }
        .legend-text {
            font-size: 11px;
            color: #333;
            white-space: nowrap;
        }

        /* 地图标记样式 */
        .map-marker {
            position: absolute;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: rgba(220, 53, 69, 0.8);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: transform 0.2s;
            z-index: 5;
        }
        .map-marker:hover {
            transform: scale(1.1);
        }
        .map-marker.red-alert {
            background-color: rgba(220, 53, 69, 0.8);
        }
        .map-marker.orange-alert {
            background-color: rgba(255, 153, 0, 0.8);
        }
        .map-marker.yellow-alert {
            background-color: rgba(255, 193, 7, 0.8);
        }
        .map-marker.blue-alert {
            background-color: rgba(13, 110, 253, 0.8);
        }
    </style>
</head>
<body class="bg-gray-100 flex flex-col min-h-screen">

    <!-- Navbar Start -->
    <header class="top-nav">
        <div class="system-title">
            <strong>广西公路水路安全畅通与应急处置系统</strong>
        </div>
        <nav class="tab-navigation">
            <a href="new_index.html" class="tab-button">风险一张图</a>
            <a href="plan_list.html" class="tab-button">应急处置</a>
            <button class="tab-button active" onclick="window.location.href='flood-prevention.html'">防汛防台</button>
            <button class="tab-button" onclick="alert('应急演练功能暂未开放');">应急演练</button>
        </nav>
    </header>
    <!-- Navbar End -->

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container h-full" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area -->
        <main id="main-content" class="flex-grow p-6 bg-gray-100 min-h-screen">
            <div class="py-6">
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800">气象预警与通知管理</h2>
                        <p class="text-gray-600 mt-1">查看最新气象预警并管理已下发的通知</p>
                    </div>
                </div>

                <div class="bg-white rounded-t-lg shadow-sm mb-0">
                    <div class="p-4 sm:px-6">
                        <nav class="flex space-x-4 overflow-x-auto pb-1">
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600 focus:outline-none active" data-tab="alert-list">
                                <i class="fas fa-cloud-sun-rain mr-1"></i> 预警列表
                            </button>
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="notification-list">
                                <i class="fas fa-bullhorn mr-1"></i> 预警通知跟踪
                            </button>
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="alert-map">
                                <i class="fas fa-map-marked-alt mr-1"></i> 预警地图
                            </button>
                        </nav>
                    </div>
                </div>

                <div class="bg-white rounded-b-lg shadow-md pt-4">
                    <!-- 预警列表 Tab -->
                    <div id="alert-listTab" class="tab-content active px-6 pb-6">
                        <div class="flex justify-between items-center mb-4">
                           <h3 class="text-xl font-semibold text-gray-800">当前气象预警</h3>
                           <!-- 可能的未来按钮: <button class="bg-blue-500 ...">手动录入预警</button> -->
                       </div>
                       <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                           <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预警ID</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预警类型</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预警级别</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">影响区域</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预警内容</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布时间</th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                        </tr>
                                    </thead>
                                     <tbody id="weatherAlertListTableBody" class="bg-white divide-y divide-gray-200">
                                         <!-- 示例数据会被JS填充 -->
                                     </tbody>
                                </table>
                           </div>
                       </div>
                    </div>

                    <!-- 预警地图 Tab -->
                    <div id="alert-mapTab" class="tab-content px-6 pb-6">
                        <div class="flex justify-between items-center mb-4">
                           <h3 class="text-xl font-semibold text-gray-800">气象预警地图</h3>
                        </div>

                        <div class="map-container">
                            <img src="lib/map.png" alt="广西地图" id="weather-map-image">

                            <!-- 图例 -->
                            <div class="map-legend">
                                <div class="legend-section">
                                    <div class="legend-title">预警类型</div>
                                    <div class="legend-items">
                                        <div class="legend-item">
                                            <div class="legend-icon">
                                                <i class="fas fa-cloud-showers-heavy" style="font-size: 8px;"></i>
                                            </div>
                                            <div class="legend-text">暴雨预警</div>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-icon">
                                                <i class="fas fa-wind" style="font-size: 8px;"></i>
                                            </div>
                                            <div class="legend-text">台风预警</div>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-icon">
                                                <i class="fas fa-temperature-high" style="font-size: 8px;"></i>
                                            </div>
                                            <div class="legend-text">高温预警</div>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-icon">
                                                <i class="fas fa-cloud-rain" style="font-size: 8px;"></i>
                                            </div>
                                            <div class="legend-text">洪水预警</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="legend-section">
                                    <div class="legend-title">预警级别</div>
                                    <div class="legend-items">
                                        <div class="legend-item">
                                            <div class="legend-icon" style="background-color: rgba(220, 53, 69, 0.8);"></div>
                                            <div class="legend-text">红色预警</div>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-icon" style="background-color: rgba(255, 153, 0, 0.8);"></div>
                                            <div class="legend-text">橙色预警</div>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-icon" style="background-color: rgba(255, 193, 7, 0.8);"></div>
                                            <div class="legend-text">黄色预警</div>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-icon" style="background-color: rgba(13, 110, 253, 0.8);"></div>
                                            <div class="legend-text">蓝色预警</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 地图上的标注点 -->
                            <div class="map-marker red-alert" style="top: 130px; left: 170px;" data-id="warning001" data-type="rain" data-level="red">
                                <i class="fas fa-cloud-showers-heavy"></i>
                            </div>
                            <div class="map-marker orange-alert" style="top: 190px; left: 210px;" data-id="warning002" data-type="typhoon" data-level="orange">
                                <i class="fas fa-wind"></i>
                            </div>
                            <div class="map-marker yellow-alert" style="top: 170px; left: 320px;" data-id="warning003" data-type="temperature" data-level="yellow">
                                <i class="fas fa-temperature-high"></i>
                            </div>
                        </div>
                    </div>

                    <!-- 预警通知跟踪 Tab -->
                    <div id="notification-listTab" class="tab-content px-6 pb-6">
                        <div class="flex justify-between items-center mb-4">
                           <h3 class="text-xl font-semibold text-gray-800">预警通知下发与确认跟踪</h3>
                           <!-- 这里的按钮可以考虑下发新通知或筛选 -->
                        </div>
                        <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                           <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">通知ID</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联预警类型</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">通知单位</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">下发时间</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">要求确认时限</th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">确认进度</th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                        </tr>
                                    </thead>
                                     <tbody id="weatherNotificationListTableBody" class="bg-white divide-y divide-gray-200">
                                        <!-- 示例数据会被JS填充 -->
                                     </tbody>
                                </table>
                           </div>
                       </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals for this page (e.g., View Notification Details, Urge Modal) -->
    <!-- 查看通知详情/进度 Modal -->
    <div id="viewNotificationProgressModalApp">
        <el-dialog
            v-model="dialogVisible"
            :title="'查看通知确认进度: ' + notificationTitle"
            width="50%"
            :close-on-click-modal="false"
        >
            <div class="mb-4">
                <p><strong>关联预警:</strong> {{ relatedAlert.type }} - {{ relatedAlert.level }} ({{ relatedAlert.area }})</p>
                <p><strong>预警内容:</strong> {{ relatedAlert.description }}</p>
                <p><strong>通知下发时间:</strong> {{ notificationTime }}</p>
            </div>
            <div class="border rounded-md">
                <div class="bg-gray-100 px-4 py-2 font-semibold flex justify-between border-b">
                    <span>责任单位</span>
                    <span>确认状态</span>
                    <span>确认时间</span>
                </div>
                <div class="max-h-60 overflow-y-auto">
                     <div v-if="progressList.length === 0" class="px-4 py-3 text-gray-500 text-center">暂无单位确认数据</div>
                     <div v-for="(item, index) in progressList" :key="index" class="flex justify-between items-center px-4 py-2" :class="{'border-t': index > 0}">
                        <span class="text-gray-800 w-1/3">{{ item.unitName }} ({{ item.contactPerson }} - {{ item.contactPhone }})</span>
                        <span :class="getStatusTextColor(item.status)" class="w-1/3 text-center">{{ item.status }}</span>
                        <span class="text-gray-600 w-1/3 text-right">{{ item.confirmedTime || '-' }}</span>
                    </div>
                </div>
           </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary" @click="dialogVisible = false">关闭</el-button>
                </span>
            </template>
        </el-dialog>
    </div>

     <!-- 催办 Modal -->
    <div id="urgeNotificationModalApp">
        <el-dialog
            v-model="dialogVisible"
            title="确认催办"
            width="30%"
            :close-on-click-modal="false"
        >
            <p>确定要向 <strong>{{ notificationTitle }}</strong> 通知中尚未确认的单位发送催办提醒吗？</p>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="warning" @click="confirmUrge">确认催办</el-button>
                </span>
            </template>
        </el-dialog>
    </div>


    <!-- Load Libraries -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>

    <!-- Page Specific Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // --- Tab Switching Logic ---
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabId = button.getAttribute('data-tab') + 'Tab';
                    tabButtons.forEach(btn => {
                        btn.classList.remove('text-blue-600', 'border-blue-600', 'active');
                        btn.classList.add('text-gray-500', 'border-transparent');
                    });
                    tabContents.forEach(content => content.classList.remove('active'));
                    button.classList.remove('text-gray-500', 'border-transparent');
                    button.classList.add('text-blue-600', 'border-blue-600', 'active');
                    const activeTabContent = document.getElementById(tabId);
                    if (activeTabContent) activeTabContent.classList.add('active');
                });
            });

            // --- Mock Data ---
            const mockAlerts = [
                { id: 'ALERT001', type: '暴雨', level: '橙色', area: '青秀区南部', content: '预计未来3小时内有50毫米以上强降雨，请注意防范山洪和内涝。', publishTime: '2024-07-30 10:00', responsibleUnits: [{ unitId: 'qs_gov', unitName: '青秀区应急办',负责人: '张三', phone: '13800138000', confirmed: false, confirmedTime: null }, { unitId: 'qs_traffic', unitName: '青秀区交警大队',负责人: '李四', phone: '13900139000', confirmed: false, confirmedTime: null }] },
                { id: 'ALERT002', type: '台风', level: '蓝色', area: '沿海区域', content: '台风"悟空"预计24小时内登陆，请做好防风准备。', publishTime: '2024-07-30 09:00', responsibleUnits: [{ unitId: 'coastal_gov', unitName: '沿海指挥部',负责人: '赵六', phone: '13600136000', confirmed: true, confirmedTime: '2024-07-30 09:30' }] },
                { id: 'ALERT003', type: '高温', level: '黄色', area: '全市范围', content: '全市今日最高气温将达到37℃，请注意防暑降温。', publishTime: '2024-07-29 14:00', responsibleUnits: [{ unitId: 'city_health', unitName: '市卫健委',负责人: '周八', phone: '13400134000', confirmed: false, confirmedTime: null }, { unitId: 'city_emergency', unitName: '市应急局',负责人: '吴九', phone: '13300133000', confirmed: true, confirmedTime: '2024-07-29 14:15' }] }
            ];

            // Simulate notifications based on alerts and their units
            let notificationCounter = 1;
            const mockNotifications = [];
            mockAlerts.forEach(alert => {
                alert.responsibleUnits.forEach(unit => {
                    mockNotifications.push({
                        id: `NOTIF${String(notificationCounter++).padStart(3, '0')}`,
                        alertId: alert.id,
                        alertType: alert.type,
                        alertLevel: alert.level,
                        alertArea: alert.area,
                        alertContent: alert.content,
                        targetUnitId: unit.unitId,
                        targetUnitName: unit.unitName,
                        contactPerson: unit.负责人,
                        contactPhone: unit.phone,
                        dispatchTime: alert.publishTime, // Simplified, assume dispatched at alert publish time
                        confirmDeadline: new Date(new Date(alert.publishTime).getTime() + 2 * 60 * 60 * 1000).toLocaleString(), // Example: 2 hours later
                        isConfirmed: unit.confirmed,
                        confirmedTime: unit.confirmedTime,
                        get totalUnits() { return mockNotifications.filter(n => n.alertId === this.alertId).length; },
                        get confirmedUnitsCount() { return mockNotifications.filter(n => n.alertId === this.alertId && n.isConfirmed).length; },
                        get progress() { return this.totalUnits > 0 ? Math.round((this.confirmedUnitsCount / this.totalUnits) * 100) : 0; },
                        get statusText() {
                            if (this.isConfirmed) return '已确认';
                            // Add logic for overdue if deadline is passed
                            return '待确认';
                        }
                    });
                });
            });


            // --- Populate Alert List Table ---
            const alertListTableBody = document.getElementById('weatherAlertListTableBody');
            if(alertListTableBody) {
                mockAlerts.forEach(alert => {
                    const row = alertListTableBody.insertRow();
                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${alert.id}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${alert.type}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getAlertLevelClass(alert.level)}">${alert.level}</span></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${alert.area}</td>
                        <td class="px-6 py-4 text-sm text-gray-500 truncate max-w-xs" title="${alert.content}">${alert.content}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${alert.publishTime}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none btn-view-alert-details" data-alert-id="${alert.id}" title="查看详情/通知单位"><i class="fas fa-eye"></i></button>
                            <!-- <button class="text-green-600 hover:text-green-800 focus:outline-none ml-2 btn-dispatch-alert-notification" data-alert-id="${alert.id}" title="下发通知"><i class="fas fa-paper-plane"></i></button> -->
                        </td>
                    `;
                });
            }
            function getAlertLevelClass(level) {
                if (level === '红色') return 'bg-red-100 text-red-800';
                if (level === '橙色') return 'bg-yellow-100 text-yellow-800';
                if (level === '黄色') return 'bg-yellow-100 text-yellow-800'; // Corrected
                if (level === '蓝色') return 'bg-blue-100 text-blue-800';
                return 'bg-gray-100 text-gray-800';
            }


            // --- Populate Notification List Table ---
            const notificationListTableBody = document.getElementById('weatherNotificationListTableBody');
            if(notificationListTableBody){
                mockNotifications.forEach(notif => {
                     // Calculate progress specifically for this alert group
                    const relatedAlertUnits = mockNotifications.filter(n => n.alertId === notif.alertId);
                    const confirmedCount = relatedAlertUnits.filter(n => n.isConfirmed).length;
                    const totalCount = relatedAlertUnits.length;
                    const progressPercent = totalCount > 0 ? Math.round((confirmedCount / totalCount) * 100) : 0;

                    const row = notificationListTableBody.insertRow();
                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${notif.id}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${notif.alertType} (${notif.alertLevel})</td>
                        <td class="px-6 py-4 text-sm text-gray-500 truncate max-w-xs" title="${notif.targetUnitName}">${notif.targetUnitName}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${notif.dispatchTime}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${notif.confirmDeadline}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                            ${progressPercent}%
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${notif.isConfirmed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                                ${notif.isConfirmed ? '已确认' : '待确认'}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-notification-progress" data-notification-id="${notif.id}" data-alert-id="${notif.alertId}" title="查看详情与进度"><i class="fas fa-tasks"></i></button>
                            ${!notif.isConfirmed ? `<button class="text-yellow-600 hover:text-yellow-800 focus:outline-none btn-urge-notification" data-notification-id="${notif.id}" title="催办"><i class="fas fa-bell"></i></button>` : ''}
                        </td>
                    `;
                });
            }

            // --- Vue Apps for Modals ---
            const ViewNotificationProgressModalApp = {
                 data() {
                     return {
                         dialogVisible: false,
                         notificationTitle: '',
                         relatedAlert: { type: '', level: '', area: '', description: ''},
                         notificationTime: '',
                         progressList: [] // { unitName, contactPerson, contactPhone, status, confirmedTime }
                     };
                 },
                 methods: {
                     openModal(notificationId) {
                         const notification = mockNotifications.find(n => n.id === notificationId);
                         if (!notification) return;

                         this.notificationTitle = `${notification.alertType}预警 (${notification.targetUnitName})`;
                         const alert = mockAlerts.find(a => a.id === notification.alertId);
                         if(alert){
                            this.relatedAlert = { type: alert.type, level: alert.level, area: alert.area, description: alert.content };
                         }
                         this.notificationTime = notification.dispatchTime;

                         // Find all units related to this specific alert
                         this.progressList = mockNotifications
                            .filter(n => n.alertId === notification.alertId)
                            .map(n => ({
                                unitName: n.targetUnitName,
                                contactPerson: n.contactPerson,
                                contactPhone: n.contactPhone,
                                status: n.isConfirmed ? '已确认' : '待确认',
                                confirmedTime: n.confirmedTime
                            }));
                         this.dialogVisible = true;
                     },
                     getStatusTextColor(status) {
                         if (status === '已确认') return 'text-green-600 font-semibold';
                         return 'text-yellow-600';
                     }
                 }
            };
            const viewNotificationProgressVm = Vue.createApp(ViewNotificationProgressModalApp).use(ElementPlus).mount('#viewNotificationProgressModalApp');

            const UrgeNotificationModalApp = {
                 data() {
                     return {
                         dialogVisible: false,
                         notificationTitle: '',
                         notificationId: null
                     };
                 },
                 methods: {
                     openModal(notificationId) {
                         const notification = mockNotifications.find(n => n.id === notificationId);
                         if (!notification) return;
                         this.notificationId = notificationId;
                         this.notificationTitle = `${notification.alertType}预警发至 ${notification.targetUnitName}`;
                         this.dialogVisible = true;
                     },
                     confirmUrge() {
                         console.log(`Sending urge for notification ID: ${this.notificationId}`);
                         alert(`已向 ${this.notificationTitle} 发送催办提醒 (模拟)`);
                         this.dialogVisible = false;
                         // Potentially update UI or data source
                     }
                 }
            };
            const urgeNotificationVm = Vue.createApp(UrgeNotificationModalApp).use(ElementPlus).mount('#urgeNotificationModalApp');


            // --- Event Listeners for Table Buttons ---
            document.getElementById('main-content').addEventListener('click', (event) => {
                const button = event.target.closest('button');
                if (!button) return;

                if (button.classList.contains('btn-view-alert-details')) {
                    const alertId = button.dataset.alertId;
                    // For now, just log. Could open a modal showing alert details + responsible units from `mockAlerts`
                    console.log('View details for alert ID:', alertId);
                    alert('查看预警详情 (功能待实现，可弹窗显示 mockAlerts 中对应项的完整信息，并列出所有待通知单位)。');
                    // Example: Open index.html's weather notification modal for this alert
                    // const alertDataForModal = mockAlerts.find(a => a.id === alertId);
                    // if (alertDataForModal && typeof window.openWeatherNotificationModal === 'function') {
                    //     window.openWeatherNotificationModal(alertDataForModal); // Assuming it's globally accessible from script.js
                    // }
                } else if (button.classList.contains('btn-view-notification-progress')) {
                    const notificationId = button.dataset.notificationId;
                    viewNotificationProgressVm.openModal(notificationId);
                } else if (button.classList.contains('btn-urge-notification')) {
                    const notificationId = button.dataset.notificationId;
                    urgeNotificationVm.openModal(notificationId);
                }
            });

        });
    </script>

    <!-- Load component HTML first -->
    <script src="js/sidebarComponent_weather.js"></script> <!-- Using weather sidebar -->
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>
</body>
</html>