<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风险隐患检查 - 应急管理系统</title>
    <!-- 引入样式 -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- 添加 Element Plus 样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        html, body { /* Added for full height */
            height: 100%;
            margin: 0;
        }
        body { /* Keep base font */
             font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
        }
        /* Removed old sidebar/main-content layout styles from common.css previously */

        /* Keep specific styles for status badges, TreeSelect, Upload list */
        .status-badge {
          padding: 0.25rem 0.5rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 600;
          display: inline-block;
          text-align: center;
          min-width: 4rem; /* 保持一致宽度 */
        }
        .status-high { background-color: #FEE2E2; color: #991B1B; } /* 红色 - 高风险 */
        .status-medium { background-color: #FEF3C7; color: #92400E; } /* 黄色 - 中风险 */
        .status-low { background-color: #DBEAFE; color: #1E40AF; } /* 蓝色 - 低风险 */
        .status-none { background-color: #F3F4F6; color: #4B5563; } /* 灰色 - 无风险/已整改 */

        /* 自定义 Tree Select 样式 */
        .el-tree-select {
            width: 100% !important;
        }
        .el-select-dropdown__wrap {
            max-height: 400px;
        }
        .el-tree-node__content {
            height: 32px;
        }
        .el-tree-node__label {
            font-size: 14px;
        }
        .upload-list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Navbar Placeholder -->
    <div id="navbar-placeholder"></div>

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container h-full" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area -->
        <main id="main-content" class="flex-grow pb-8 px-6 bg-gray-100">
            <div class="py-6">
                <!-- 页面标题和操作按钮 -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 class="text-2xl font-semibold text-gray-800">隐患列表</h2>
                        <p class="text-sm text-gray-500 mt-1">查看和管理风险隐患检查记录</p>
                    </div>
                    <!-- <button id="btnAddHazard" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <i class="fas fa-plus mr-2"></i> 添加检查记录
                    </button> -->
                </div>

                <!-- 过滤栏 -->
                <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <label for="filterCity" class="block text-sm font-medium text-gray-700 mb-1">市</label>
                            <input type="text" id="filterCity" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入市名称">
                        </div>
                        <div>
                            <label for="filterCounty" class="block text-sm font-medium text-gray-700 mb-1">区/县</label>
                            <input type="text" id="filterCounty" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入区/县名称">
                        </div>
                        <div>
                            <label for="filterOrgUnit" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                            <div id="filterOrgApp">
                                <el-tree-select
                                    v-model="selectedUnits"
                                    :data="unitOptions"
                                    multiple
                                    show-checkbox
                                    :props="{ value: 'value', label: 'label', children: 'children' }"
                                    placeholder="请选择单位"
                                    class="block w-full"
                                    @change="handleFilterOrgChange"
                                />
                            </div>
                        </div>
                        <div>
                            <label for="filterCategory" class="block text-sm font-medium text-gray-700 mb-1">检查类别</label>
                            <div id="filterCategoryApp">
                                <el-tree-select
                                    v-model="selectedCategories"
                                    :data="categoryOptions"
                                    multiple
                                    show-checkbox
                                    check-strictly="false"  
                                    :props="{ value: 'value', label: 'label', children: 'children' }"
                                    placeholder="请选择检查类别"
                                    class="block w-full"
                                    @change="handleFilterCategoryChange"
                                />
                            </div>
                        </div>
                        <div>
                            <label for="filterRiskLevel" class="block text-sm font-medium text-gray-700 mb-1">风险等级</label>
                            <select id="filterRiskLevel" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                <option value="">全部</option>
                                <option value="high">高</option>
                                <option value="medium">中</option>
                                <option value="low">低</option>
                                <option value="none">无风险/已整改</option>
                            </select>
                        </div>
                        <div>
                            <label for="filterIsHazard" class="block text-sm font-medium text-gray-700 mb-1">是否为隐患点</label>
                            <select id="filterIsHazard" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                <option value="">全部</option>
                                <option value="yes">是</option>
                                <option value="no">否</option>
                            </select>
                        </div>
                    </div>
                     <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mt-4">
                         <div>
                            <label for="filterRoadNumber" class="block text-sm font-medium text-gray-700 mb-1">路段编号</label>
                            <input type="text" id="filterRoadNumber" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入路段编号">
                        </div>
                        <div class="col-start-4 md:col-start-5 flex items-end space-x-2 justify-end">
                            <button id="btnFilter" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                              <i class="fas fa-search mr-1"></i> 查询
                            </button>
                            <button id="btnResetFilter" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                              <i class="fas fa-undo mr-1"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">市</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">区/县</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查类别</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路段编号</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险等级</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">是否隐患点</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险点描述</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!-- 示例数据行 -->
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">南宁市</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">青秀区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">南宁市交通运输局</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">风险路段-地质灾害风险</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">G324</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-high">高</span></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-semibold">是</td>
                                    <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">K1500+200处边坡有落石风险</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                        <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="1"><i class="fas fa-eye"></i></button>
                                        <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="1"><i class="fas fa-edit"></i></button>
                                        <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete" data-id="1"><i class="fas fa-trash-alt"></i></button>
                                        <button class="text-green-600 hover:text-green-800 focus:outline-none ml-2 btn-rectify" data-id="1" title="生成整改任务"><i class="fas fa-clipboard-list"></i></button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钦州市</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">灵山县</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钦州市交通运输局</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">基础保障设施隐患-防洪标识</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">S211</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-low">低</span></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">否</td>
                                    <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">桥梁限高标识不清</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                        <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="2"><i class="fas fa-eye"></i></button>
                                        <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="2"><i class="fas fa-edit"></i></button>
                                        <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete" data-id="2"><i class="fas fa-trash-alt"></i></button>
                                        <button class="text-green-600 hover:text-green-800 focus:outline-none ml-2 btn-rectify" data-id="2" title="生成整改任务"><i class="fas fa-clipboard-list"></i></button>
                                    </td>
                                </tr>
                                 <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">玉林市</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">福绵区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">玉林市交通运输局</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">涉灾隐患点-桥梁</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">X456</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-medium">中</span></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-semibold">是</td>
                                    <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">XX桥梁伸缩缝堵塞</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                        <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="3"><i class="fas fa-eye"></i></button>
                                        <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="3"><i class="fas fa-edit"></i></button>
                                        <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete" data-id="3"><i class="fas fa-trash-alt"></i></button>
                                        <button class="text-green-600 hover:text-green-800 focus:outline-none ml-2 btn-rectify" data-id="3" title="生成整改任务"><i class="fas fa-clipboard-list"></i></button>
                                    </td>
                                </tr>
                                <!-- 更多数据行 -->
                            </tbody>
                        </table>
                    </div>
                    <!-- 分页 -->
                    <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">3</span> 条记录
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">上一页</span>
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-100">
                                        1
                                    </a>
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">下一页</span>
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 添加/编辑隐患模态框 -->
    <div id="hazardModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800" id="modalTitle">添加检查记录</h3>
                <button class="text-gray-400 hover:text-gray-500 focus:outline-none btn-close-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="px-6 py-4 max-h-[70vh] overflow-y-auto">
                <form id="hazardForm">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="modalCategory" class="block text-sm font-medium text-gray-700 mb-1">检查类别 <span class="text-red-500">*</span></label>
                            <select id="modalCategory" name="category" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="">请选择</option>
                                <optgroup label="风险路段">
                                    <option value="risk_section_flood">山洪淹没区风险路段</option>
                                    <option value="risk_section_geology">地质灾害风险路段</option>
                                </optgroup>
                                <option value="management_mechanism">工作管理机制隐患</option>
                                <optgroup label="基础保障设施隐患">
                                     <option value="basic_facilities_sign">防洪标识</option>
                                     <option value="basic_facilities_trail">检查步道</option>
                                     <option value="basic_facilities_hazard">涉灾隐患点</option>
                                </optgroup>
                                 <optgroup label="涉灾隐患点">
                                    <option value="hazard_points_slope">边坡</option>
                                    <option value="hazard_points_drainage">防洪排水设施</option>
                                    <option value="hazard_points_bridge">桥梁</option>
                                    <option value="hazard_points_tunnel">隧道</option>
                                </optgroup>
                            </select>
                        </div>
                         <div>
                            <label for="modalCity" class="block text-sm font-medium text-gray-700 mb-1">市、区/县名称 <span class="text-red-500">*</span></label>
                            <!-- TODO: 联动下拉或自动关联 -->
                            <input type="text" id="modalCity" name="city_county" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="系统自动关联/手动选择" required>
                        </div>
                        <div>
                            <label for="modalOrgUnit" class="block text-sm font-medium text-gray-700 mb-1">所属单位 <span class="text-red-500">*</span></label>
                            <div id="modalOrgApp">
                                <el-tree-select
                                    v-model="selectedUnit"
                                    :data="unitOptions"
                                    :props="{ value: 'value', label: 'label', children: 'children' }"
                                    placeholder="请选择单位"
                                    class="block w-full"
                                    clearable
                                    @change="handleModalOrgChange"
                                />
                            </div>
                        </div>
                        <div>
                            <label for="modalRoadNumber" class="block text-sm font-medium text-gray-700 mb-1">公路编号 <span class="text-red-500">*</span></label>
                            <!-- TODO: 根据市/区县加载 -->
                            <select id="modalRoadNumber" name="road_number" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="">请先选择市/区县</option>
                                <option value="G324">G324</option>
                                <option value="S211">S211</option>
                                <option value="X456">X456</option>
                            </select>
                        </div>
                         <div>
                            <label for="modalStartStake" class="block text-sm font-medium text-gray-700 mb-1">起点桩号</label>
                            <input type="text" id="modalStartStake" name="start_stake" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如: K1500+200">
                        </div>
                         <div>
                            <label for="modalEndStake" class="block text-sm font-medium text-gray-700 mb-1">止点桩号</label>
                            <input type="text" id="modalEndStake" name="end_stake" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如: K1500+500">
                        </div>
                    </div>

                    <div class="mt-4">
                      <label for="modalRiskDescription" class="block text-sm font-medium text-gray-700 mb-1">风险点描述 <span class="text-red-500">*</span></label>
                      <textarea id="modalRiskDescription" name="risk_description" rows="3" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required></textarea>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">现场照片/附件</label>
                            <input type="file" id="modalPhotos" name="photos" multiple class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                            <div id="uploadedFilesList" class="mt-2 text-sm text-gray-600"></div>
                        </div>
                        <div>
                            <label for="modalRiskLevel" class="block text-sm font-medium text-gray-700 mb-1">风险等级 <span class="text-red-500">*</span></label>
                            <select id="modalRiskLevel" name="risk_level" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="high">高</option>
                                <option value="medium">中</option>
                                <option value="low">低</option>
                                <option value="none">无风险</option>
                            </select>
                        </div>
                         <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">是否已采取措施 <span class="text-red-500">*</span></label>
                            <div class="flex items-center space-x-4 mt-1">
                                <label><input type="radio" name="measures_taken" value="yes" class="mr-1"> 是</label>
                                <label><input type="radio" name="measures_taken" value="no" class="mr-1" checked> 否</label>
                            </div>
                        </div>
                         <div>
                            <label for="modalMeasures" class="block text-sm font-medium text-gray-700 mb-1">已（拟）采取的措施</label>
                            <!-- TODO: 可多选 + 自定义 -->
                            <textarea id="modalMeasures" name="measures" rows="2" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="可多选预设措施，或手动填写"></textarea>
                            <input type="file" id="modalMeasureFiles" name="measure_files" multiple class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-1 file:px-2 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-gray-50 file:text-gray-700 hover:file:bg-gray-100" placeholder="上传方案/报告">
                            <div id="uploadedMeasureFilesList" class="mt-1 text-sm text-gray-600"></div>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 border-t pt-4">
                        <div>
                           <label class="block text-sm font-medium text-gray-700 mb-1">省级责任单位及人员</label>
                           <p class="text-sm text-gray-600">系统自动关联</p>
                        </div>
                         <div>
                           <label class="block text-sm font-medium text-gray-700 mb-1">复核责任单位及人员</label>
                           <p class="text-sm text-gray-600">系统自动关联</p>
                        </div>
                         <div>
                           <label class="block text-sm font-medium text-gray-700 mb-1">排查责任单位及人员</label>
                           <p class="text-sm text-gray-600">系统自动关联</p>
                        </div>
                    </div>
                </form>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-modal">
                  取消
                </button>
                <button id="btnSave" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  保存
                </button>
            </div>
        </div>
    </div>

    <!-- 查看详情模态框 -->
    <div id="viewModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">查看检查记录详情</h3>
                <button class="text-gray-400 hover:text-gray-500 focus:outline-none btn-close-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="px-6 py-4 max-h-[70vh] overflow-y-auto">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 pb-4 border-b">
                    <div><strong class="text-gray-600">检查类别:</strong> <span id="view-category"></span></div>
                    <div><strong class="text-gray-600">市/区县:</strong> <span id="view-city_county"></span></div>
                    <div><strong class="text-gray-600">所属单位:</strong> <span id="view-orgUnit"></span></div>
                    <div><strong class="text-gray-600">公路编号:</strong> <span id="view-roadNumber"></span></div>
                    <div><strong class="text-gray-600">起点桩号:</strong> <span id="view-startStake"></span></div>
                    <div><strong class="text-gray-600">止点桩号:</strong> <span id="view-endStake"></span></div>
                    <div><strong class="text-gray-600">风险等级:</strong> <span id="view-riskLevel"></span></div>
                    <div><strong class="text-gray-600">是否隐患点:</strong> <span id="view-isHazard" class="font-semibold"></span></div>
                    <div><strong class="text-gray-600">是否已采取措施:</strong> <span id="view-measuresTaken"></span></div>
                </div>
                 <div class="mb-4">
                    <strong class="text-gray-600 block mb-1">风险点描述:</strong>
                    <p id="view-riskDescription" class="text-gray-800 bg-gray-50 p-2 rounded"></p>
                 </div>
                  <div class="mb-4">
                    <strong class="text-gray-600 block mb-1">已（拟）采取的措施:</strong>
                    <p id="view-measures" class="text-gray-800 bg-gray-50 p-2 rounded"></p>
                 </div>
                 <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                     <div>
                         <strong class="text-gray-600 block mb-1">现场照片/附件:</strong>
                         <div id="view-photos" class="text-blue-600"></div>
                     </div>
                      <div>
                         <strong class="text-gray-600 block mb-1">措施附件:</strong>
                         <div id="view-measureFiles" class="text-blue-600"></div>
                     </div>
                 </div>
                 <div class="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
                     <div><strong class="text-gray-600">省级责任单位:</strong> <span id="view-provincialResp"></span></div>
                     <div><strong class="text-gray-600">复核责任单位:</strong> <span id="view-reviewResp"></span></div>
                     <div><strong class="text-gray-600">排查责任单位:</strong> <span id="view-inspectResp"></span></div>
                 </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 btn-close-modal">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div id="deleteConfirmModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">确认删除</h3>
            </div>
            <div class="px-6 py-4">
                <p class="text-sm text-gray-700">您确定要删除这条检查记录吗？相关整改任务（如有）将可能受到影响。此操作无法撤销。</p>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-modal">
                  取消
                </button>
                <button id="btnConfirmDelete" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                  删除
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <script>
        // 模拟单位数据 (应从后端获取)
        const unitData = [{
            value: '1', label: '广西壮族自治区交通运输厅', children: [
                { value: '1.1', label: '直属事业单位及专项机构', children: [
                        { value: '1.1.1', label: '自治区公路发展中心' },
                        { value: '1.1.2', label: '自治区高速公路发展中心' },
                        { value: '1.1.3', label: '自治区道路运输发展中心' }
                ]},
                { value: '1.2', label: '市级交通运输局', children: [
                        { value: '1.2.1', label: '钦州市交通运输局' },
                        { value: '1.2.2', label: '南宁市交通运输局' },
                        { value: '1.2.3', label: '玉林市交通运输局' }
                ]}
            ]
        }];

        // Vue 应用 - 用于筛选栏所属单位多选树形选择器
        const filterOrgApp = Vue.createApp({
            data() { return { selectedUnits: [], unitOptions: unitData } },
            methods: { handleFilterOrgChange(value) { console.log('筛选选中的单位:', value); } }
        }).use(ElementPlus);
        filterOrgApp.mount('#filterOrgApp');

        // Vue 应用 - 用于模态框内所属单位单选树形选择器
        const modalOrgApp = Vue.createApp({
            data() { return { selectedUnit: null, unitOptions: unitData } },
            methods: { handleModalOrgChange(value) { console.log('模态框选中的单位:', value); } }
        }).use(ElementPlus);
        modalOrgApp.mount('#modalOrgApp');

        // 模拟检查类别数据 (应从后端获取或配置)
        const categoryData = [
            { value: 'risk_section', label: '风险路段', children: [
                    { value: 'risk_section_flood', label: '山洪淹没区风险路段' },
                    { value: 'risk_section_geology', label: '地质灾害风险路段' }
            ]},
            { value: 'management_mechanism', label: '工作管理机制隐患' },
            { value: 'basic_facilities', label: '基础保障设施隐患', children: [
                    { value: 'basic_facilities_sign', label: '防洪标识' },
                    { value: 'basic_facilities_trail', label: '检查步道' },
                    { value: 'basic_facilities_hazard', label: '涉灾隐患点' } // 注意：文档中此项为三级标题，这里简化处理
            ]},
            { value: 'hazard_points', label: '涉灾隐患点', children: [
                    { value: 'hazard_points_slope', label: '边坡' },
                    { value: 'hazard_points_drainage', label: '防洪排水设施' },
                    { value: 'hazard_points_bridge', label: '桥梁' },
                    { value: 'hazard_points_tunnel', label: '隧道' }
            ]}
        ];

        // Vue 应用 - 用于筛选栏检查类别多选树形选择器
        const filterCategoryApp = Vue.createApp({
            data() { return { selectedCategories: [], categoryOptions: categoryData } },
            methods: { handleFilterCategoryChange(value) { console.log('筛选选中的类别:', value); } }
        }).use(ElementPlus);
        filterCategoryApp.mount('#filterCategoryApp');

        document.addEventListener('DOMContentLoaded', function() {
            const hazardModal = document.getElementById('hazardModal');
            const viewModal = document.getElementById('viewModal');
            const deleteConfirmModal = document.getElementById('deleteConfirmModal');
            const btnAddHazard = document.getElementById('btnAddHazard');
            const hazardForm = document.getElementById('hazardForm');
            const modalTitle = document.getElementById('modalTitle');
            let currentEditId = null; // 用于存储当前编辑的记录ID
            let hazardIdToDelete = null; // 用于存储待删除的ID

            // --- 模态框控制 ---
            const openModal = (modal) => modal.classList.remove('hidden');
            const closeModal = (modal) => modal.classList.add('hidden');

            // 打开新增模态框
            if (btnAddHazard) {
                btnAddHazard.addEventListener('click', () => {
                    currentEditId = null; // 清除编辑ID
                    modalTitle.textContent = '添加检查记录';
                    hazardForm.reset(); // 清空表单
                    modalOrgApp._instance.data.selectedUnit = null; // 清空树形选择器
                    clearUploadedFiles('uploadedFilesList');
                    clearUploadedFiles('uploadedMeasureFilesList');
                    openModal(hazardModal);
                });
            }

            // 关闭模态框按钮
            document.querySelectorAll('.btn-close-modal').forEach(button => {
                button.addEventListener('click', () => {
                    closeModal(hazardModal);
                    closeModal(viewModal);
                    closeModal(deleteConfirmModal);
                });
            });

            // --- 列表操作按钮 ---
            document.querySelector('tbody')?.addEventListener('click', (event) => {
                const target = event.target.closest('button');
                if (!target) return;

                const id = target.getAttribute('data-id');

                if (target.classList.contains('btn-view')) {
                    console.log('查看 ID:', id);
                    // TODO: 根据ID获取数据并填充查看模态框
                    populateViewModal(id); // 假设此函数填充模态框
                    openModal(viewModal);
                } else if (target.classList.contains('btn-edit')) {
                    console.log('编辑 ID:', id);
                    currentEditId = id;
                    modalTitle.textContent = '编辑检查记录';
                    // TODO: 根据ID获取数据并填充编辑模态框表单
                    populateEditModal(id); // 假设此函数填充表单
                    openModal(hazardModal);
                } else if (target.classList.contains('btn-delete')) {
                    console.log('删除 ID:', id);
                    hazardIdToDelete = id;
                    openModal(deleteConfirmModal);
                } else if (target.classList.contains('btn-rectify')) {
                    console.log('生成整改任务 ID:', id);
                    alert(`模拟为记录 ${id} 生成整改任务`);
                    // TODO: 跳转或打开整改任务相关的模态框/页面
                }
            });

            // --- 模态框表单处理 ---
             // 文件上传显示逻辑 (简化版)
            document.getElementById('modalPhotos')?.addEventListener('change', function(e) {
                displayUploadedFiles(e.target.files, 'uploadedFilesList');
            });
            document.getElementById('modalMeasureFiles')?.addEventListener('change', function(e) {
                displayUploadedFiles(e.target.files, 'uploadedMeasureFilesList');
            });

             // 保存按钮
            document.getElementById('btnSave')?.addEventListener('click', () => {
                // TODO: 表单验证
                const formData = new FormData(hazardForm);
                 // 手动添加 TreeSelect 的值
                formData.append('organization_unit_id', modalOrgApp._instance.data.selectedUnit);

                // 手动添加 is_hazard 单选按钮的值
                const isHazardSelected = document.querySelector('input[name="is_hazard"]:checked');
                if (isHazardSelected) {
                    formData.append('is_hazard', isHazardSelected.value);
                }

                // 模拟文件处理，实际应上传文件本身
                const photoFiles = document.getElementById('modalPhotos').files;
                for(let i=0; i<photoFiles.length; i++){
                    formData.append('photos[]', photoFiles[i].name); // 仅示例名称
                }
                 const measureFiles = document.getElementById('modalMeasureFiles').files;
                for(let i=0; i<measureFiles.length; i++){
                    formData.append('measure_files[]', measureFiles[i].name); // 仅示例名称
                }

                const data = Object.fromEntries(formData.entries());

                if (currentEditId) {
                    console.log('更新记录 ID:', currentEditId, '数据:', data);
                    // TODO: 发送更新请求
                    alert(`更新记录 ${currentEditId} 成功 (模拟)`);
                } else {
                    console.log('新增记录 数据:', data);
                    // TODO: 发送新增请求
                     alert('新增记录成功 (模拟)');
                }
                closeModal(hazardModal);
                // TODO: 刷新列表
            });

            // --- 删除确认 ---
            document.getElementById('btnConfirmDelete')?.addEventListener('click', () => {
                 if (hazardIdToDelete) {
                    console.log('确认删除 ID:', hazardIdToDelete);
                    // TODO: 发送删除请求
                    alert(`删除记录 ${hazardIdToDelete} 成功 (模拟)`);
                    hazardIdToDelete = null; // 重置
                    closeModal(deleteConfirmModal);
                    // TODO: 刷新列表
                 }
            });

            // --- 筛选栏处理 ---
             document.getElementById('btnFilter')?.addEventListener('click', () => {
                const filterData = {
                    city: document.getElementById('filterCity').value,
                    county: document.getElementById('filterCounty').value,
                    units: filterOrgApp._instance.data.selectedUnits,
                    categories: filterCategoryApp._instance.data.selectedCategories,
                    riskLevel: document.getElementById('filterRiskLevel').value,
                    isHazard: document.getElementById('filterIsHazard').value,
                    roadNumber: document.getElementById('filterRoadNumber').value,
                };
                console.log('应用筛选:', filterData);
                // TODO: 应用筛选条件刷新列表
                 alert('应用筛选条件 (模拟)');
            });

            document.getElementById('btnResetFilter')?.addEventListener('click', () => {
                 document.getElementById('filterCity').value = '';
                 document.getElementById('filterCounty').value = '';
                 filterOrgApp._instance.data.selectedUnits = [];
                 filterCategoryApp._instance.data.selectedCategories = [];
                 document.getElementById('filterRiskLevel').value = '';
                 document.getElementById('filterIsHazard').value = '';
                 document.getElementById('filterRoadNumber').value = '';
                 console.log('重置筛选条件');
                 // TODO: 重置筛选并刷新列表
                 alert('重置筛选条件 (模拟)');
            });

        });

        // --- 辅助函数 ---
        function populateViewModal(id) {
          // 模拟获取数据并填充查看模态框
          console.log(`模拟获取 ID=${id} 的数据填充查看模态框`);
          document.getElementById('view-category').textContent = '风险路段-地质灾害风险';
          document.getElementById('view-city_county').textContent = '南宁市 / 青秀区';
          document.getElementById('view-orgUnit').textContent = '南宁市交通运输局';
          document.getElementById('view-roadNumber').textContent = 'G324';
          document.getElementById('view-startStake').textContent = 'K1500+200';
          document.getElementById('view-endStake').textContent = 'K1500+500';
          document.getElementById('view-riskLevel').innerHTML = '<span class="status-badge status-high">高</span>';
          document.getElementById('view-isHazard').textContent = '是';
          document.getElementById('view-isHazard').classList.add('text-red-600');
          document.getElementById('view-measuresTaken').textContent = '否';
          document.getElementById('view-riskDescription').textContent = `记录 ${id}: K1500+200处边坡有落石风险，需尽快处理。`;
          document.getElementById('view-measures').textContent = '尚未采取措施';
          document.getElementById('view-photos').innerHTML = '<a href="#" class="hover:underline">photo1.jpg</a>, <a href="#" class="hover:underline">photo2.jpg</a>';
          document.getElementById('view-measureFiles').innerHTML = '无';
          document.getElementById('view-provincialResp').textContent = '省交通厅 - 李四';
          document.getElementById('view-reviewResp').textContent = '市交通局 - 王五';
          document.getElementById('view-inspectResp').textContent = '南宁市交通运输局 - 张三';
        }

        function populateEditModal(id) {
           // 模拟获取数据并填充编辑模态框
           console.log(`模拟获取 ID=${id} 的数据填充编辑模态框`);
           hazardForm.reset(); // 先重置
           document.getElementById('modalCategory').value = 'risk_section_geology';
           document.getElementById('modalCity').value = '南宁市 / 青秀区';
           modalOrgApp._instance.data.selectedUnit = '1.2.2'; // 模拟选中南宁市交通运输局
           document.getElementById('modalRoadNumber').value = 'G324';
           document.getElementById('modalStartStake').value = 'K1500+200';
           document.getElementById('modalEndStake').value = 'K1500+500';
           document.getElementById('modalRiskDescription').value = `记录 ${id}: K1500+200处边坡有落石风险，需尽快处理。（编辑中）`;
           document.getElementById('modalRiskLevel').value = 'high';
           document.querySelector('input[name="measures_taken"][value="no"]').checked = true;
           document.getElementById('modalMeasures').value = '';
           // 模拟已上传的文件
           const filesList = document.getElementById('uploadedFilesList');
           clearUploadedFiles('uploadedFilesList');
           filesList.innerHTML += `<div class="upload-list-item"><span>existing_photo1.jpg</span> <button type="button" class="text-red-500 text-xs" onclick="this.parentNode.remove()">删除</button></div>`;
           filesList.innerHTML += `<div class="upload-list-item"><span>existing_photo2.jpg</span> <button type="button" class="text-red-500 text-xs" onclick="this.parentNode.remove()">删除</button></div>`;
           clearUploadedFiles('uploadedMeasureFilesList');

        }

        function displayUploadedFiles(files, listElementId) {
            const listElement = document.getElementById(listElementId);
            if (!listElement) return;
            // listElement.innerHTML = ''; // 清空现有列表项，如果需要替换的话
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const listItem = document.createElement('div');
                listItem.classList.add('upload-list-item');
                listItem.innerHTML = `
                    <span>${file.name} (${(file.size / 1024).toFixed(1)} KB)</span>
                    <button type="button" class="text-red-500 text-xs" onclick="this.parentNode.remove()">删除</button>
                `;
                listElement.appendChild(listItem);
            }
        }
        function clearUploadedFiles(listElementId) {
             const listElement = document.getElementById(listElementId);
             if (listElement) listElement.innerHTML = '';
        }

    </script>

    <!-- Load component HTML first -->
    <script src="js/navbarComponent_risk.js"></script>
    <script src="js/sidebarComponent_risk.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>
</body>
</html> 