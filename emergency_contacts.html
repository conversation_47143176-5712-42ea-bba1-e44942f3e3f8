<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应急通讯录 - 应急管理系统</title>
    <!-- 引入样式 -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/common.css"> <!-- 假设 common.css 存在且包含必要的基本样式 -->
    <link rel="stylesheet" href="css/unified_header.css">
    <!-- 添加 Element Plus 样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        html, body {
            height: 100%;
            margin: 0;
            font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
        }
        /* 主标签页样式 */
        .main-tab-btn {
            padding: 0.75rem 1.25rem;
            margin-right: 0.5rem;
            cursor: pointer;
            transition: background-color 0.2s, color 0.2s, border-color 0.2s;
            border-bottom: 3px solid transparent;
            display: inline-flex;
            align-items: center;
            font-size: 1rem;
            color: #4b5563; /* text-gray-600 */
        }
        .main-tab-btn.active {
            color: #2563EB; /* text-blue-600 */
            border-bottom-color: #2563EB; /* border-blue-600 */
            font-weight: 600;
        }
        .main-tab-content {
            display: none;
        }
        .main-tab-content.active {
            display: block;
        }

        /* Element Plus Dialog 层级确保 */
        .el-overlay { z-index: 2000 !important; }
        .el-dialog { z-index: 2001 !important; }

       /* Tree Select 样式 */
        .el-tree-select { width: 100% !important; }
        .el-select-dropdown__wrap { max-height: 400px; }
        .el-tree-node__content { height: 32px; }
        .el-tree-node__label { font-size: 14px; }

        /* 状态标签样式 (从 personnel_list.html 引入) */
        .status-badge { padding: 0.25rem 0.5rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 600; display: inline-block; }
        .status-badge.active, .status-badge.normal, .status-badge.available { background-color: #DEF7EC; color: #03543E; } /* 绿色 - 正常/可用 */
        .status-badge.leave, .status-badge.inactive, .status-badge.unavailable { background-color: #FEF3C7; color: #92400E; } /* 黄色 - 停用/不可用 */
        .status-badge.retired { background-color: #FEE2E2; color: #991B1B; } /* 红色 - 退休/报废等 */
        .status-badge.on_mission { background-color: #E0E7FF; color: #3730A3; } /* 蓝色 - 任务中 */
    </style>
</head>
<body class="bg-gray-100 min-h-screen">

    <!-- Navbar Start -->
    <header class="top-nav">
        <div class="system-title">
            <strong>广西公路水路安全畅通与应急处置系统</strong>
        </div>
        <nav class="tab-navigation">
            <a href="new_index.html" class="tab-button">风险一张图</a>
            <a href="my_check_tasks.html" class="tab-button">风险隐患管理</a>
            <a href="plan_list.html" class="tab-button active">应急处置</a>
            <button class="tab-button" onclick="alert('应急演练功能暂未开放');">应急演练</button>
        </nav>
    </header>
    <!-- Navbar End -->

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container h-full" style="display: flex;">
        <!-- Sidebar Placeholder (will be populated by loadComponents.js) -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area -->
        <main id="main-content" class="flex-grow pb-8 px-6 bg-gray-100">
            <div class="py-6">
                <!-- 页面标题 -->
                <div class="mb-6">
                    <h2 class="text-3xl font-bold text-gray-800">应急通讯录</h2>
                    <p class="text-gray-600 mt-1">管理单位内部人员、外部专家及第三方救援队伍联系信息</p>
                </div>

                <!-- Global Filters -->
                <div id="global-filters-app" class="bg-white p-4 rounded-lg shadow-sm mb-6">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">通用筛选</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-4 items-end">
                        <div>
                            <label for="global_city_filter_select" class="block text-sm font-medium text-gray-700 mb-1">广西地市</label>
                            <el-select v-model="selectedCity" placeholder="请选择地市" clearable class="block w-full" @change="handleCityChange" id="global_city_filter_select">
                                <el-option v-for="city in cityOptions" :key="city.value" :label="city.label" :value="city.value"></el-option>
                            </el-select>
                        </div>
                        <div>
                            <label for="global_org_unit_filter_tree_select" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                            <el-tree-select
                                v-model="selectedOrgUnit"
                                :data="filteredOrgUnitOptions"
                                :props="{ value: 'value', label: 'label', children: 'children' }"
                                placeholder="请选择单位"
                                clearable
                                check-strictly
                                class="block w-full"
                                @change="handleOrgChange"
                                id="global_org_unit_filter_tree_select"
                            />
                        </div>
                        <div class="flex space-x-2">
                            <button @click="applyGlobalFilters" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm w-full sm:w-auto flex-grow sm:flex-grow-0 flex items-center justify-center">
                              <i class="fas fa-filter mr-2"></i> 应用筛选
                            </button>
                            <button @click="resetGlobalFilters" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm w-full sm:w-auto flex-grow sm:flex-grow-0 flex items-center justify-center">
                              <i class="fas fa-undo mr-2"></i> 重置筛选
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Main Tab 导航 -->
                <div class="mb-6 border-b border-gray-300">
                    <nav class="-mb-px flex space-x-2" aria-label="MainContactsTabs">
                        <button class="main-tab-btn active" data-main-tab="personnel-section">
                            <i class="fas fa-users mr-2"></i>单位人员
                        </button>
                        <button class="main-tab-btn" data-main-tab="expert-section">
                            <i class="fas fa-user-tie mr-2"></i>专家库
                        </button>
                        <button class="main-tab-btn" data-main-tab="third-party-section">
                            <i class="fas fa-handshake mr-2"></i>救援队伍
                        </button>
                    </nav>
                </div>

                <!-- Main Tab 内容区域 -->
                <div id="main-tab-content-container">
                    <!-- 单位人员内容 -->
                    <div id="personnel-section" class="main-tab-content active">
                        <!-- Content for Personnel List will be added here -->
                                                <!-- 页面标题 -->
                                                <div class="flex justify-between items-center mb-6 pt-4"> <!-- Added pt-4 -->
                                                    <button id="btnAddPersonnel" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                      <i class="fas fa-plus mr-2"></i>添加人员
                                                    </button>
                                                  </div>

                                                  <!-- 过滤栏 -->
                                                  <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                                                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                                      <div>
                                                        <label for="personnel_position_filter" class="block text-sm font-medium text-gray-700 mb-1">职位</label>
                                                        <select id="personnel_position_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                          <option value="">全部职位</option>
                                                          <option value="director">主任</option>
                                                          <option value="deputy">副主任</option>
                                                          <option value="chief">科长</option>
                                                          <option value="staff">工作人员</option>
                                                          <!-- Add more positions -->
                                                        </select>
                                                      </div>
                                                      <div>
                                                        <label for="personnel_status_filter" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                                                        <select id="personnel_status_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                          <option value="">全部状态</option>
                                                          <option value="active">正常</option>
                                                          <option value="leave">停用</option>
                                                          <!-- Add other statuses if needed -->
                                                        </select>
                                                      </div>
                                                      <div class="flex items-end space-x-2">
                                                        <button id="btnFilterPersonnel" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                          <i class="fas fa-filter mr-1"></i> 筛选
                                                        </button>
                                                        <button id="btnResetFilterPersonnel" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                                          <i class="fas fa-undo mr-1"></i> 重置
                                                        </button>
                                                      </div>
                                                    </div>
                                                  </div>

                                                  <!-- 数据表格 -->
                                                  <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                                                    <div class="overflow-x-auto">
                                                      <table class="min-w-full divide-y divide-gray-200">
                                                        <thead class="bg-gray-50">
                                                          <tr>
                                                            <th scope="col" class="w-16 px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                                            <th scope="col" class="w-16 px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">性别</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职位</th>
                                                            <th scope="col" class="w-40 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                                                            <th scope="col" class="w-24 px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                                            <th scope="col" class="w-32 px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                                          </tr>
                                                        </thead>
                                                        <tbody class="bg-white divide-y divide-gray-200">
                                                          <tr>
                                                            <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">1</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李明</td>
                                                            <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center">男</td>
                                                            <td class="px-6 py-4 text-sm text-gray-900">广西壮族自治区交通运输厅</td>
                                                            <td class="px-6 py-4 text-sm text-gray-900">安监科</td>
                                                            <td class="px-6 py-4 text-sm text-gray-900">科长</td>
                                                            <td class="w-40 px-6 py-4 text-sm text-gray-900">*********01</td>
                                                            <td class="w-24 px-6 py-4 whitespace-nowrap text-center">
                                                               <span class="status-badge active">正常</span>
                                                            </td>
                                                            <td class="w-32 px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                              <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit-personnel" data-id="1">
                                                                <i class="fas fa-edit"></i> 编辑
                                                              </button>
                                                              <button class="text-red-600 hover:text-red-900 btn-delete-personnel" data-id="1">
                                                                <i class="fas fa-trash-alt"></i> 删除
                                                              </button>
                                                            </td>
                                                          </tr>
                                                          <tr>
                                                            <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">2</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王芳</td>
                                                            <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center">女</td>
                                                            <td class="px-6 py-4 text-sm text-gray-900">广西壮族自治区交通运输厅</td>
                                                            <td class="px-6 py-4 text-sm text-gray-900">安监科</td>
                                                            <td class="px-6 py-4 text-sm text-gray-900">科员</td>
                                                            <td class="w-40 px-6 py-4 text-sm text-gray-900">**********2</td>
                                                             <td class="w-24 px-6 py-4 whitespace-nowrap text-center">
                                                               <span class="status-badge leave">停用</span>
                                                            </td>
                                                            <td class="w-32 px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                              <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit-personnel" data-id="2">
                                                                <i class="fas fa-edit"></i> 编辑
                                                              </button>
                                                              <button class="text-red-600 hover:text-red-900 btn-delete-personnel" data-id="2">
                                                                <i class="fas fa-trash-alt"></i> 删除
                                                              </button>
                                                            </td>
                                                          </tr>
                                                          <!-- More rows -->
                                                        </tbody>
                                                      </table>
                                                    </div>

                                                    <!-- 分页控件 -->
                                                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                                                         <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                                            <div>
                                                                <p class="text-sm text-gray-700">
                                                                    显示第 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条记录
                                                                </p>
                                                            </div>
                                                            <div>
                                                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"><span class="sr-only">上一页</span><i class="fas fa-chevron-left"></i></a>
                                                                    <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                                                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"><span class="sr-only">下一页</span><i class="fas fa-chevron-right"></i></a>
                                                                </nav>
                                                            </div>
                                                        </div>
                                                    </div>
                                                  </div>

                                                  <!-- 添加/编辑人员 Modal -->
                                                  <div id="personnel-modal-app"> <!-- MODIFIED ID -->
                                                      <el-dialog
                                                          v-model="dialogVisible"
                                                          :title="isEditMode ? '编辑人员' : '新增人员'"
                                                          width="60%"
                                                          @closed="resetForm"
                                                          :close-on-click-modal="false"
                                                      >
                                                        <el-form :model="personnelForm" ref="personnelFormRef" label-width="100px">
                                                            <el-row :gutter="20">
                                                                <el-col :span="12">
                                                                    <el-form-item label="姓名" required prop="name">
                                                                        <el-input v-model="personnelForm.name" placeholder="请输入姓名"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="12">
                                                                    <el-form-item label="性别" required prop="gender">
                                                                        <el-radio-group v-model="personnelForm.gender">
                                                                            <el-radio label="male">男</el-radio>
                                                                            <el-radio label="female">女</el-radio>
                                                                        </el-radio-group>
                                                                    </el-form-item>
                                                                </el-col>
                                                            </el-row>
                                                            <el-form-item label="所属单位" required prop="orgUnit">
                                                                <el-tree-select
                                                                    v-model="personnelForm.orgUnit"
                                                                    :data="orgOptions"
                                                                    :multiple="false"
                                                                    :check-strictly="true"
                                                                    placeholder="请选择所属单位"
                                                                    clearable
                                                                    style="width: 100%;"
                                                                />
                                                            </el-form-item>
                                                            <el-row :gutter="20">
                                                                <el-col :span="12">
                                                                    <el-form-item label="部门" prop="department">
                                                                        <el-input v-model="personnelForm.department" placeholder="请输入部门"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="12">
                                                                    <el-form-item label="职位" prop="position">
                                                                        <el-input v-model="personnelForm.position" placeholder="请输入职位"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                            </el-row>
                                                            <el-row :gutter="20">
                                                                <el-col :span="12">
                                                                    <el-form-item label="联系电话" required prop="phone">
                                                                        <el-input v-model="personnelForm.phone" placeholder="请输入联系电话"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="12">
                                                                     <el-form-item label="状态" required prop="status">
                                                                          <el-select v-model="personnelForm.status" placeholder="请选择状态" style="width: 100%;">
                                                                              <el-option label="正常" value="active"></el-option>
                                                                              <el-option label="停用" value="leave"></el-option>
                                                                          </el-select>
                                                                     </el-form-item>
                                                                </el-col>
                                                            </el-row>
                                                        </el-form>
                                                        <template #footer>
                                                            <span class="dialog-footer">
                                                                <el-button @click="closeModal">取消</el-button>
                                                                <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                                                            </span>
                                                        </template>
                                                      </el-dialog>
                                                  </div>
                    </div>

                    <!-- 专家库内容 -->
                    <div id="expert-section" class="main-tab-content">
                        <!-- Content for Expert List will be added here -->
                                                <!-- 页面标题 -->
                                                <div class="flex justify-between items-center mb-6 pt-4"> <!-- Added pt-4 -->
                                                    <button id="btnAddExpert" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                      <i class="fas fa-plus mr-2"></i>添加专家
                                                    </button>
                                                  </div>

                                                  <!-- 过滤栏 -->
                                                  <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                                                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                                                      <div>
                                                        <label for="expert_expertise_filter" class="block text-sm font-medium text-gray-700 mb-1">专业领域</label>
                                                        <select id="expert_expertise_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                          <option value="">全部领域</option>
                                                          <option value="fire">消防救援</option>
                                                          <option value="medical">医疗救护</option>
                                                          <option value="chemical">危化品处置</option>
                                                          <option value="geology">地质灾害</option>
                                                          <option value="flood">洪涝灾害</option>
                                                          <!-- Add more domains -->
                                                        </select>
                                                      </div>
                                                      <div>
                                                        <label for="expert_level_filter" class="block text-sm font-medium text-gray-700 mb-1">专家级别</label>
                                                        <select id="expert_level_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                          <option value="">全部级别</option>
                                                          <option value="national">国家级</option>
                                                          <option value="provincial">省级</option>
                                                          <option value="city">市级</option>
                                                          <option value="county">县级</option>
                                                          <!-- Add more levels -->
                                                        </select>
                                                      </div>
                                                      <div>
                                                        <label for="expert_status_filter" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                                                        <select id="expert_status_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                          <option value="">全部状态</option>
                                                          <option value="normal">正常</option>
                                                          <option value="inactive">停用</option>
                                                        </select>
                                                      </div>
                                                      <div class="flex items-end space-x-2">
                                                        <button id="btnFilterExperts" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                          <i class="fas fa-filter mr-1"></i> 筛选
                                                        </button>
                                                        <button id="btnResetExpertFilters" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                                          <i class="fas fa-undo mr-1"></i> 重置
                                                        </button>
                                                      </div>
                                                    </div>
                                                  </div>

                                                  <!-- 数据表格 -->
                                                  <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                                                    <div class="overflow-x-auto">
                                                      <table class="min-w-full divide-y divide-gray-200">
                                                        <thead class="bg-gray-50">
                                                          <tr>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">性别</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专业领域</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专家级别</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系电话</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最近一次确认时间</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                                          </tr>
                                                        </thead>
                                                        <tbody class="bg-white divide-y divide-gray-200">
                                                          <tr>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">刘建国</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">男</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">省消防总队</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">消防救援</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">省级</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">*********01</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-26</td>
                                                            <td class="px-6 py-4 whitespace-nowrap">
                                                              <span class="status-badge normal">正常</span>
                                                            </td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium"> <!-- Changed text-right to text-center -->
                                                              <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit-expert" data-id="1"> <!-- MODIFIED CLASS -->
                                                                <i class="fas fa-edit"></i> 编辑
                                                              </button>
                                                              <button class="text-red-600 hover:text-red-900 btn-delete-expert" data-id="1"> <!-- MODIFIED CLASS -->
                                                                <i class="fas fa-trash-alt"></i> 删除
                                                              </button>
                                                            </td>
                                                          </tr>
                                                           <tr>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">张美华</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">女</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市第一人民医院</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">医疗救护</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">国家级</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">**********2</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-06-15</td>
                                                            <td class="px-6 py-4 whitespace-nowrap">
                                                              <span class="status-badge inactive">停用</span> <!-- Example changed status -->
                                                            </td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                                              <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit-expert" data-id="2">
                                                                <i class="fas fa-edit"></i> 编辑
                                                              </button>
                                                              <button class="text-red-600 hover:text-red-900 btn-delete-expert" data-id="2">
                                                                <i class="fas fa-trash-alt"></i> 删除
                                                              </button>
                                                            </td>
                                                          </tr>
                                                          <!-- More expert rows -->
                                                        </tbody>
                                                      </table>
                                                    </div>

                                                    <!-- 分页控件 -->
                                                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                                                         <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                                            <div>
                                                                <p class="text-sm text-gray-700">
                                                                    显示第 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条记录
                                                                </p>
                                                            </div>
                                                            <div>
                                                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                                                     <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"><span class="sr-only">上一页</span><i class="fas fa-chevron-left"></i></a>
                                                                     <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                                                                     <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"><span class="sr-only">下一页</span><i class="fas fa-chevron-right"></i></a>
                                                                </nav>
                                                            </div>
                                                        </div>
                                                    </div>
                                                  </div>

                                                  <!-- 添加/编辑专家 Modal -->
                                                   <!-- Note: Original file used ID "addPersonnelModal", changed to "expert-modal-app" -->
                                                  <div id="expert-modal-app"> <!-- MODIFIED ID -->
                                                      <el-dialog
                                                          v-model="dialogVisible"
                                                          :title="modalTitle"
                                                          width="60%"
                                                          @closed="resetForm"
                                                          :close-on-click-modal="false"
                                                      >
                                                          <el-form :model="expertForm" ref="expertFormRef" label-width="100px">
                                                                <el-row :gutter="20">
                                                                    <el-col :span="12">
                                                                        <el-form-item label="姓名" required prop="name">
                                                                            <el-input v-model="expertForm.name" placeholder="请输入专家姓名"></el-input>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                    <el-col :span="12">
                                                                        <el-form-item label="性别" required prop="gender">
                                                                            <el-radio-group v-model="expertForm.gender">
                                                                                <el-radio label="male">男</el-radio>
                                                                                <el-radio label="female">女</el-radio>
                                                                            </el-radio-group>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                </el-row>
                                                                <el-form-item label="所属单位" required prop="organizationId">
                                                                  <el-tree-select
                                                                        v-model="expertForm.organizationId"
                                                                      :data="orgOptions"
                                                                        :multiple="false"
                                                                        check-strictly
                                                                      :props="{ value: 'value', label: 'label', children: 'children' }"
                                                                        placeholder="请选择所属单位"
                                                                        class="w-full"
                                                                        style="width: 100%;"
                                                                    />
                                                                </el-form-item>
                                                                <el-row :gutter="20">
                                                                    <el-col :span="12">
                                                                        <el-form-item label="专业领域" required prop="expertise">
                                                                            <el-select v-model="expertForm.expertise" placeholder="请选择专业领域" style="width: 100%;">
                                                                                <el-option label="消防救援" value="fire"></el-option>
                                                                                <el-option label="医疗救护" value="medical"></el-option>
                                                                                <el-option label="危化品处置" value="chemical"></el-option>
                                                                                <el-option label="地质灾害" value="geology"></el-option>
                                                                                <el-option label="洪涝灾害" value="flood"></el-option>
                                                                                <el-option label="其他" value="other"></el-option>
                                                                            </el-select>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                    <el-col :span="12">
                                                                        <el-form-item label="级别" required prop="level">
                                                                            <el-select v-model="expertForm.level" placeholder="请选择专家级别" style="width: 100%;">
                                                                                <el-option label="国家级" value="national"></el-option>
                                                                                <el-option label="省级" value="provincial"></el-option>
                                                                                <el-option label="市级" value="municipal"></el-option> <!-- Changed from city -->
                                                                                <el-option label="县级" value="county"></el-option>
                                                                            </el-select>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                </el-row>
                                                                <el-row :gutter="20">
                                                                    <el-col :span="12">
                                                                        <el-form-item label="联系电话" required prop="phone">
                                                                            <el-input v-model="expertForm.phone" placeholder="请输入联系电话"></el-input>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                    <el-col :span="12">
                                                                        <el-form-item label="状态" required prop="status">
                                                                            <el-select v-model="expertForm.status" placeholder="请选择状态" style="width: 100%;">
                                                                                <el-option label="正常" value="normal"></el-option>
                                                                                <el-option label="停用" value="inactive"></el-option>
                                                                            </el-select>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                </el-row>
                                                            </el-form>
                                                            <template #footer>
                                                                <span class="dialog-footer">
                                                                    <el-button @click="closeModal">取消</el-button>
                                                                    <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                                                                </span>
                                                            </template>
                                                      </el-dialog>
                                                  </div>

                                                  <!-- 删除确认模态框 -->
                                                   <!-- Note: Original file used ID "deleteConfirmModal", changed to "expert-delete-confirm-modal" -->
                                                  <div id="expert-delete-confirm-modal" class="fixed inset-0 z-[100] hidden"> <!-- MODIFIED ID -->
                                                      <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
                                                      <div class="fixed inset-0 z-10 overflow-y-auto">
                                                          <div class="flex min-h-full items-center justify-center p-4">
                                                              <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                                                                  <div class="px-6 py-4 border-b border-gray-200">
                                                                      <h3 class="text-lg font-semibold text-gray-800">确认删除</h3>
                                                                  </div>
                                                                  <div class="px-6 py-4">
                                                                      <p class="text-sm text-gray-700">您确定要删除该专家信息吗？此操作无法撤销。</p>
                                                                  </div>
                                                                  <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                                                                      <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-expert-delete-modal"> <!-- MODIFIED CLASS -->
                                                                          取消
                                                                      </button>
                                                                      <button id="btnConfirmDeleteExpert" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"> <!-- MODIFIED ID -->
                                                                          删除
                                                                      </button>
                                                                  </div>
                                                              </div>
                                                          </div>
                                                      </div>
                                                  </div>
                    </div>

                    <!-- 救援队伍内容 -->
                    <div id="third-party-section" class="main-tab-content">
                        <!-- Content for Third Party List will be added here -->
                                                <!-- 页面标题 -->
                                                <div class="flex justify-between items-center mb-6 pt-4"> <!-- Added pt-4 -->
                                                    <button id="btnAddThirdParty" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                      <i class="fas fa-plus mr-2"></i>添加应急救援人员
                                                    </button>
                                                  </div>

                                                  <!-- 过滤栏 -->
                                                  <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                                                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                                                      <div>
                                                        <label for="third_party_filterName" class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                                                        <input type="text" id="third_party_filterName" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="输入姓名关键词">
                                                      </div>
                                                      <div>
                                                        <label for="third_party_filterCompany" class="block text-sm font-medium text-gray-700 mb-1">所属三方公司</label>
                                                        <select id="third_party_filterCompany" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                          <option value="">全部公司</option>
                                                          <option value="companyA">XX消防技术服务公司</option>
                                                          <option value="companyB">XX医疗急救中心</option>
                                                          <option value="companyC">XX电力工程公司</option>
                                                          <option value="companyD">XX运输有限公司</option>
                                                          <option value="companyE">XX环境监测评估公司</option>
                                                        </select>
                                                      </div>
                                                      <div>
                                                        <label for="personnel_status_filter" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                                                        <select id="personnel_status_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                          <option value="">全部状态</option>
                                                          <option value="active">正常</option>
                                                          <option value="leave">停用</option>
                                                          <!-- Add other statuses if needed -->
                                                        </select>
                                                      </div>
                                                      <div class="flex items-end space-x-2 col-start-5"> <!-- Adjusted col-start -->
                                                        <button id="btnFilterThirdParty" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                          <i class="fas fa-search mr-1"></i> 查询
                                                        </button>
                                                        <button id="btnResetThirdPartyFilter" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                                          <i class="fas fa-undo mr-1"></i> 重置
                                                        </button>
                                                      </div>
                                                    </div>
                                                  </div>

                                                  <!-- 数据表格 -->
                                                  <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                                                    <div class="overflow-x-auto">
                                                      <table class="min-w-full divide-y divide-gray-200">
                                                        <thead class="bg-gray-50">
                                                          <tr>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属三方公司</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职务</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系电话</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">技能特长</th>
                                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                                          </tr>
                                                        </thead>
                                                        <tbody class="bg-white divide-y divide-gray-200">
                                                          <tr>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">王军</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">广西壮族自治区交通运输厅</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">XX消防技术服务公司</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">现场负责人</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">*********01</td>
                                                            <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs" title="灭火救援, 现场指挥">灭火救援, 现场指挥</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-center"> <span class="status-badge available">可用</span></td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                                              <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit-third-party" data-id="1"> <!-- MODIFIED CLASS -->
                                                                <i class="fas fa-edit"></i> 编辑
                                                              </button>
                                                              <button class="text-red-600 hover:text-red-900 btn-delete-third-party" data-id="1"> <!-- MODIFIED CLASS -->
                                                                <i class="fas fa-trash-alt"></i> 删除
                                                              </button>
                                                            </td>
                                                          </tr>
                                                          <tr>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">李红</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">自治区公路发展中心</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">XX医疗急救中心</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">医护队长</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">**********2</td>
                                                            <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs" title="急救处理, 包扎">急救处理, 包扎</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-center"> <span class="status-badge on_mission">任务中</span></td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                                              <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit-third-party" data-id="2">
                                                                <i class="fas fa-edit"></i> 编辑
                                                              </button>
                                                              <button class="text-red-600 hover:text-red-900 btn-delete-third-party" data-id="2">
                                                                <i class="fas fa-trash-alt"></i> 删除
                                                              </button>
                                                            </td>
                                                          </tr>
                                                          <!-- More rows -->
                                                        </tbody>
                                                      </table>
                                                    </div>

                                                    <!-- 分页控件 -->
                                                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                                                         <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                                            <div>
                                                                <p class="text-sm text-gray-700">
                                                                    显示第 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条记录
                                                                </p>
                                                            </div>
                                                            <div>
                                                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"><span class="sr-only">上一页</span><i class="fas fa-chevron-left"></i></a>
                                                                    <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                                                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"><span class="sr-only">下一页</span><i class="fas fa-chevron-right"></i></a>
                                                                </nav>
                                                            </div>
                                                        </div>
                                                    </div>
                                                  </div>

                                                   <!-- 添加/编辑三方人员模态框 -->
                                                   <!-- Note: Original file used ID "addThirdPartyModal", changed to "third-party-modal-app" -->
                                                  <div id="third-party-modal-app"> <!-- MODIFIED ID -->
                                                      <el-dialog
                                                          v-model="dialogVisible"
                                                          :title="modalTitle"
                                                          width="60%"
                                                          @closed="resetForm"
                                                          :close-on-click-modal="false"
                                                      >
                                                          <el-form :model="thirdPartyForm" ref="thirdPartyFormRef" label-width="120px">
                                                                <el-row :gutter="20">
                                                                    <el-col :span="12">
                                                                        <el-form-item label="姓名" required prop="name">
                                                                            <el-input v-model="thirdPartyForm.name" placeholder="请输入人员姓名"></el-input>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                    <el-col :span="12">
                                                                        <el-form-item label="性别" required prop="gender">
                                                                            <el-radio-group v-model="thirdPartyForm.gender">
                                                                                <el-radio label="male">男</el-radio>
                                                                                <el-radio label="female">女</el-radio>
                                                                            </el-radio-group>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                </el-row>
                                                                <el-row :gutter="20">
                                                                    <el-col :span="12">
                                                                        <el-form-item label="所属单位" prop="organizationId">
                                                                            <el-tree-select
                                                                                v-model="thirdPartyForm.organizationId"
                                                                                :data="orgOptions"
                                                                                :multiple="false"
                                                                                check-strictly
                                                                                placeholder="请选择所属单位 (非必填)"
                                                                                class="w-full"
                                                                                style="width: 100%;"
                                                                                clearable
                                                                            />
                                                                        </el-form-item>
                                                                    </el-col>
                                                                    <el-col :span="12">
                                                                        <el-form-item label="所属三方公司" required prop="company">
                                                                            <el-select v-model="thirdPartyForm.company" placeholder="请选择所属公司" style="width: 100%;">
                                                                                <el-option label="XX消防技术服务公司" value="companyA"></el-option>
                                                                                <el-option label="XX医疗急救中心" value="companyB"></el-option>
                                                                                <el-option label="XX电力工程公司" value="companyC"></el-option>
                                                                                <el-option label="XX运输有限公司" value="companyD"></el-option>
                                                                                <el-option label="XX环境监测评估公司" value="companyE"></el-option>
                                                                                <el-option label="其他" value="other"></el-option>
                                                                            </el-select>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                </el-row>
                                                                <el-row :gutter="20">
                                                                    <el-col :span="12">
                                                                        <el-form-item label="职务/岗位" prop="position">
                                                                            <el-input v-model="thirdPartyForm.position" placeholder="请输入职务或岗位"></el-input>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                    <el-col :span="12">
                                                                        <el-form-item label="联系电话" required prop="phone">
                                                                            <el-input v-model="thirdPartyForm.phone" placeholder="请输入联系电话"></el-input>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                </el-row>
                                                                 <el-form-item label="技能特长" prop="skills">
                                                                     <el-input type="textarea" :rows="3" v-model="thirdPartyForm.skills" placeholder="请输入人员的技能或特长，多个请用逗号分隔"></el-input>
                                                                 </el-form-item>
                                                                 <el-form-item label="状态" required prop="status">
                                                                     <el-select v-model="thirdPartyForm.status" placeholder="请选择状态" style="width: 100%;">
                                                                         <el-option label="可用" value="available"></el-option>
                                                                         <el-option label="任务中" value="on_mission"></el-option>
                                                                         <el-option label="不可用" value="unavailable"></el-option>
                                                                     </el-select>
                                                                 </el-form-item>
                                                            </el-form>
                                                            <template #footer>
                                                                <span class="dialog-footer">
                                                                    <el-button @click="closeModal">取消</el-button>
                                                                    <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                                                                </span>
                                                            </template>
                                                      </el-dialog>
                                                  </div>

                                                  <!-- 删除确认模态框 -->
                                                  <!-- Note: Original file used ID "deleteConfirmModal", changed to "third-party-delete-confirm-modal" -->
                                                  <div id="third-party-delete-confirm-modal" class="fixed inset-0 z-[100] hidden"> <!-- MODIFIED ID -->
                                                      <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
                                                      <div class="fixed inset-0 z-10 overflow-y-auto">
                                                          <div class="flex min-h-full items-center justify-center p-4">
                                                              <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                                                                  <div class="px-6 py-4 border-b border-gray-200">
                                                                      <h3 class="text-lg font-semibold text-gray-800">确认删除</h3>
                                                                  </div>
                                                                  <div class="px-6 py-4">
                                                                      <p class="text-sm text-gray-700">您确定要删除该应急救援人员信息吗？此操作无法撤销。</p>
                                                                  </div>
                                                                  <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                                                                      <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-third-party-delete-modal"> <!-- MODIFIED CLASS -->
                                                                          取消
                                                                      </button>
                                                                      <button id="btnConfirmDeleteThirdParty" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"> <!-- MODIFIED ID -->
                                                                          删除
                                                                      </button>
                                                                  </div>
                                                              </div>
                                                          </div>
                                                      </div>
                                                  </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入 Vue 和 Element Plus -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <script src="https://unpkg.com/element-plus"></script>

    <!-- 全局 standardUnitOptions 定义 -->
    <script>
        // 注意：此处的组织树选项需要整合 personnel_list, expert_list, third_party_list 中可能涉及的所有单位
        const standardUnitOptions = [
            { value: '1', label: '广西壮族自治区交通运输厅', children: [
                { value: '1.1', label: '直属事业单位及专项机构', children: [
                    { value: '1.1.1', label: '自治区公路发展中心' },
                    { value: '1.1.2', label: '自治区高速公路发展中心' },
                    { value: '1.1.3', label: '自治区道路运输发展中心' }
                ]},
                { value: '1.2', label: '市级交通运输局', children: [
                    { value: '1.2.1', label: '钦州市交通运输局' },
                    { value: '1.2.2', label: '南宁市交通运输局' },
                    { value: '1.2.3', label: '玉林市交通运输局' }
                ]},
                 { value: '1.3', label: '厅机关处室', children: [ // 假设有内部处室
                    { value: '1.3.1', label: '安监科' },
                    { value: '1.3.2', label: '办公室' }
                ]}
            ]},
             // 从 expert_list 可能引入的单位
             { value: '2', label: '省消防总队' }, // 省级单位示例
             { value: '3', label: '市第一人民医院' }, // 医疗机构示例
             { value: '4', label: '应急管理局危化处' }, // 其他政府部门示例
             { value: '5', label: '地质勘查院' }, // 科研院所示例
             { value: '6', label: '水利设计院' }, // 科研院所示例
             // 从 third_party_list 可能引入的单位/公司
             { value: 'SF001', label: 'XX消防技术服务公司' },
             { value: 'MED001', label: 'XX医疗急救中心' },
             { value: 'POW001', label: 'XX电力工程公司' },
             { value: 'TRA001', label: 'XX运输有限公司' },
             { value: 'ENV001', label: 'XX环境监测评估公司' }
             // ... 可能需要添加更多或调整结构
        ];
    </script>

    <!-- Global Filters Vue App -->
    <script>
        const GlobalFiltersApp = {
            data() {
                return {
                    selectedCity: null,
                    cityOptions: [],
                    selectedOrgUnit: null,
                    orgUnitOptions: standardUnitOptions, // Full tree for reference
                    filteredOrgUnitOptions: standardUnitOptions, // Tree for the el-tree-select
                };
            },
            methods: {
                populateCityOptions() {
                    const cityRoot = this.orgUnitOptions.find(opt => opt.value === '1'); // '广西壮族自治区交通运输厅'
                    if (cityRoot) {
                        const cityGroup = cityRoot.children.find(child => child.value === '1.2'); // '市级交通运输局'
                        if (cityGroup && cityGroup.children) {
                            this.cityOptions = cityGroup.children.map(city => ({ value: city.value, label: city.label, originalNode: city }));
                        }
                    }
                },
                handleCityChange(cityValue) {
                    console.log('全局 - 选中地市:', cityValue);
                    this.selectedOrgUnit = null; // Reset org unit when city changes
                    if (cityValue) {
                        const selectedCityOption = this.cityOptions.find(c => c.value === cityValue);
                        if (selectedCityOption && selectedCityOption.originalNode) {
                             // Create a new tree with only this city node (and its children if any)
                             // Ensure it's a deep copy to avoid modifying standardUnitOptions indirectly
                            this.filteredOrgUnitOptions = [JSON.parse(JSON.stringify(selectedCityOption.originalNode))];
                        } else {
                            this.filteredOrgUnitOptions = []; // Or some other default if city not found
                        }
                    } else {
                        this.filteredOrgUnitOptions = this.orgUnitOptions; // Show all if no city selected
                    }
                },
                handleOrgChange(orgValue) {
                    console.log('全局 - 选中单位:', orgValue);
                },
                applyGlobalFilters() {
                    console.log(`--- Global Apply Filters ---`);
                    console.log(`Global Filters: City=${this.selectedCity}, Org=${this.selectedOrgUnit}`);

                    const activeTabBtn = document.querySelector('.main-tab-btn.active');
                    if (!activeTabBtn) return;
                    const activeTabId = activeTabBtn.dataset.mainTab;

                    let specificFilters = {};
                    if (activeTabId === 'personnel-section') {
                        specificFilters.department = document.getElementById('personnel_department_filter')?.value;
                        specificFilters.position = document.getElementById('personnel_position_filter')?.value;
                        specificFilters.status = document.getElementById('personnel_status_filter')?.value;
                        console.log('Applying to Personnel with specific:', specificFilters);
                    } else if (activeTabId === 'expert-section') {
                        specificFilters.expertise = document.getElementById('expert_expertise_filter')?.value;
                        specificFilters.level = document.getElementById('expert_level_filter')?.value;
                        specificFilters.status = document.getElementById('expert_status_filter')?.value;
                        console.log('Applying to Experts with specific:', specificFilters);
                    } else if (activeTabId === 'third-party-section') {
                        specificFilters.name = document.getElementById('third_party_filterName')?.value;
                        specificFilters.company = document.getElementById('third_party_filterCompany')?.value;
                        console.log('Applying to Third Party with specific:', specificFilters);
                    }
                    ElementPlus.ElMessage.success('全局筛选已应用 (模拟)');
                },
                resetGlobalFilters() {
                    this.selectedCity = null;
                    this.selectedOrgUnit = null;
                    this.filteredOrgUnitOptions = this.orgUnitOptions; // Reset to full tree
                    console.log('重置全局筛选');

                    // Reset tab-specific filters
                    const personnelDept = document.getElementById('personnel_department_filter');
                    if(personnelDept) personnelDept.value = '';
                    const personnelPos = document.getElementById('personnel_position_filter');
                    if(personnelPos) personnelPos.value = '';
                    const personnelStatus = document.getElementById('personnel_status_filter');
                    if(personnelStatus) personnelStatus.value = '';

                    const expertExpertise = document.getElementById('expert_expertise_filter');
                    if(expertExpertise) expertExpertise.value = '';
                    const expertLevel = document.getElementById('expert_level_filter');
                    if(expertLevel) expertLevel.value = '';
                    const expertStatus = document.getElementById('expert_status_filter');
                    if(expertStatus) expertStatus.value = '';

                    const thirdPartyName = document.getElementById('third_party_filterName');
                    if(thirdPartyName) thirdPartyName.value = '';
                    const thirdPartyCompany = document.getElementById('third_party_filterCompany');
                    if(thirdPartyCompany) thirdPartyCompany.value = '';

                    console.log('所有特定筛选条件已重置');
                    ElementPlus.ElMessage.info('所有筛选已重置 (模拟)');
                }
            },
            mounted() {
                this.populateCityOptions();
            }
        };
    </script>

    <!-- 各模块脚本占位符 -->
    <script id="personnel-module-scripts">
                // ----- Personnel Module Scripts -----

        // Vue App for Org Filter
        const PersonnelOrgFilterAppDefinition = {
            data() {
                return {
                    selectedUnit: null,
                    orgOptions: standardUnitOptions // Use global data
                };
            },
            methods: {
                handleOrgChange(value) {
                    console.log('人员筛选 - 选中单位:', value);
                }
            }
        };
        const personnelOrgFilterApp = Vue.createApp(PersonnelOrgFilterAppDefinition);
        personnelOrgFilterApp.use(ElementPlus);
        personnelOrgFilterApp.mount('#personnel-org-filter-app'); // Mount to new ID

        // Vue App for Personnel Modal
        const PersonnelModalDefinition = {
            data() {
                return {
                    dialogVisible: false,
                    isEditMode: false,
                    personnelForm: {
                        id: null, name: '', gender: 'male', orgUnit: null,
                        department: '', position: '', phone: '', status: 'active'
                    },
                    orgOptions: standardUnitOptions // Use global data
                };
            },
            methods: {
                openModal(isEdit = false, personnelData = null) {
                    this.isEditMode = isEdit;
                    if (isEdit && personnelData) {
                         this.personnelForm = {
                             ...this.personnelForm, // Start with default structure
                             ...personnelData, // Override with provided data
                             orgUnit: personnelData.orgUnit // Ensure orgUnit is handled correctly
                         };
                    } else {
                        this.resetForm();
                    }
                    this.dialogVisible = true;
                },
                closeModal() { this.dialogVisible = false; },
                submitForm() {
                    // Simplified validation for integration
                    if (!this.personnelForm.name || !this.personnelForm.orgUnit || !this.personnelForm.phone) {
                         ElementPlus.ElMessage.error('请填写姓名、所属单位和联系电话!');
                         return;
                     }
                    console.log('人员模态框 - 表单提交:', this.personnelForm);
                    alert('人员信息已' + (this.isEditMode ? '保存' : '添加') + ' (模拟)');
                    this.closeModal();
                },
                resetForm() {
                    this.personnelForm = { id: null, name: '', gender: 'male', orgUnit: null, department: '', position: '', phone: '', status: 'active' };
                    // if (this.$refs.personnelFormRef) { this.$refs.personnelFormRef.clearValidate(); }
                }
            }
        };
        const personnelModalApp = Vue.createApp(PersonnelModalDefinition);
        personnelModalApp.use(ElementPlus);
        const personnelModalInstance = personnelModalApp.mount('#personnel-modal-app'); // Mount to new ID

        // Event Listeners for Personnel Tab buttons
        document.getElementById('personnel-section').addEventListener('click', function(event) {
            if (event.target.closest('#btnAddPersonnel')) {
                 personnelModalInstance.openModal(false);
            }

            const editButton = event.target.closest('.btn-edit-personnel');
            if (editButton) {
                const personnelId = editButton.dataset.id;
                console.log("编辑人员 ID:", personnelId);
                // Mock data should match the form structure
                const mockData = {
                     id: personnelId, name: `编辑-${personnelId}`, gender: 'female',
                     orgUnit: '1.1.1', // Example org ID
                     department: '技术部', position: '工程师',
                     phone: `138001380${personnelId}`, status: 'active'
                };
                personnelModalInstance.openModal(true, mockData);
            }

            const deleteButton = event.target.closest('.btn-delete-personnel');
            if (deleteButton) {
                const personnelId = deleteButton.dataset.id;
                if (confirm(`确定要删除人员 ID ${personnelId} 吗？`)) {
                    console.log("删除人员 ID:", personnelId);
                     alert('删除人员 ID: ' + personnelId + ' (模拟)');
                }
            }

            if (event.target.closest('#btnFilterPersonnel')) {
                const orgUnit = document.getElementById('personnel_orgUnit_filter').value; // Note: This gets value from underlying input, not the Vue model directly here
                const department = document.getElementById('personnel_department_filter').value;
                const position = document.getElementById('personnel_position_filter').value;
                const status = document.getElementById('personnel_status_filter').value;
                console.log('筛选人员:', { /* orgUnit: personnelOrgFilterApp.selectedUnit, */ department, position, status }); // Accessing Vue model (selectedUnit) directly is complex here
                alert('触发人员筛选 (模拟)');
            }
            if (event.target.closest('#btnResetFilterPersonnel')) {
                 // Reset select elements
                 document.getElementById('personnel_department_filter').value = '';
                 document.getElementById('personnel_position_filter').value = '';
                 document.getElementById('personnel_status_filter').value = '';
                 // Reset Vue TreeSelect (requires access via instance or different approach)
                 // personnelOrgFilterApp.selectedUnit = null; // Direct access might fail
                 console.log('重置人员筛选');
                 alert('人员筛选已重置 (模拟)');
            }
        });

    </script>
    <script id="expert-module-scripts">
                // ----- Expert Module Scripts -----

        // Vue App for Org Filter in search bar
        const ExpertOrgFilterAppDefinition = {
            data() {
                return {
                    selectedOrgs: [], // Experts allow multiple orgs in filter
                    orgOptions: standardUnitOptions // Use global data
                }
            },
            methods: {
                handleOrgChange(value) {
                    console.log('专家筛选 - 选中单位:', value);
                }
            }
        };
        const expertOrgFilterApp = Vue.createApp(ExpertOrgFilterAppDefinition);
        expertOrgFilterApp.use(ElementPlus);
        expertOrgFilterApp.mount('#expert-org-filter-app'); // Mount to new ID

        // Vue App for Expert Modal
        const ExpertModalDefinition = {
            data() {
                return {
                    dialogVisible: false,
                    isEditMode: false,
                    modalTitle: '添加专家',
                    expertForm: {
                        id: null, name: '', gender: 'male', organizationId: null, // Assuming single org for an expert
                        expertise: '', level: '', phone: '', status: 'normal'
                    },
                    orgOptions: standardUnitOptions // Use global data for modal too
                };
            },
            methods: {
                openModal(isEdit = false, expertData = null) {
                    this.isEditMode = isEdit;
                    this.modalTitle = isEdit ? '编辑专家' : '添加专家';
                    if (isEdit && expertData) {
                        this.expertForm = { ...this.expertForm, ...expertData };
                    } else {
                        this.resetForm();
                    }
                    this.dialogVisible = true;
                },
                closeModal() { this.dialogVisible = false; },
                submitForm() {
                    // Simplified validation
                     if (!this.expertForm.name || !this.expertForm.organizationId || !this.expertForm.expertise || !this.expertForm.level || !this.expertForm.phone) {
                         ElementPlus.ElMessage.error('请填写所有必填项!');
                         return;
                     }
                    console.log('专家模态框 - 表单提交:', this.expertForm);
                    alert('专家信息已' + (this.isEditMode ? '保存' : '添加') + ' (模拟)');
                    this.closeModal();
                },
                resetForm() {
                    this.expertForm = { id: null, name: '', gender: 'male', organizationId: null, expertise: '', level: '', phone: '', status: 'normal' };
                    // if (this.$refs.expertFormRef) { this.$refs.expertFormRef.clearValidate(); }
                }
            }
        };
        const expertModalApp = Vue.createApp(ExpertModalDefinition);
        expertModalApp.use(ElementPlus);
        const expertModalInstance = expertModalApp.mount('#expert-modal-app'); // Mount to new ID

        // Event Listeners for Expert Tab buttons
        const expertSection = document.getElementById('expert-section');
        const expertDeleteConfirmModal = document.getElementById('expert-delete-confirm-modal'); // Get delete confirm modal

        expertSection.addEventListener('click', function(event) {
            if (event.target.closest('#btnAddExpert')) {
                 expertModalInstance.openModal(false);
            }

            const editButton = event.target.closest('.btn-edit-expert'); // Use new class
            if (editButton) {
                const expertId = editButton.dataset.id;
                console.log("编辑专家 ID:", expertId);
                const mockData = {
                    id: expertId, name: `专家-${expertId}`, gender: 'female', organizationId: '3', // Example Org: 市第一人民医院
                    expertise: 'medical', level: 'national', phone: `**********${expertId}`, status: 'normal'
                };
                expertModalInstance.openModal(true, mockData);
            }

            const deleteButton = event.target.closest('.btn-delete-expert'); // Use new class
            if (deleteButton && expertDeleteConfirmModal) {
                const expertId = deleteButton.dataset.id;
                console.log('请求删除专家 ID:', expertId);
                expertDeleteConfirmModal.dataset.deleteId = expertId; // Store ID on the modal
                expertDeleteConfirmModal.classList.remove('hidden'); // Show the confirmation modal
            }

            if (event.target.closest('#btnFilterExperts')) {
                 const expertise = document.getElementById('expert_expertise_filter').value;
                 const level = document.getElementById('expert_level_filter').value;
                 const status = document.getElementById('expert_status_filter').value;
                 // const orgs = expertOrgFilterApp.selectedOrgs; // Accessing Vue model directly is tricky here
                 console.log('筛选专家:', { expertise, level, status /*, orgs */});
                 alert('触发专家筛选 (模拟)');
            }
            if (event.target.closest('#btnResetExpertFilters')) {
                 document.getElementById('expert_expertise_filter').value = '';
                 document.getElementById('expert_level_filter').value = '';
                 document.getElementById('expert_status_filter').value = '';
                 // Reset Vue TreeSelect (requires instance access or method)
                 // expertOrgFilterApp.selectedOrgs = []; // Direct access might fail
                 console.log('重置专家筛选');
                 alert('专家筛选已重置 (模拟)');
            }
        });

        // Listener for the delete confirmation modal's buttons
         if (expertDeleteConfirmModal) {
             expertDeleteConfirmModal.addEventListener('click', function(event) {
                 if (event.target.closest('.btn-close-expert-delete-modal')) { // Use new class
                     expertDeleteConfirmModal.classList.add('hidden');
                     delete expertDeleteConfirmModal.dataset.deleteId; // Clear stored ID
                 }
                 if (event.target.closest('#btnConfirmDeleteExpert')) { // Use new ID
                     const expertIdToDelete = expertDeleteConfirmModal.dataset.deleteId;
                     if (expertIdToDelete) {
                         console.log('确认删除专家 ID:', expertIdToDelete);
                         alert('删除专家 ID: ' + expertIdToDelete + ' (模拟)');
                         // TODO: Add actual delete API call
                         expertDeleteConfirmModal.classList.add('hidden');
                         delete expertDeleteConfirmModal.dataset.deleteId;
                         // Optionally refresh table
                     }
                 }
             });
         }

    </script>
    <script id="third-party-module-scripts">
               // ----- Third Party Module Scripts -----

        // Vue App for Org Filter (if needed, adjust or reuse logic)
        const ThirdPartyOrgFilterAppDefinition = {
            data() {
                return {
                    selectedUnit: null, // Or selectedOrgs: [] if multiple allowed
                    orgOptions: standardUnitOptions // Use global data
                };
            },
            methods: {
                handleOrgChange(value) {
                    console.log('救援队伍筛选 - 选中单位:', value);
                }
            }
        };
        const thirdPartyOrgFilterApp = Vue.createApp(ThirdPartyOrgFilterAppDefinition);
        thirdPartyOrgFilterApp.use(ElementPlus);
        // Mount only if the filter app element exists
        if (document.getElementById('third-party-org-filter-app')) {
             thirdPartyOrgFilterApp.mount('#third-party-org-filter-app'); // Mount to new ID
        }


        // Vue App for Third Party Modal
        const ThirdPartyModalDefinition = {
            data() {
                return {
                    dialogVisible: false,
                    isEditMode: false,
                    modalTitle: '添加应急救援人员',
                    thirdPartyForm: {
                        id: null, name: '', gender: 'male', company: '', position: '',
                        phone: '', status: 'available', skills: '', organizationId: null
                    },
                    orgOptions: standardUnitOptions // Use global data
                };
            },
            methods: {
                openModal(isEdit = false, personData = null) {
                    this.isEditMode = isEdit;
                    this.modalTitle = isEdit ? '编辑应急救援人员' : '添加应急救援人员';
                    if (isEdit && personData) {
                        this.thirdPartyForm = { ...this.thirdPartyForm, ...personData };
                    } else {
                        this.resetForm();
                    }
                    this.dialogVisible = true;
                },
                closeModal() { this.dialogVisible = false; },
                submitForm() {
                    // Simplified validation
                     if (!this.thirdPartyForm.name || !this.thirdPartyForm.company || !this.thirdPartyForm.phone) {
                         ElementPlus.ElMessage.error('请填写姓名、所属公司和联系电话!');
                         return;
                     }
                    console.log('救援队伍模态框 - 表单提交:', this.thirdPartyForm);
                    alert('救援人员信息已' + (this.isEditMode ? '保存' : '添加') + ' (模拟)');
                    this.closeModal();
                },
                resetForm() {
                    this.thirdPartyForm = { id: null, name: '', gender: 'male', company: '', position: '', phone: '', status: 'available', skills: '', organizationId: null };
                    // if (this.$refs.thirdPartyFormRef) { ... }
                }
            }
        };
        const thirdPartyModalApp = Vue.createApp(ThirdPartyModalDefinition);
        thirdPartyModalApp.use(ElementPlus);
        const thirdPartyModalInstance = thirdPartyModalApp.mount('#third-party-modal-app'); // Mount to new ID

        // Event Listeners for Third Party Tab buttons
        const thirdPartySection = document.getElementById('third-party-section');
        const thirdPartyDeleteConfirmModal = document.getElementById('third-party-delete-confirm-modal');

        thirdPartySection.addEventListener('click', function(event) {
            if (event.target.closest('#btnAddThirdParty')) {
                 thirdPartyModalInstance.openModal(false);
            }

            const editButton = event.target.closest('.btn-edit-third-party'); // Use new class
            if (editButton) {
                const personId = editButton.dataset.id;
                console.log("编辑救援人员 ID:", personId);
                const mockData = {
                     id: personId, name: `救援-${personId}`, gender: 'male',
                     organizationId: '1.1.2', // Example Org
                     company: 'companyC', position: '电工',
                     phone: `*********${personId}`, status: 'available',
                     skills: '电力抢修'
                 };
                thirdPartyModalInstance.openModal(true, mockData);
            }

            const deleteButton = event.target.closest('.btn-delete-third-party'); // Use new class
            if (deleteButton && thirdPartyDeleteConfirmModal) {
                const personId = deleteButton.dataset.id;
                 console.log('请求删除救援人员 ID:', personId);
                thirdPartyDeleteConfirmModal.dataset.deleteId = personId; // Store ID on the modal
                thirdPartyDeleteConfirmModal.classList.remove('hidden'); // Show the confirmation modal
            }

            if (event.target.closest('#btnFilterThirdParty')) {
                 const name = document.getElementById('third_party_filterName').value;
                 const company = document.getElementById('third_party_filterCompany').value;
                 // const orgUnit = thirdPartyOrgFilterApp.selectedUnit; // Accessing Vue model directly is tricky here
                 console.log('筛选救援队伍:', { name, company /*, orgUnit */ });
                 alert('触发救援队伍筛选 (模拟)');
            }
            if (event.target.closest('#btnResetThirdPartyFilter')) {
                 document.getElementById('third_party_filterName').value = '';
                 document.getElementById('third_party_filterCompany').value = '';
                 // Reset Vue TreeSelect (requires instance access or method)
                 // thirdPartyOrgFilterApp.selectedUnit = null;
                 console.log('重置救援队伍筛选');
                 alert('救援队伍筛选已重置 (模拟)');
            }
        });

        // Listener for the third party delete confirmation modal's buttons
         if (thirdPartyDeleteConfirmModal) {
             thirdPartyDeleteConfirmModal.addEventListener('click', function(event) {
                 if (event.target.closest('.btn-close-third-party-delete-modal')) { // Use new class
                     thirdPartyDeleteConfirmModal.classList.add('hidden');
                     delete thirdPartyDeleteConfirmModal.dataset.deleteId; // Clear stored ID
                 }
                 if (event.target.closest('#btnConfirmDeleteThirdParty')) { // Use new ID
                     const personIdToDelete = thirdPartyDeleteConfirmModal.dataset.deleteId;
                     if (personIdToDelete) {
                         console.log('确认删除救援人员 ID:', personIdToDelete);
                         alert('删除救援人员 ID: ' + personIdToDelete + ' (模拟)');
                         // TODO: Add actual delete API call
                         thirdPartyDeleteConfirmModal.classList.add('hidden');
                         delete thirdPartyDeleteConfirmModal.dataset.deleteId;
                         // Optionally refresh table
                     }
                 }
             });
         }

    </script>

    <!-- 主标签页切换逻辑 -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const mainTabButtons = document.querySelectorAll('.main-tab-btn');
            const mainTabContents = document.querySelectorAll('.main-tab-content');

            // Initialize Global Filters Vue App
            const globalFiltersVueAppInstance = Vue.createApp(GlobalFiltersApp).use(ElementPlus).mount('#global-filters-app');
            // window.globalFiltersVueApp = globalFiltersVueAppInstance; // Optional: if needed elsewhere non-Vue context

            // Initial state processing for tabs (no change here)
            let activeFound = false;
            mainTabContents.forEach(content => {
                if (content.classList.contains('active')) {
                    content.style.display = 'block';
                    activeFound = true;
                } else {
                    content.style.display = 'none';
                }
            });
            // 如果内容没有active，检查按钮
            if (!activeFound) {
                 mainTabButtons.forEach(btn => {
                    if (btn.classList.contains('active')) activeFound = true;
                 });
            }
             // 如果按钮和内容都没有active，默认激活第一个
            if (!activeFound && mainTabButtons.length > 0) {
                mainTabButtons[0].classList.add('active');
                const firstTabContentId = mainTabButtons[0].dataset.mainTab;
                const firstTabContent = document.getElementById(firstTabContentId);
                if (firstTabContent) {
                    firstTabContent.style.display = 'block';
                    firstTabContent.classList.add('active');
                }
            }


            mainTabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetMainTab = button.dataset.mainTab;

                    mainTabButtons.forEach(btn => { btn.classList.remove('active'); });
                    button.classList.add('active');

                    mainTabContents.forEach(content => {
                        content.style.display = 'none';
                        content.classList.remove('active');
                    });
                    const activeContent = document.getElementById(targetMainTab);
                    if (activeContent) {
                        activeContent.style.display = 'block';
                        activeContent.classList.add('active');
                    }
                });
            });
        });
    </script>

    <!-- 首先加载组件HTML定义 (导航栏, 侧边栏) -->
    <script src="js/navbarComponent.js"></script>
    <script src="js/sidebarComponent_emergency.js"></script>
    <!-- 然后加载注入组件和高亮链接的脚本 -->
    <script src="js/loadComponents.js"></script>
</body>
</html>
