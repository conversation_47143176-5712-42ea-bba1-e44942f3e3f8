<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风险一张图 - 广西交通运输应急管理系统</title>

    <!-- 引入公共样式 -->
    <link rel="stylesheet" href="css/emergency-common.css">
    <link rel="stylesheet" href="new_style.css">

    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 引入导航栏组件 -->
    <script src="js/navigation-component.js"></script>

    <style>
        /* 修复模态框表格白色背景问题 */
        .modal table td {
            background-color: #2c3e50 !important;
            color: #ecf0f1 !important;
        }

        .modal table tbody tr {
            background-color: #2c3e50 !important;
        }

        .modal table tbody tr:hover {
            background-color: #34495e !important;
        }

        /* 修复模态框表格白色背景问题 */
        .modal table td {
            background-color: #2c3e50 !important;
            color: #ecf0f1 !important;
        }

        .modal table tbody tr {
            background-color: #2c3e50 !important;
        }

        .modal table tbody tr:hover {
            background-color: #34495e !important;
        }

        /* 修复模态框中info-item的白色背景问题 */
        .modal .info-item {
            background-color: #2c3e50 !important;
            color: #ecf0f1 !important;
        }

        /* 确保模态框中的所有div都使用深色背景 */
        .modal div {
            background-color: #2c3e50 !important;
        }

        /* 但是保持表格头部的背景色 */
        .modal table thead tr {
            background-color: #34495e !important;
        }

        /* 保持模态框内容区域的背景色 */
        .modal .modal-content,
        .modal .modal-body,
        .modal .info-section > div {
            background-color: #2c3e50 !important;
        }

        /* 强制覆盖所有可能的白色背景 */
        .modal * {
            background-color: inherit !important;
        }

        /* 但是保持必要的背景色 */
        .modal .modal-content {
            background-color: #2c3e50 !important;
        }

        .modal .modal-header {
            background-color: #34495e !important;
        }

        .modal .modal-body {
            background-color: #2c3e50 !important;
            font-size: 18px !important;
        }

        /* 增大模态框整体字体大小 */
        .modal .info-item {
            font-size: 18px !important;
        }

        .modal .info-item strong {
            font-size: 18px !important;
        }

        .modal .info-item span {
            font-size: 18px !important;
        }

        .modal table td {
            font-size: 18px !important;
        }

        .modal table th {
            font-size: 18px !important;
        }

        /* 按钮样式优化 */
        .modal button {
            background-color: #3498db !important;
            color: white !important;
            border: none !important;
            padding: 10px 16px !important;
            border-radius: 5px !important;
            cursor: pointer !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            transition: background-color 0.3s ease !important;
        }

        .modal button:hover {
            background-color: #2980b9 !important;
        }

        /* 返回按钮特殊样式 */
        .modal button[onclick*="return"] {
            background-color: #6c757d !important;
        }

        .modal button[onclick*="return"]:hover {
            background-color: #5a6268 !important;
        }

        /* 查看风险详情按钮 */
        .modal button[onclick*="Risk"] {
            background-color: #e74c3c !important;
        }

        .modal button[onclick*="Risk"]:hover {
            background-color: #c0392b !important;
        }

        /* 查看物资详情按钮 */
        .modal button[onclick*="Supply"] {
            background-color: #3498db !important;
        }

        .modal button[onclick*="Supply"]:hover {
            background-color: #2980b9 !important;
        }

        /* 查看救援详情按钮 */
        .modal button[onclick*="Rescue"] {
            background-color: #28a745 !important;
        }

        .modal button[onclick*="Rescue"]:hover {
            background-color: #218838 !important;
        }

        /* 修改字段标签颜色 - 从蓝色改为更柔和的颜色 */
        .modal .info-item strong {
            color: #95a5a6 !important; /* 柔和的灰色 */
        }

        .modal h4 {
            color: #7f8c8d !important; /* 稍深一点的灰色用于标题 */
        }

        /* 也可以尝试其他颜色选项 */
        /*
        .modal .info-item strong {
            color: #e67e22 !important; // 橙色
            color: #27ae60 !important; // 绿色
            color: #8e44ad !important; // 紫色
            color: #f39c12 !important; // 金色
            color: #16a085 !important; // 青色
        }
        */
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏容器 -->
        <div id="navigation-container"></div>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="tab-content-container">
                <!-- 风险一张图内容 -->
                <div id="risk-map-content" class="tab-content" style="display: flex;">
                    <aside class="left-sidebar">
                        <div class="resource-filter-container">
                            <!-- 1. 资源类型选择器 -->
                            <div class="resource-type-selector">
                                <h4>资源类型</h4>
                                <div class="resource-type-item select-all-res-types">
                                    <input type="checkbox" id="res-type-all" name="resource-type-all" checked onclick="toggleAllMarkers()">
                                    <label for="res-type-all"><strong>全选/全不选</strong></label>
                                </div>

                                <div class="resource-type-tabs" style="position: relative;">
                                    <div style="display: flex; align-items: center;">
                                        <div style="position: relative; display: flex; align-items: center;">
                                            <button class="resource-tab-button active" onclick="showRiskHazards()">风险隐患点</button>
                                            <button onclick="showAllRiskMarkers()" title="修复风险点显示" style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); background-color: rgba(220, 53, 69, 0.2); border: none; border-radius: 50%; width: 16px; height: 16px; display: flex; align-items: center; justify-content: center; cursor: pointer; padding: 0;">
                                                <i class="fas fa-exclamation-triangle" style="font-size: 8px; color: white;"></i>
                                            </button>
                                        </div>
                                        <div style="position: relative; display: flex; align-items: center;">
                                            <button class="resource-tab-button" onclick="showConstructionProjects()">在建项目</button>
                                            <button onclick="showAllProjectMarkers()" title="修复项目点显示" style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); background-color: rgba(40, 167, 69, 0.2); border: none; border-radius: 50%; width: 16px; height: 16px; display: flex; align-items: center; justify-content: center; cursor: pointer; padding: 0;">
                                                <i class="fas fa-road" style="font-size: 8px; color: white;"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 2. 风险隐患点和在建项目筛选内容 -->
                            <div class="resource-content-container">
                                <!-- 风险隐患点筛选内容 -->
                                <div id="risk-hazards-content" class="resource-tab-content active">
                                    <div class="filter-section">
                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="risk-level-select">风险等级：</label>
                                                <select id="risk-level-select" class="filter-select">
                                                    <option value="all">所有等级</option>
                                                    <option value="high">高风险</option>
                                                    <option value="medium">中风险</option>
                                                    <option value="low">低风险</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="filter-row">
                                            <div class="filter-item risk-type-list-container">
                                                <label>风险隐患类型：</label>
                                                <div class="risk-type-list">
                                                    <div class="risk-type-item">
                                                        <input type="checkbox" id="risk-type-highway" name="risk-type" value="highway" checked>
                                                        <label for="risk-type-highway">公路交通</label>
                                                    </div>
                                                    <div class="risk-type-item">
                                                        <input type="checkbox" id="risk-type-road-transport" name="risk-type" value="road-transport" checked>
                                                        <label for="risk-type-road-transport">道路运输</label>
                                                    </div>
                                                    <div class="risk-type-item">
                                                        <input type="checkbox" id="risk-type-water-transport" name="risk-type" value="water-transport" checked>
                                                        <label for="risk-type-water-transport">水路运输</label>
                                                    </div>
                                                    <div class="risk-type-item">
                                                        <input type="checkbox" id="risk-type-port-waterway" name="risk-type" value="port-waterway" checked>
                                                        <label for="risk-type-port-waterway">港口和航道</label>
                                                    </div>
                                                    <div class="risk-type-item">
                                                        <input type="checkbox" id="risk-type-flood-typhoon" name="risk-type" value="flood-typhoon" checked>
                                                        <label for="risk-type-flood-typhoon">防洪防台</label>
                                                    </div>
                                                    <div class="risk-type-item">
                                                        <input type="checkbox" id="risk-type-geological" name="risk-type" value="geological" checked>
                                                        <label for="risk-type-geological">地质灾害</label>
                                                    </div>
                                                    <div class="risk-type-item">
                                                        <input type="checkbox" id="risk-type-construction-production" name="risk-type" value="construction-production" checked>
                                                        <label for="risk-type-construction-production">公路水运工程生产</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 在建项目筛选内容 -->
                                <div id="construction-projects-content" class="resource-tab-content">
                                    <div class="filter-section">
                                        <div class="filter-row">
                                    <div class="filter-item project-type-list-container">
                                        <label>项目类型：</label>
                                        <div class="project-type-list">
                                            <div class="project-type-item">
                                                <input type="checkbox" id="project-type-large-waterway" name="project-type" value="large-waterway" checked>
                                                <label for="project-type-large-waterway">大型水运工程</label>
                                            </div>
                                            <div class="project-type-item">
                                                <input type="checkbox" id="project-type-port" name="project-type" value="port" checked>
                                                <label for="project-type-port">港口工程</label>
                                            </div>
                                            <div class="project-type-item">
                                                <input type="checkbox" id="project-type-highway" name="project-type" value="highway" checked>
                                                <label for="project-type-highway">高速公路</label>
                                            </div>
                                            <div class="project-type-item">
                                                <input type="checkbox" id="project-type-national-provincial" name="project-type" value="national-provincial" checked>
                                                <label for="project-type-national-provincial">普通国省干线</label>
                                            </div>
                                            <div class="project-type-item">
                                                <input type="checkbox" id="project-type-waterway" name="project-type" value="waterway" checked>
                                                <label for="project-type-waterway">水运工程</label>
                                            </div>
                                            <div class="project-type-item">
                                                <input type="checkbox" id="project-type-highway-construction" name="project-type" value="highway-construction" checked>
                                                <label for="project-type-highway-construction">在建高速公路</label>
                                            </div>
                                            <div class="project-type-item">
                                                <input type="checkbox" id="project-type-medium-low-risk-waterway" name="project-type" value="medium-low-risk-waterway" checked>
                                                <label for="project-type-medium-low-risk-waterway">中低风险(Ⅰ级)型水运工程</label>
                                            </div>
                                            <div class="project-type-item">
                                                <input type="checkbox" id="project-type-small-medium-waterway" name="project-type" value="small-medium-waterway" checked>
                                                <label for="project-type-small-medium-waterway">中小型水运工程</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="filter-row" style="flex-wrap: wrap; justify-content: flex-start;">

                                            <div class="filter-item" style="margin-right: 20px; margin-bottom: 10px; min-width: 200px;">
                                                <label for="project-status-select">项目状态：</label>
                                                <select id="project-status-select" class="filter-select">
                                                    <option value="all">所有状态</option>
                                                    <option value="started">已开工</option>
                                                    <option value="not-started">未开工</option>
                                                    <option value="paused">暂停</option>
                                                    <option value="closed">已封闭</option>
                                                </select>
                                            </div>

                                            <div class="filter-item" style="margin-right: 20px; margin-bottom: 10px; min-width: 200px;">
                                                <label for="project-risk-select">风险状态：</label>
                                                <select id="project-risk-select" class="filter-select">
                                                    <option value="all">所有状态</option>
                                                    <option value="yes">存在风险隐患</option>
                                                    <option value="no">无风险隐患</option>
                                                </select>
                                            </div>

                                            <div class="filter-item" style="margin-right: 20px; margin-bottom: 10px; min-width: 200px;">
                                                <label for="project-risk-level-select">风险等级：</label>
                                                <select id="project-risk-level-select" class="filter-select">
                                                    <option value="all">所有等级</option>
                                                    <option value="high">高风险</option>
                                                    <option value="medium">中风险</option>
                                                    <option value="low">低风险</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 3. 资源条件筛选 -->
                            <div class="resource-condition-filter">
                                <div class="filter-tabs">
                                    <button class="filter-tab-button active" data-tab="unit" onclick="switchFilterTab(this, 'unit')">按单位划分</button>
                                    <button class="filter-tab-button" data-tab="road" onclick="switchFilterTab(this, 'road')">按路段划分</button>
                                </div>

                                <!-- 3.1 按单位划分内容 -->
                                <div id="filter-by-unit-content" class="filter-tab-content active">
                                    <ul class="collapsible-tree">
                                        <li>
                                            <span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt" value="gxtyt"><label for="unit-gxtyt">广西交通运输厅</label>
                                            <ul>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-nn" value="gxtyt-nn"><label for="unit-gxtyt-nn">南宁市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-lz" value="gxtyt-lz"><label for="unit-gxtyt-lz">柳州市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-gl" value="gxtyt-gl"><label for="unit-gxtyt-gl">桂林市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-wz" value="gxtyt-wz"><label for="unit-gxtyt-wz">梧州市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-bh" value="gxtyt-bh"><label for="unit-gxtyt-bh">北海市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-fcg" value="gxtyt-fcg"><label for="unit-gxtyt-fcg">防城港市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-qz" value="gxtyt-qz"><label for="unit-gxtyt-qz">钦州市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-gg" value="gxtyt-gg"><label for="unit-gxtyt-gg">贵港市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-yl" value="gxtyt-yl"><label for="unit-gxtyt-yl">玉林市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-bs" value="gxtyt-bs"><label for="unit-gxtyt-bs">百色市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-hz" value="gxtyt-hz"><label for="unit-gxtyt-hz">贺州市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-hc" value="gxtyt-hc"><label for="unit-gxtyt-hc">河池市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-lb" value="gxtyt-lb"><label for="unit-gxtyt-lb">来宾市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-cz" value="gxtyt-cz"><label for="unit-gxtyt-cz">崇左市交通运输局</label></li>
                                                <li>
                                                    <span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-zs" value="gxtyt-zs"><label for="unit-gxtyt-zs">直属单位</label>
                                                    <ul>
                                                        <li><input type="checkbox" id="unit-gxtyt-zs-glfz" value="gxtyt-zs-glfz"><label for="unit-gxtyt-zs-glfz">广西公路发展中心</label></li>
                                                        <li><input type="checkbox" id="unit-gxtyt-zs-gsglfz" value="gxtyt-zs-gsglfz"><label for="unit-gxtyt-zs-gsglfz">广西高速公路发展中心</label></li>
                                                    </ul>
                                                </li>
                                            </ul>
                                        </li>
                                        <li>
                                            <input type="checkbox" id="unit-qy" value="qy"><label for="unit-qy">企业</label>
                                        </li>
                                    </ul>
                                </div>

                                <!-- 3.2 按路段划分内容 -->
                                <div id="filter-by-road-content" class="filter-tab-content">
                                    <ul class="collapsible-tree">
                                        <li>
                                            <span class="tree-toggler">+</span><input type="checkbox" id="road-gl" value="gl"><label for="road-gl">公路</label>
                                            <ul>
                                                <li>
                                                    <span class="tree-toggler">+</span><input type="checkbox" id="road-gl-gs" value="gl-gs"><label for="road-gl-gs">高速公路</label>
                                                    <ul>
                                                        <li><input type="checkbox" id="road-gl-gs-g72" value="gl-gs-g72"><label for="road-gl-gs-g72">G72</label></li>
                                                        <li><input type="checkbox" id="road-gl-gs-g80" value="gl-gs-g80"><label for="road-gl-gs-g80">G80</label></li>
                                                    </ul>
                                                </li>
                                                <li>
                                                    <span class="tree-toggler">+</span><input type="checkbox" id="road-gl-gsgd" value="gl-gsgd"><label for="road-gl-gsgd">国省干道</label>
                                                    <ul>
                                                        <li><input type="checkbox" id="road-gl-gsgd-s201" value="gl-gsgd-s201"><label for="road-gl-gsgd-s201">S201</label></li>
                                                    </ul>
                                                </li>
                                                <li>
                                                    <span class="tree-toggler">+</span><input type="checkbox" id="road-gl-ncgl" value="gl-ncgl"><label for="road-gl-ncgl">农村公路</label>
                                                    <ul>
                                                        <li><input type="checkbox" id="road-gl-ncgl-x001" value="gl-ncgl-x001"><label for="road-gl-ncgl-x001">X001</label></li>
                                                        <li><input type="checkbox" id="road-gl-ncgl-y002" value="gl-ncgl-y002"><label for="road-gl-ncgl-y002">Y002</label></li>
                                                    </ul>
                                                </li>
                                            </ul>
                                        </li>
                                        <li>
                                            <span class="tree-toggler">+</span><input type="checkbox" id="road-sl" value="sl"><label for="road-sl">水路</label>
                                            <ul>
                                                <li><input type="checkbox" id="road-sl-xn" value="sl-xn"><label for="road-sl-xn">西江航道</label></li>
                                            </ul>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <!-- 4. 告警信息列表 -->
                            <div class="alert-list-container">
                                <h4>告警信息 <i class="fas fa-bell" style="color: #dc3545; margin-left: 5px;"></i></h4>
                                <div class="alert-tabs" style="display: flex; margin-bottom: 15px; border-bottom: 1px solid #ddd;">
                                    <button class="alert-tab-button active" data-tab="new-hazards" onclick="switchAlertTab(this, 'new-hazards')" style="flex: 1; padding: 10px 15px; border: none; background: #f8f9fa; color: #333; cursor: pointer; border-bottom: 2px solid #007bff; font-weight: bold;">新的隐患</button>
                                    <button class="alert-tab-button" data-tab="overdue-hazards" onclick="switchAlertTab(this, 'overdue-hazards')" style="flex: 1; padding: 10px 15px; border: none; background: #f8f9fa; color: #666; cursor: pointer; border-bottom: 2px solid transparent;">隐患整改超时</button>
                                </div>

                                <!-- 4.1 新的隐患内容 -->
                                <div id="alert-new-hazards-content" class="alert-tab-content active" style="display: block;">
                                    <ul class="alert-list" style="list-style: none; padding: 0; margin: 0;">
                                        <li class="alert-item high" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #dc3545; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-20 08:30</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #dc3545; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">高风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">南宁市G72高速K1499+500处边坡发现新的滑坡隐患</span>
                                            </div>
                                        </li>
                                        <li class="alert-item medium" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #ffc107; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-19 16:45</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #ffc107; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">中风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">柳州市S201省道K45+200处排水设施堵塞</span>
                                            </div>
                                        </li>
                                        <li class="alert-item high" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #dc3545; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-19 10:15</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #dc3545; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">高风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">桂林市灵川县X001县道K8+300处桥梁桥墩出现裂缝</span>
                                            </div>
                                        </li>
                                        <li class="alert-item low" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #28a745; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-18 14:20</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #28a745; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">低风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">梧州市G80高速K890+100处防洪标识缺失</span>
                                            </div>
                                        </li>
                                        <li class="alert-item medium" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #ffc107; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-18 09:30</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #ffc107; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">中风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">北海市合浦县Y002乡道K5+800处路基沉降</span>
                                            </div>
                                        </li>
                                        <li class="alert-item medium" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #ffc107; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-17 16:10</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #ffc107; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">中风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">防城港市东兴市S203省道K67+400处涵洞淤积</span>
                                            </div>
                                        </li>
                                        <li class="alert-item high" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #dc3545; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-17 11:25</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #dc3545; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">高风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">钦州市钦南区G75高速K1200+800处隧道渗水</span>
                                            </div>
                                        </li>
                                        <li class="alert-item low" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #28a745; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-16 15:40</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #28a745; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">低风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">贵港市平南县X008县道K12+500处检查步道损坏</span>
                                            </div>
                                        </li>
                                        <li class="alert-item medium" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #ffc107; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-16 10:05</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #ffc107; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">中风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">玉林市陆川县G72高速K1650+200处边坡植被损坏</span>
                                            </div>
                                        </li>
                                        <li class="alert-item high" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #dc3545; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-15 14:15</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #dc3545; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">高风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">百色市田阳区S308省道K89+700处山体松动</span>
                                            </div>
                                        </li>
                                    </ul>
                                </div>

                                <!-- 4.2 隐患整改超时内容 -->
                                <div id="alert-overdue-hazards-content" class="alert-tab-content" style="display: none;">
                                    <ul class="alert-list" style="list-style: none; padding: 0; margin: 0;">
                                        <li class="alert-item overdue-high" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #dc3545; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: #fff5f5;">
                                            <div class="alert-time" style="font-size: 12px; color: #dc3545; margin-bottom: 5px; font-weight: bold;">2024-05-10 (超时10天)</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #dc3545; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">高风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">贺州市八步区G72高速K1800+300处边坡加固工程</span>
                                            </div>
                                        </li>
                                        <li class="alert-item overdue-high" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #dc3545; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: #fff5f5;">
                                            <div class="alert-time" style="font-size: 12px; color: #dc3545; margin-bottom: 5px; font-weight: bold;">2024-05-08 (超时12天)</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #dc3545; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">高风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">河池市金城江区S309省道K56+800处桥梁维修</span>
                                            </div>
                                        </li>
                                        <li class="alert-item overdue-medium" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #ffc107; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: #fffbf0;">
                                            <div class="alert-time" style="font-size: 12px; color: #ffc107; margin-bottom: 5px; font-weight: bold;">2024-05-05 (超时15天)</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #ffc107; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">中风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">来宾市兴宾区G72高速K1700+500处排水系统疏通</span>
                                            </div>
                                        </li>
                                        <li class="alert-item overdue-medium" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #ffc107; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: #fffbf0;">
                                            <div class="alert-time" style="font-size: 12px; color: #ffc107; margin-bottom: 5px; font-weight: bold;">2024-05-03 (超时17天)</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #ffc107; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">中风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">崇左市江州区X012县道K23+400处路基修复</span>
                                            </div>
                                        </li>
                                        <li class="alert-item overdue-high" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #dc3545; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: #fff5f5;">
                                            <div class="alert-time" style="font-size: 12px; color: #dc3545; margin-bottom: 5px; font-weight: bold;">2024-04-30 (超时20天)</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #dc3545; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">高风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">南宁市邕宁区G80高速K1050+200处隧道衬砌修复</span>
                                            </div>
                                        </li>
                                        <li class="alert-item overdue-low" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #28a745; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: #f8fff9;">
                                            <div class="alert-time" style="font-size: 12px; color: #28a745; margin-bottom: 5px; font-weight: bold;">2024-04-28 (超时22天)</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #28a745; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">低风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">柳州市柳江区Y005乡道K7+600处防洪标识更换</span>
                                            </div>
                                        </li>
                                        <li class="alert-item overdue-medium" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #ffc107; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: #fffbf0;">
                                            <div class="alert-time" style="font-size: 12px; color: #ffc107; margin-bottom: 5px; font-weight: bold;">2024-04-25 (超时25天)</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #ffc107; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">中风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">桂林市临桂区S202省道K78+900处边沟清淤</span>
                                            </div>
                                        </li>
                                        <li class="alert-item overdue-high" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #dc3545; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: #fff5f5;">
                                            <div class="alert-time" style="font-size: 12px; color: #dc3545; margin-bottom: 5px; font-weight: bold;">2024-04-20 (超时30天)</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #dc3545; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">高风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">梧州市长洲区G72高速K1550+400处边坡防护网修复</span>
                                            </div>
                                        </li>
                                        <li class="alert-item overdue-medium" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #ffc107; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: #fffbf0;">
                                            <div class="alert-time" style="font-size: 12px; color: #ffc107; margin-bottom: 5px; font-weight: bold;">2024-04-18 (超时32天)</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #ffc107; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">中风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">北海市银海区X015县道K34+200处涵洞修复</span>
                                            </div>
                                        </li>
                                        <li class="alert-item overdue-low" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #28a745; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: #f8fff9;">
                                            <div class="alert-time" style="font-size: 12px; color: #28a745; margin-bottom: 5px; font-weight: bold;">2024-04-15 (超时35天)</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #28a745; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">低风险</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">防城港市港口区Y008乡道K9+100处检查步道修复</span>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </aside>

                    <section class="map-display-area">
                        <!-- 最新告警提示框 - 长方形显示完整内容 -->
                        <div class="latest-alert-container" id="risk-map-alert" style="position: absolute; top: 15px; left: 15px; z-index: 1000; width: 480px;">
                            <div class="latest-alert high" style="background: rgba(220, 53, 69, 0.95); color: white; padding: 12px 15px; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.2); display: flex; align-items: center; gap: 10px; font-size: 13px;">
                                <div class="alert-icon" style="font-size: 16px; flex-shrink: 0;">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="alert-content" style="flex: 1;">
                                    <div class="alert-message" style="line-height: 1.4;">
                                        <span class="alert-level" style="background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 3px; margin-right: 8px; font-size: 11px; font-weight: bold;">高风险</span>
                                        <span class="alert-text" style="font-size: 13px;">南宁市G72高速K1499+500处边坡发现新的滑坡隐患</span>
                                    </div>
                                </div>
                                <div class="alert-actions" style="display: flex; gap: 8px; flex-shrink: 0;">
                                    <button class="alert-more-btn" onclick="document.getElementById('alert-new-hazards-content').scrollIntoView({behavior: 'smooth'})" style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 5px 10px; border-radius: 4px; cursor: pointer; font-size: 11px; font-weight: bold;">
                                        查看详情
                                    </button>
                                    <button class="alert-close-btn" onclick="document.getElementById('risk-map-alert').style.display='none'" style="background: none; border: none; color: white; cursor: pointer; font-size: 14px; padding: 3px;">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 风险隐患管理按钮 -->
                        <div class="management-button-container">
                            <a href="my_check_tasks.html" class="management-button">
                                <i class="fas fa-tasks"></i> 风险隐患管理
                            </a>
                        </div>

                        <!-- 地图图片 -->
                        <img src="lib/map_new.png" alt="广西地图" id="map-image">

                        <!-- 图例 - 移到右下角，提高背景透明度，使用正确的7种隐患类型，添加在建项目 -->
                        <div class="map-legend" style="position: absolute !important; right: 15px !important; bottom: 15px !important; left: auto !important; top: auto !important; background: rgba(255, 255, 255, 0.98) !important; padding: 10px; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.15); max-width: 160px; z-index: 1000; font-size: 10px; border: 1px solid rgba(0,0,0,0.1);">
                            <div class="legend-section" style="margin-bottom: 8px;">
                                <div class="legend-title" style="font-size: 11px; font-weight: bold; color: #333; margin-bottom: 5px; border-bottom: 1px solid #007bff; padding-bottom: 2px;">风险隐患类型</div>
                                <div class="legend-items">
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(220, 53, 69, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-road" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">公路交通</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(40, 167, 69, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-truck" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">道路运输</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(23, 162, 184, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-ship" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">水路运输</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(255, 193, 7, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-anchor" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">港口航道</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(108, 117, 125, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-water" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">防洪防台</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(255, 153, 0, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-mountain" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">地质灾害</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(220, 53, 69, 0.7); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-hard-hat" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">工程生产</div>
                                    </div>
                                </div>
                            </div>

                            <div class="legend-section" style="margin-bottom: 8px;">
                                <div class="legend-title" style="font-size: 11px; font-weight: bold; color: #333; margin-bottom: 5px; border-bottom: 1px solid #28a745; padding-bottom: 2px;">在建项目类型</div>
                                <div class="legend-items">
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(40, 167, 69, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-ship" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">大型水运工程</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(23, 162, 184, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-anchor" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">港口工程</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(220, 53, 69, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-road" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">高速公路</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(255, 193, 7, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-route" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">普通国省干线</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(108, 117, 125, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-water" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">水运工程</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(255, 87, 34, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-highway" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">在建高速公路</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(156, 39, 176, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-ship" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">中低风险(Ⅰ级)型水运工程</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(76, 175, 80, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-water" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">中小型水运工程</div>
                                    </div>
                                </div>
                            </div>

                            <div class="legend-section">
                                <div class="legend-title" style="font-size: 11px; font-weight: bold; color: #333; margin-bottom: 5px; border-bottom: 1px solid #dc3545; padding-bottom: 2px;">风险等级</div>
                                <div class="legend-items">
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; background-color: rgba(220, 53, 69, 0.9); border-radius: 2px; margin-right: 5px;"></div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">高风险</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; background-color: rgba(255, 153, 0, 0.9); border-radius: 2px; margin-right: 5px;"></div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">中风险</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; background-color: rgba(40, 167, 69, 0.9); border-radius: 2px; margin-right: 5px;"></div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">低风险</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 地图上的标注点 - 风险隐患点 -->
                        <!-- 玉林区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 469px; left: 240px;" data-id="risk001" data-type="risks" data-risk-type="slope" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <!-- 北海区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 460px; left: 384px;" data-id="risk002" data-type="risks" data-risk-type="drainage" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-tint"></i>
                        </div>
                        <!-- 钦州区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 510px; left: 433px;" data-id="risk003" data-type="risks" data-risk-type="bridge" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-archway"></i>
                        </div>
                        <!-- 钦州区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 565px; left: 509px;" data-id="risk004" data-type="risks" data-risk-type="bridge" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-archway"></i>
                        </div>
                        <!-- 百色区域 -->
                        <div class="map-marker risk-marker risk-high" style="top: 525px; left: 569px;" data-id="risk005" data-type="risks" data-risk-type="tunnel" data-risk-level="high" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 729px; left: 581px;" data-id="risk006" data-type="risks" data-risk-type="slope" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <!-- 桂林区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 748px; left: 448px;" data-id="risk007" data-type="risks" data-risk-type="slope" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <!-- 贺州区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 639px; left: 611px;" data-id="risk008" data-type="risks" data-risk-type="flood-section" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-water"></i>
                        </div>
                        <!-- 梧州区域 -->
                        <div class="map-marker risk-marker risk-high" style="top: 839px; left: 601px;" data-id="risk009" data-type="risks" data-risk-type="flood-section" data-risk-level="high" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-water"></i>
                        </div>
                        <!-- 贺州区域 -->
                        <div class="map-marker risk-marker risk-high" style="top: 954px; left: 537px;" data-id="risk010" data-type="risks" data-risk-type="slope" data-risk-level="high" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 900px; left: 654px;" data-id="risk011" data-type="risks" data-risk-type="tunnel" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 百色区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 848px; left: 786px;" data-id="risk012" data-type="risks" data-risk-type="drainage" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-tint"></i>
                        </div>
                        <!-- 河池区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 943px; left: 739px;" data-id="risk013" data-type="risks" data-risk-type="flood-section" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-water"></i>
                        </div>
                        <!-- 防城港区域 -->
                        <div class="map-marker risk-marker risk-high" style="top: 969px; left: 851px;" data-id="risk014" data-type="risks" data-risk-type="slope" data-risk-level="high" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker risk-marker risk-high" style="top: 1004px; left: 905px;" data-id="risk015" data-type="risks" data-risk-type="tunnel" data-risk-level="high" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 钦州区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 931px; left: 988px;" data-id="risk016" data-type="risks" data-risk-type="flood-section" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-water"></i>
                        </div>
                        <!-- 来宾区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 1031px; left: 1005px;" data-id="risk017" data-type="risks" data-risk-type="tunnel" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 河池区域 -->
                        <div class="map-marker risk-marker risk-high" style="top: 1000px; left: 1111px;" data-id="risk018" data-type="risks" data-risk-type="flood-section" data-risk-level="high" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-water"></i>
                        </div>
                        <!-- 贺州区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 941px; left: 1150px;" data-id="risk019" data-type="risks" data-risk-type="flood-section" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-water"></i>
                        </div>
                        <!-- 梧州区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 930px; left: 1200px;" data-id="risk020" data-type="risks" data-risk-type="flood-section" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-water"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 867px; left: 1195px;" data-id="risk021" data-type="risks" data-risk-type="flood-section" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-water"></i>
                        </div>
                        <!-- 梧州区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 767px; left: 1158px;" data-id="risk022" data-type="risks" data-risk-type="slope" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <!-- 百色区域 -->
                        <div class="map-marker risk-marker risk-high" style="top: 689px; left: 1193px;" data-id="risk023" data-type="risks" data-risk-type="bridge" data-risk-level="high" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-archway"></i>
                        </div>
                        <!-- 来宾区域 -->
                        <div class="map-marker risk-marker risk-high" style="top: 735px; left: 963px;" data-id="risk024" data-type="risks" data-risk-type="tunnel" data-risk-level="high" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 梧州区域 -->
                        <div class="map-marker risk-marker risk-high" style="top: 713px; left: 812px;" data-id="risk025" data-type="risks" data-risk-type="tunnel" data-risk-level="high" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 来宾区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 777px; left: 865px;" data-id="risk026" data-type="risks" data-risk-type="flood-section" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-water"></i>
                        </div>
                        <!-- 来宾区域 -->
                        <div class="map-marker risk-marker risk-high" style="top: 743px; left: 729px;" data-id="risk027" data-type="risks" data-risk-type="tunnel" data-risk-level="high" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 桂林区域 -->
                        <div class="map-marker risk-marker risk-high" style="top: 631px; left: 791px;" data-id="risk028" data-type="risks" data-risk-type="tunnel" data-risk-level="high" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 565px; left: 663px;" data-id="risk029" data-type="risks" data-risk-type="bridge" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-archway"></i>
                        </div>
                        <!-- 来宾区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 410px; left: 771px;" data-id="risk030" data-type="risks" data-risk-type="tunnel" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 419px; left: 650px;" data-id="risk031" data-type="risks" data-risk-type="bridge" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-archway"></i>
                        </div>
                        <!-- 崇左区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 498px; left: 772px;" data-id="risk032" data-type="risks" data-risk-type="tunnel" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 南宁区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 443px; left: 910px;" data-id="risk033" data-type="risks" data-risk-type="flood-section" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-water"></i>
                        </div>
                        <!-- 贺州区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 377px; left: 957px;" data-id="risk034" data-type="risks" data-risk-type="bridge" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-archway"></i>
                        </div>
                        <!-- 防城港区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 280px; left: 1035px;" data-id="risk035" data-type="risks" data-risk-type="flood-section" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-water"></i>
                        </div>
                        <!-- 南宁区域 -->
                        <div class="map-marker risk-marker risk-high" style="top: 323px; left: 1100px;" data-id="risk036" data-type="risks" data-risk-type="tunnel" data-risk-level="high" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker risk-marker risk-high" style="top: 300px; left: 1208px;" data-id="risk037" data-type="risks" data-risk-type="drainage" data-risk-level="high" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-tint"></i>
                        </div>
                        <!-- 贺州区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 251px; left: 1218px;" data-id="risk038" data-type="risks" data-risk-type="slope" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <!-- 南宁区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 201px; left: 1166px;" data-id="risk039" data-type="risks" data-risk-type="flood-section" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-water"></i>
                        </div>
                        <!-- 防城港区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 160px; left: 1272px;" data-id="risk040" data-type="risks" data-risk-type="tunnel" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 梧州区域 -->
                        <div class="map-marker risk-marker risk-high" style="top: 168px; left: 1346px;" data-id="risk041" data-type="risks" data-risk-type="flood-section" data-risk-level="high" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-water"></i>
                        </div>
                        <!-- 南宁区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 411px; left: 1320px;" data-id="risk042" data-type="risks" data-risk-type="bridge" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-archway"></i>
                        </div>
                        <!-- 百色区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 399px; left: 1257px;" data-id="risk043" data-type="risks" data-risk-type="tunnel" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 来宾区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 442px; left: 1179px;" data-id="risk044" data-type="risks" data-risk-type="slope" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 475px; left: 1062px;" data-id="risk045" data-type="risks" data-risk-type="tunnel" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 钦州区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 548px; left: 1030px;" data-id="risk046" data-type="risks" data-risk-type="bridge" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-archway"></i>
                        </div>
                        <!-- 河池区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 607px; left: 1095px;" data-id="risk047" data-type="risks" data-risk-type="drainage" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-tint"></i>
                        </div>
                        <!-- 崇左区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 657px; left: 1098px;" data-id="risk048" data-type="risks" data-risk-type="slope" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 734px; left: 1057px;" data-id="risk049" data-type="risks" data-risk-type="slope" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <!-- 南宁区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 840px; left: 1085px;" data-id="risk050" data-type="risks" data-risk-type="drainage" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-tint"></i>
                        </div>
                        <!-- 来宾区域 -->
                        <div class="map-marker risk-marker risk-high" style="top: 849px; left: 985px;" data-id="risk051" data-type="risks" data-risk-type="slope" data-risk-level="high" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 645px; left: 912px;" data-id="risk052" data-type="risks" data-risk-type="slope" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <!-- 防城港区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 800px; left: 1243px;" data-id="risk053" data-type="risks" data-risk-type="flood-section" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-water"></i>
                        </div>
                        <!-- 河池区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 701px; left: 1293px;" data-id="risk054" data-type="risks" data-risk-type="bridge" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-archway"></i>
                        </div>
                        <!-- 百色区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 640px; left: 1379px;" data-id="risk055" data-type="risks" data-risk-type="tunnel" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 南宁区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 639px; left: 1294px;" data-id="risk056" data-type="risks" data-risk-type="flood-section" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-water"></i>
                        </div>
                        <!-- 崇左区域 -->
                        <div class="map-marker risk-marker risk-high" style="top: 551px; left: 1378px;" data-id="risk057" data-type="risks" data-risk-type="flood-section" data-risk-level="high" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-water"></i>
                        </div>
                        <!-- 防城港区域 -->
                        <div class="map-marker risk-marker risk-low" style="top: 463px; left: 1391px;" data-id="risk058" data-type="risks" data-risk-type="drainage" data-risk-level="low" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-tint"></i>
                        </div>
                        <!-- 来宾区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 387px; left: 1399px;" data-id="risk059" data-type="risks" data-risk-type="flood-section" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-water"></i>
                        </div>
                        <!-- 南宁区域 -->
                        <div class="map-marker risk-marker risk-medium" style="top: 550px; left: 867px;" data-id="risk060" data-type="risks" data-risk-type="drainage" data-risk-level="medium" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-tint"></i>
                        </div>

                        <!-- 在建项目点 -->
                        <!-- 梧州区域 -->
                        <div class="map-marker project-marker project-risk" style="top: 478px; left: 339px; color: red; display: none;" data-id="project001" data-type="projects" data-project-type="bridge" data-project-status="started" data-has-risk="yes" data-risk-level="high" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-archway"></i>
                        </div>
                        <!-- 北海区域 -->
                        <div class="map-marker project-marker project-risk" style="top: 469px; left: 481px; color: red; display: none;" data-id="project002" data-type="projects" data-project-type="slope" data-project-status="started" data-has-risk="yes" data-risk-level="high" data-has-measure="yes" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 崇左区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 634px; left: 491px;  display: none;" data-id="project003" data-type="projects" data-project-type="road" data-project-status="started" data-has-risk="no" data-has-measure="yes" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-road"></i>
                        </div>
                        <!-- 崇左区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 708px; left: 490px;  display: none;" data-id="project004" data-type="projects" data-project-type="bridge" data-project-status="started" data-has-risk="no" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-archway"></i>
                        </div>
                        <!-- 柳州区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 860px; left: 725px;  display: none;" data-id="project005" data-type="projects" data-project-type="road" data-project-status="started" data-has-risk="no" data-has-measure="yes" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-road"></i>
                        </div>
                        <!-- 河池区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 869px; left: 912px;  display: none;" data-id="project006" data-type="projects" data-project-type="slope" data-project-status="started" data-has-risk="no" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker project-marker project-risk" style="top: 736px; left: 924px;  display: none;" data-id="project007" data-type="projects" data-project-type="slope" data-project-status="started" data-has-risk="yes" data-risk-level="low" data-has-measure="yes" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 防城港区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 553px; left: 932px;  display: none;" data-id="project008" data-type="projects" data-project-type="slope" data-project-status="started" data-has-risk="no" data-has-measure="yes" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 桂林区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 479px; left: 986px;  display: none;" data-id="project009" data-type="projects" data-project-type="tunnel" data-project-status="started" data-has-risk="no" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 防城港区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 415px; left: 1127px;  display: none;" data-id="project010" data-type="projects" data-project-type="tunnel" data-project-status="started" data-has-risk="no" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 河池区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 233px; left: 1044px;  display: none;" data-id="project011" data-type="projects" data-project-type="road" data-project-status="started" data-has-risk="no" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-road"></i>
                        </div>
                        <!-- 钦州区域 -->
                        <div class="map-marker project-marker project-risk" style="top: 298px; left: 1151px; color: orange; display: none;" data-id="project012" data-type="projects" data-project-type="road" data-project-status="started" data-has-risk="yes" data-risk-level="medium" data-has-measure="yes" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-road"></i>
                        </div>
                        <!-- 防城港区域 -->
                        <div class="map-marker project-marker project-risk" style="top: 508px; left: 1315px;  display: none;" data-id="project013" data-type="projects" data-project-type="slope" data-project-status="started" data-has-risk="yes" data-risk-level="low" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 崇左区域 -->
                        <div class="map-marker project-marker project-risk" style="top: 634px; left: 1248px;  display: none;" data-id="project014" data-type="projects" data-project-type="bridge" data-project-status="started" data-has-risk="yes" data-risk-level="low" data-has-measure="yes" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-archway"></i>
                        </div>
                        <!-- 柳州区域 -->
                        <div class="map-marker project-marker project-risk" style="top: 795px; left: 1112px; color: orange; display: none;" data-id="project015" data-type="projects" data-project-type="bridge" data-project-status="started" data-has-risk="yes" data-risk-level="medium" data-has-measure="yes" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-archway"></i>
                        </div>
                        <!-- 防城港区域 -->
                        <div class="map-marker project-marker project-risk" style="top: 929px; left: 1093px; color: red; display: none;" data-id="project016" data-type="projects" data-project-type="tunnel" data-project-status="started" data-has-risk="yes" data-risk-level="high" data-has-measure="yes" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 百色区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 783px; left: 1324px;  display: none;" data-id="project017" data-type="projects" data-project-type="road" data-project-status="started" data-has-risk="no" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-road"></i>
                        </div>
                        <!-- 来宾区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 631px; left: 995px;  display: none;" data-id="project018" data-type="projects" data-project-type="tunnel" data-project-status="started" data-has-risk="no" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 百色区域 -->
                        <div class="map-marker project-marker project-risk" style="top: 576px; left: 781px; color: red; display: none;" data-id="project019" data-type="projects" data-project-type="road" data-project-status="started" data-has-risk="yes" data-risk-level="high" data-has-measure="yes" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-road"></i>
                        </div>
                        <!-- 梧州区域 -->
                        <div class="map-marker project-marker project-risk" style="top: 389px; left: 912px; color: orange; display: none;" data-id="project020" data-type="projects" data-project-type="road" data-project-status="started" data-has-risk="yes" data-risk-level="medium" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-road"></i>
                        </div>
                        <!-- 柳州区域 -->
                        <div class="map-marker project-marker project-risk" style="top: 993px; left: 797px;  display: none;" data-id="project021" data-type="projects" data-project-type="road" data-project-status="started" data-has-risk="yes" data-risk-level="low" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-road"></i>
                        </div>
                        <!-- 来宾区域 -->
                        <div class="map-marker project-marker project-risk" style="top: 937px; left: 576px; color: red; display: none;" data-id="project022" data-type="projects" data-project-type="road" data-project-status="started" data-has-risk="yes" data-risk-level="high" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-road"></i>
                        </div>
                        <!-- 钦州区域 -->
                        <div class="map-marker project-marker project-risk" style="top: 852px; left: 562px;  display: none;" data-id="project023" data-type="projects" data-project-type="road" data-project-status="started" data-has-risk="yes" data-risk-level="low" data-has-measure="yes" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-road"></i>
                        </div>
                        <!-- 河池区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 710px; left: 683px;  display: none;" data-id="project024" data-type="projects" data-project-type="road" data-project-status="started" data-has-risk="no" data-has-measure="yes" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-road"></i>
                        </div>
                        <!-- 贵港区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 1027px; left: 978px;  display: none;" data-id="project025" data-type="projects" data-project-type="bridge" data-project-status="started" data-has-risk="no" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-archway"></i>
                        </div>
                        <!-- 桂林区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 718px; left: 1175px;  display: none;" data-id="project026" data-type="projects" data-project-type="road" data-project-status="started" data-has-risk="no" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-road"></i>
                        </div>
                        <!-- 柳州区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 403px; left: 745px;  display: none;" data-id="project027" data-type="projects" data-project-type="road" data-project-status="started" data-has-risk="no" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-road"></i>
                        </div>
                        <!-- 贺州区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 399px; left: 597px;  display: none;" data-id="project028" data-type="projects" data-project-type="tunnel" data-project-status="started" data-has-risk="no" data-has-measure="yes" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 柳州区域 -->
                        <div class="map-marker project-marker project-risk" style="top: 194px; left: 1316px;  display: none;" data-id="project029" data-type="projects" data-project-type="tunnel" data-project-status="started" data-has-risk="yes" data-risk-level="low" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 梧州区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 181px; left: 1166px;  display: none;" data-id="project030" data-type="projects" data-project-type="road" data-project-status="started" data-has-risk="no" data-has-measure="yes" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-road"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 412px; left: 1313px;  display: none;" data-id="project031" data-type="projects" data-project-type="tunnel" data-project-status="started" data-has-risk="no" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <!-- 梧州区域 -->
                        <div class="map-marker project-marker project-risk" style="top: 595px; left: 1464px; color: red; display: none;" data-id="project032" data-type="projects" data-project-type="road" data-project-status="started" data-has-risk="yes" data-risk-level="high" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-road"></i>
                        </div>
                        <!-- 百色区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 503px; left: 1484px;  display: none;" data-id="project033" data-type="projects" data-project-type="road" data-project-status="started" data-has-risk="no" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-road"></i>
                        </div>
                        <!-- 桂林区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 554px; left: 1238px;  display: none;" data-id="project034" data-type="projects" data-project-type="bridge" data-project-status="started" data-has-risk="no" data-has-measure="yes" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-archway"></i>
                        </div>
                        <!-- 钦州区域 -->
                        <div class="map-marker project-marker project-safe" style="top: 730px; left: 1034px;  display: none;" data-id="project035" data-type="projects" data-project-type="tunnel" data-project-status="started" data-has-risk="no" data-has-measure="no" data-unit="gxtyt-nn" data-road="gl-gs-g72">
                            <i class="fas fa-mountain"></i>
                        </div>
                    </section>

                    <aside class="right-sidebar">
                        <div class="statistics-panel">
                            <h3>统计分析</h3>
                            <div class="stat-grid">
                                <div class="stat-item">
                                    <div class="stat-label">风险隐患数量</div>
                                    <div class="stat-value" id="risk-count">120</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">在建项目数量</div>
                                    <div class="stat-value" id="project-count">85</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">高风险隐患</div>
                                    <div class="stat-value risk-high-count" id="high-risk-count">45</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">已整改风险</div>
                                    <div class="stat-value" id="fixed-risk-count">68</div>
                                </div>
                            </div>
                        </div>
                        <div class="risk-details-list">
                            <h3>详情列表</h3>

                            <!-- 详情列表标签卡 -->
                            <div class="details-tabs">
                                <button class="details-tab-button active" onclick="showRiskHazardsDetails()">风险隐患</button>
                                <button class="details-tab-button" onclick="showProjectsDetails()">在建项目</button>
                            </div>

                            <!-- 风险隐患详情内容 -->
                            <div id="details-risk-hazards-content" class="details-tab-content" style="display: block;">
                                <table class="details-table">
                                    <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>地市</th>
                                            <th>风险点名称</th>
                                            <th>风险等级</th>
                                            <th>上报时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 风险隐患列表内容 -->
                                        <tr>
                                            <td>1</td>
                                            <td>南宁市</td>
                                            <td>K10+200边坡失稳</td>
                                            <td><span class="risk-level high">高</span></td>
                                            <td>2024-05-01</td>
                                        </tr>
                                        <tr>
                                            <td>2</td>
                                            <td>桂林市</td>
                                            <td>大桥2号桥墩裂缝</td>
                                            <td><span class="risk-level medium">中</span></td>
                                            <td>2024-05-10</td>
                                        </tr>
                                        <tr>
                                            <td>3</td>
                                            <td>玉林市</td>
                                            <td>S201公路K45+300路基沉降</td>
                                            <td><span class="risk-level high">高</span></td>
                                            <td>2024-05-08</td>
                                        </tr>
                                        <tr>
                                            <td>4</td>
                                            <td>柳州市</td>
                                            <td>G72高速K230+500涵洞堵塞</td>
                                            <td><span class="risk-level low">低</span></td>
                                            <td>2024-05-12</td>
                                        </tr>
                                        <tr>
                                            <td>5</td>
                                            <td>南宁市</td>
                                            <td>G80高速K150+200护栏损坏</td>
                                            <td><span class="risk-level medium">中</span></td>
                                            <td>2024-05-15</td>
                                        </tr>
                                        <tr>
                                            <td>6</td>
                                            <td>桂林市</td>
                                            <td>西江航道5号浮标偏移</td>
                                            <td><span class="risk-level medium">中</span></td>
                                            <td>2024-05-14</td>
                                        </tr>
                                        <tr>
                                            <td>7</td>
                                            <td>钦州市</td>
                                            <td>G75高速K320+100边坡滑坡</td>
                                            <td><span class="risk-level high">高</span></td>
                                            <td>2024-05-16</td>
                                        </tr>
                                        <tr>
                                            <td>8</td>
                                            <td>贵港市</td>
                                            <td>S203省道K78+500路面塌陷</td>
                                            <td><span class="risk-level medium">中</span></td>
                                            <td>2024-05-17</td>
                                        </tr>
                                        <tr>
                                            <td>9</td>
                                            <td>河池市</td>
                                            <td>G65高速K420+200隧道渗水</td>
                                            <td><span class="risk-level low">低</span></td>
                                            <td>2024-05-18</td>
                                        </tr>
                                        <tr>
                                            <td>10</td>
                                            <td>防城港市</td>
                                            <td>G7511高速K60+300排水沟堵塞</td>
                                            <td><span class="risk-level low">低</span></td>
                                            <td>2024-05-19</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 在建项目详情内容 -->
                            <div id="details-projects-content" class="details-tab-content" style="display: none;">
                                <table class="details-table">
                                    <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>地市</th>
                                            <th>项目名称</th>
                                            <th>项目类型</th>
                                            <th>开工时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 在建项目列表内容 -->
                                        <tr>
                                            <td>1</td>
                                            <td>南宁市</td>
                                            <td>G72高速扩建工程</td>
                                            <td>道路工程</td>
                                            <td>2023-10-15</td>
                                        </tr>
                                        <tr>
                                            <td>2</td>
                                            <td>桂林市</td>
                                            <td>漓江大桥改造工程</td>
                                            <td>桥梁工程</td>
                                            <td>2024-01-20</td>
                                        </tr>
                                        <tr>
                                            <td>3</td>
                                            <td>玉林市</td>
                                            <td>S201公路改扩建工程</td>
                                            <td>道路工程</td>
                                            <td>2023-12-05</td>
                                        </tr>
                                        <tr>
                                            <td>4</td>
                                            <td>柳州市</td>
                                            <td>柳江隧道建设工程</td>
                                            <td>隧道工程</td>
                                            <td>2024-03-10</td>
                                        </tr>
                                        <tr>
                                            <td>5</td>
                                            <td>南宁市</td>
                                            <td>邕江航道整治工程</td>
                                            <td>水运工程</td>
                                            <td>2024-02-28</td>
                                        </tr>
                                        <tr>
                                            <td>6</td>
                                            <td>桂林市</td>
                                            <td>灵川互通立交桥工程</td>
                                            <td>桥梁工程</td>
                                            <td>2024-04-15</td>
                                        </tr>
                                        <tr>
                                            <td>7</td>
                                            <td>钦州市</td>
                                            <td>钦州港疏港公路工程</td>
                                            <td>道路工程</td>
                                            <td>2024-03-05</td>
                                        </tr>
                                        <tr>
                                            <td>8</td>
                                            <td>贵港市</td>
                                            <td>贵港大桥加固工程</td>
                                            <td>桥梁工程</td>
                                            <td>2024-02-15</td>
                                        </tr>
                                        <tr>
                                            <td>9</td>
                                            <td>河池市</td>
                                            <td>都安隧道建设工程</td>
                                            <td>隧道工程</td>
                                            <td>2024-01-10</td>
                                        </tr>
                                        <tr>
                                            <td>10</td>
                                            <td>防城港市</td>
                                            <td>东湾互通立交工程</td>
                                            <td>桥梁工程</td>
                                            <td>2024-04-20</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </aside>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入公共JavaScript -->
    <script src="js/emergency-common.js"></script>

    <!-- 风险点详情模态框 -->
    <div id="risk-details-modal" class="modal" style="display: none; position: fixed; z-index: 10000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.7);">
        <div class="modal-content" style="background-color: #2c3e50; margin: 3% auto; padding: 0; border-radius: 8px; width: 90%; max-width: 1200px; max-height: 90vh; overflow-y: auto; box-shadow: 0 4px 20px rgba(0,0,0,0.5); border: 1px solid #34495e;">
            <div class="modal-header" style="background-color: #34495e; color: #ecf0f1; padding: 20px; border-radius: 8px 8px 0 0; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #4a5f7a;">
                <div style="display: flex; align-items: center; gap: 15px;">
                    <button id="risk-return-button" onclick="returnToProjectModal()" style="background-color: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; font-size: 14px; display: none;">
                        <i class="fas fa-arrow-left"></i> 返回项目详情
                    </button>
                    <h3 id="risk-modal-title" style="margin: 0; font-size: 20px; color: #3498db;">风险隐患详情: 泉南高速吴家屯隧道（柳州端）洞口上方山体塌方隐患</h3>
                </div>
                <span class="close-button" style="color: #ecf0f1; font-size: 28px; font-weight: bold; cursor: pointer; line-height: 1;" onclick="closeRiskModal()">&times;</span>
            </div>
            <div class="modal-body" style="padding: 25px; background-color: #2c3e50; color: #ecf0f1; font-size: 15px;">
                <!-- 基本信息 -->
                <div class="info-section" style="margin-bottom: 25px;">
                    <h4 style="color: #3498db; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px; font-size: 18px;">基本信息</h4>
                    <div style="background-color: #2c3e50; padding: 20px; border-radius: 8px; border: 1px solid #4a5f7a;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">省份名称：</strong><span id="risk-province">广西壮族自治区</span></div>
                            <div class="info-item"><strong style="color: #3498db;">市/州名称：</strong><span id="risk-city">柳州市</span></div>
                            <div class="info-item"><strong style="color: #3498db;">公路类型：</strong><span id="risk-road-type">高速公路</span></div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">公路编号：</strong><span id="risk-road-number">G72</span></div>
                            <div class="info-item"><strong style="color: #3498db;">公路名称：</strong><span id="risk-road-name">泉南高速</span></div>
                            <div class="info-item"><strong style="color: #3498db;">起点桩号：</strong><span id="risk-start-stake">K1499+500</span></div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">止点桩号：</strong><span id="risk-end-stake">K1501+200</span></div>
                            <div class="info-item"><strong style="color: #3498db;">长度（km）：</strong><span id="risk-length">1.7</span></div>
                            <div class="info-item"><strong style="color: #3498db;">风险等级：</strong><span id="risk-level" style="color: #dc3545; font-weight: bold;">高风险</span></div>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">风险点描述：</strong><span id="risk-description">吴家屯隧道（柳州端）洞口上方山体存在塌方隐患，岩体松动，雨季易发生滑坡</span></div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">是否已采取措施：</strong><span id="risk-has-measures">是</span></div>
                            <div class="info-item"><strong style="color: #3498db;">填报时间：</strong><span id="risk-report-time">2024-05-15 09:30</span></div>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">已（拟）采取的措施：</strong><span id="risk-taken-measures">1. 设置警示标志；2. 限制大型车辆通行；3. 加强监测；4. 制定应急预案</span></div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">省级责任单位及人员：</strong><span id="risk-province-responsible">广西交通运输厅 / 张厅长 / 13907711001</span></div>
                            <div class="info-item"><strong style="color: #3498db;">复核责任单位及人员：</strong><span id="risk-review-responsible">柳州市交通运输局 / 李局长 / 13907712002</span></div>
                            <div class="info-item"><strong style="color: #3498db;">排查责任单位及人员：</strong><span id="risk-inspect-responsible">柳州高速公路管理处 / 王处长 / 13907713003</span></div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">现场照片/附件：</strong><span id="risk-photos">隧道口照片.jpg, 山体状况.jpg</span></div>
                            <div class="info-item"><strong style="color: #3498db;">措施附件：</strong><span id="risk-measure-files">应急预案.pdf, 监测方案.pdf</span></div>
                        </div>
                    </div>
                </div>

                <!-- 整改信息 -->
                <div class="info-section" style="margin-bottom: 25px;">
                    <h4 style="color: #3498db; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px; font-size: 18px;">整改信息</h4>
                    <div style="background-color: #2c3e50; padding: 20px; border-radius: 8px; border: 1px solid #4a5f7a;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">是否整改：</strong><span id="risk-is-fixed">整改中</span></div>
                            <div class="info-item"><strong style="color: #3498db;">整改完成时间：</strong><span id="risk-fix-complete-time">-</span></div>
                            <div class="info-item"><strong style="color: #3498db;">要求完成时间：</strong><span id="risk-fix-required-time">2024-08-30</span></div>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">整改措施：</strong><span id="risk-fix-measures">1. 山体加固工程；2. 排水系统完善；3. 监测设备安装；4. 应急通道建设</span></div>
                        </div>
                        <div>
                            <div class="info-item"><strong style="color: #3498db;">证明附件：</strong><span id="risk-proof-files">整改方案.pdf, 施工图纸.dwg</span></div>
                        </div>
                    </div>
                </div>

                <!-- 附近应急物资储备 -->
                <div class="info-section" style="margin-bottom: 25px;">
                    <h4 style="color: #3498db; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px; font-size: 18px;">附近应急物资储备</h4>
                    <div style="background-color: #2c3e50; padding: 20px; border-radius: 8px; border: 1px solid #4a5f7a;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">物资点名称：</strong><span id="supply-point-name">柳州市交通应急仓库</span></div>
                            <div class="info-item"><strong style="color: #3498db;">地址：</strong><span id="supply-point-address">柳州市柳江区G72高速服务区</span></div>
                            <div class="info-item"><strong style="color: #3498db;">距离：</strong><span id="supply-point-distance">12公里</span></div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">负责人：</strong><span id="supply-point-manager">赵仓管</span></div>
                            <div class="info-item"><strong style="color: #3498db;">联系方式：</strong><span id="supply-point-contact">13907714001</span></div>
                            <div class="info-item"><strong style="color: #3498db;">更新时间：</strong><span id="supply-point-update">2024-05-20</span></div>
                        </div>
                        <div>
                            <div class="info-item"><strong style="color: #3498db;">物资列表：</strong></div>
                            <table style="width: 100%; border-collapse: collapse; margin-top: 10px; border: 1px solid #4a5f7a;">
                                <thead>
                                    <tr style="background-color: #34495e;">
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">物资名称</th>
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">数量</th>
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">单位</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">单柱式标志牌</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">20</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">套</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">反光路锥</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">100</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">个</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">防撞水马</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">50</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">个</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">平板拖车5吨</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">2</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">托吊型清障车48吨</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">1</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">移动式应急发电机</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">3</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">高空作业车</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">1</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 附近救援队伍 -->
                <div class="info-section" style="margin-bottom: 25px;">
                    <h4 style="color: #3498db; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px; font-size: 18px;">附近救援队伍</h4>
                    <div style="background-color: #2c3e50; padding: 20px; border-radius: 8px; border: 1px solid #4a5f7a;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">名称：</strong><span id="rescue-team-name">柳州市交通应急救援队</span></div>
                            <div class="info-item"><strong style="color: #3498db;">地址：</strong><span id="rescue-team-address">柳州市城中区文昌路88号</span></div>
                            <div class="info-item"><strong style="color: #3498db;">距离：</strong><span id="rescue-team-distance">15公里</span></div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">负责人：</strong><span id="rescue-team-manager">李队长</span></div>
                            <div class="info-item"><strong style="color: #3498db;">联系方式：</strong><span id="rescue-team-contact">13907715001</span></div>
                            <div class="info-item"><strong style="color: #3498db;">更新时间：</strong><span id="rescue-team-update">2024-05-20</span></div>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">队伍人数：</strong><span id="rescue-team-members">35人</span></div>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">装备列表：</strong></div>
                            <table style="width: 100%; border-collapse: collapse; margin-top: 10px; border: 1px solid #4a5f7a;">
                                <thead>
                                    <tr style="background-color: #34495e;">
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">装备名称</th>
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">数量</th>
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">单位</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">挖掘机</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">3</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">装载机</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">2</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">推土机</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">1</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">排水抢险车</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">2</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">运输车</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">5</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">吊车</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">2</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">应急照明车</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">3</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div>
                            <div class="info-item"><strong style="color: #3498db;">物资列表：</strong></div>
                            <table style="width: 100%; border-collapse: collapse; margin-top: 10px; border: 1px solid #4a5f7a;">
                                <thead>
                                    <tr style="background-color: #34495e;">
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">物资名称</th>
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">数量</th>
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">单位</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">砂石料</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">500</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">吨</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">编织袋</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">2000</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">个</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">水泥</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">100</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">吨</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">防水布</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">1000</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">m</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">钢板</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">50</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">吨</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- 项目点详情模态框 -->
    <div id="project-details-modal" class="modal" style="display: none; position: fixed; z-index: 10000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.7);">
        <div class="modal-content" style="background-color: #2c3e50; margin: 3% auto; padding: 0; border-radius: 8px; width: 90%; max-width: 1200px; max-height: 90vh; overflow-y: auto; box-shadow: 0 4px 20px rgba(0,0,0,0.5); border: 1px solid #34495e;">
            <div class="modal-header" style="background-color: #34495e; color: #ecf0f1; padding: 20px; border-radius: 8px 8px 0 0; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #4a5f7a;">
                <h3 id="project-modal-title" style="margin: 0; font-size: 20px; color: #3498db;">在建项目驻地名称：上峒路№JL1总监办</h3>
                <span class="close-button" style="color: #ecf0f1; font-size: 28px; font-weight: bold; cursor: pointer; line-height: 1;" onclick="closeProjectModal()">&times;</span>
            </div>
            <div class="modal-body" style="padding: 25px; background-color: #2c3e50; color: #ecf0f1; font-size: 15px;">
                <!-- 基本信息 -->
                <div class="info-section" style="margin-bottom: 25px;">
                    <h4 style="color: #3498db; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px; font-size: 18px;">基本信息</h4>
                    <div style="background-color: #2c3e50; padding: 20px; border-radius: 8px; border: 1px solid #4a5f7a;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">序号：</strong><span id="project-sequence">1</span></div>
                            <div class="info-item"><strong style="color: #3498db;">驻地名称：</strong><span id="project-site-name">上峒路№JL1 总监办</span></div>
                            <div class="info-item"><strong style="color: #3498db;">驻地类型：</strong><span id="project-site-type">总监办驻地</span></div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">坐标点位：</strong><span id="project-coordinates">107.9874；22.1502</span></div>
                            <div class="info-item"><strong style="color: #3498db;">所属项目名称：</strong><span id="project-name">桂林龙胜（湘桂界）至峒中公路（上思至峒中口岸段）</span></div>
                            <div class="info-item"><strong style="color: #3498db;">项目类型：</strong><span id="project-type">高速公路</span></div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">建设单位：</strong><span id="project-builder">广西新发展交通集团有限公司</span></div>
                            <div class="info-item"><strong style="color: #3498db;">施工单位：</strong><span id="project-contractor">广西路桥工程集团有限公司</span></div>
                            <div class="info-item"><strong style="color: #3498db;">驻地地址：</strong><span id="project-site-address">广西壮族自治区防城港市上思县思阳镇 5# 主路</span></div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">行政区域：</strong><span id="project-admin-area">广西壮族自治区防城港市上思县思阳镇</span></div>
                            <div class="info-item"><strong style="color: #3498db;">驻地人数：</strong><span id="project-site-people">13</span></div>
                            <div class="info-item"><strong style="color: #3498db;">驻地风险等级：</strong><span id="project-risk-level" style="color: #28a745; font-weight: bold;">低风险 (Ⅰ 级)</span></div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">房建类型：</strong><span id="project-building-type">板房</span></div>
                            <div class="info-item"><strong style="color: #3498db;">主管部门：</strong><span id="project-department">无</span></div>
                            <div class="info-item"><strong style="color: #3498db;">是否排查：</strong><span id="project-inspected">否</span></div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">是否属于临水、临崖、涉洪区域：</strong><span id="project-water-cliff">否</span></div>
                            <div class="info-item"><strong style="color: #3498db;">是否属于易垮塌区域：</strong><span id="project-collapse-area">否</span></div>
                            <div class="info-item"><strong style="color: #3498db;">是否搬迁：</strong><span id="project-relocated">否</span></div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">哨 / 联系人：</strong><span id="project-guard-contact">洪深山 / 17352867199</span></div>
                            <div class="info-item"><strong style="color: #3498db;">建设单位包保责任：</strong><span id="project-builder-responsibility">陈林 / 13875867713</span></div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">施工单位包保责任：</strong><span id="project-contractor-responsibility">无</span></div>
                            <div class="info-item"><strong style="color: #3498db;">驻地现场包保责任：</strong><span id="project-site-responsibility">洪深山 / 17352867199</span></div>
                            <div class="info-item"><strong style="color: #3498db;">省级包保联系人：</strong><span id="project-province-contact">陈大勇 / 1387580331</span></div>
                        </div>
                    </div>
                </div>

                <!-- 相关风险隐患 -->
                <div class="info-section" style="margin-bottom: 25px;">
                    <h4 style="color: #3498db; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px; font-size: 18px;">相关风险隐患</h4>
                    <div style="background-color: #2c3e50; padding: 20px; border-radius: 8px; border: 1px solid #4a5f7a;">
                        <div style="margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">关联风险隐患：</strong><span id="project-related-risks">泉南高速吴家屯隧道（柳州端）洞口上方山体塌方隐患</span></div>
                        </div>
                        <div style="text-align: center;">
                            <button onclick="viewRelatedRisk()" style="background-color: #e74c3c; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">查看风险详情</button>
                        </div>
                    </div>
                </div>

                <!-- 附近应急物资 -->
                <div class="info-section" style="margin-bottom: 25px;">
                    <h4 style="color: #3498db; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px; font-size: 18px;">附近应急物资</h4>
                    <div style="background-color: #2c3e50; padding: 20px; border-radius: 8px; border: 1px solid #4a5f7a;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">物资点名称：</strong><span id="project-supply-name">上思县交通应急物资储备点</span></div>
                            <div class="info-item"><strong style="color: #3498db;">地址：</strong><span id="project-supply-address">防城港市上思县思阳镇工业园区</span></div>
                            <div class="info-item"><strong style="color: #3498db;">距离：</strong><span id="project-supply-distance">8公里</span></div>
                        </div>
                        <div style="text-align: center; margin-top: 15px;">
                            <button onclick="viewProjectSupplyDetails()" style="background-color: #3498db; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">查看物资详情</button>
                        </div>
                    </div>
                </div>

                <!-- 附近救援队伍 -->
                <div class="info-section" style="margin-bottom: 25px;">
                    <h4 style="color: #3498db; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px; font-size: 18px;">附近救援队伍</h4>
                    <div style="background-color: #2c3e50; padding: 20px; border-radius: 8px; border: 1px solid #4a5f7a;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">队伍名称：</strong><span id="project-rescue-name">防城港市交通应急救援队</span></div>
                            <div class="info-item"><strong style="color: #3498db;">地址：</strong><span id="project-rescue-address">防城港市港口区渔万路168号</span></div>
                            <div class="info-item"><strong style="color: #3498db;">距离：</strong><span id="project-rescue-distance">25公里</span></div>
                        </div>
                        <div style="text-align: center; margin-top: 15px;">
                            <button onclick="viewProjectRescueDetails()" style="background-color: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">查看救援详情</button>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- 物资详情模态框 -->
    <div id="supply-details-modal" class="modal" style="display: none; position: fixed; z-index: 10000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.7);">
        <div class="modal-content" style="background-color: #2c3e50; margin: 3% auto; padding: 0; border-radius: 8px; width: 90%; max-width: 1200px; max-height: 90vh; overflow-y: auto; box-shadow: 0 4px 20px rgba(0,0,0,0.5); border: 1px solid #34495e;">
            <div class="modal-header" style="background-color: #34495e; color: #ecf0f1; padding: 20px; border-radius: 8px 8px 0 0; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #4a5f7a;">
                <div style="display: flex; align-items: center; gap: 15px;">
                    <button onclick="returnToProjectModal()" style="background-color: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; font-size: 14px;">
                        <i class="fas fa-arrow-left"></i> 返回项目详情
                    </button>
                    <h3 style="margin: 0; font-size: 20px; color: #3498db;">附近应急物资储备详情</h3>
                </div>
                <span class="close-button" style="color: #ecf0f1; font-size: 28px; font-weight: bold; cursor: pointer; line-height: 1;" onclick="closeSupplyModal()">&times;</span>
            </div>
            <div class="modal-body" style="padding: 25px; background-color: #2c3e50; color: #ecf0f1; font-size: 15px;">
                <!-- 附近应急物资储备 -->
                <div class="info-section" style="margin-bottom: 25px;">
                    <h4 style="color: #3498db; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px; font-size: 18px;">附近应急物资储备</h4>
                    <div style="background-color: #2c3e50; padding: 20px; border-radius: 8px; border: 1px solid #4a5f7a;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">物资点名称：</strong><span id="supply-modal-name">上思县交通应急物资储备点</span></div>
                            <div class="info-item"><strong style="color: #3498db;">地址：</strong><span id="supply-modal-address">防城港市上思县思阳镇工业园区</span></div>
                            <div class="info-item"><strong style="color: #3498db;">距离：</strong><span id="supply-modal-distance">8公里</span></div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">负责人：</strong><span id="supply-modal-manager">赵仓管</span></div>
                            <div class="info-item"><strong style="color: #3498db;">联系方式：</strong><span id="supply-modal-contact">13907714001</span></div>
                            <div class="info-item"><strong style="color: #3498db;">更新时间：</strong><span id="supply-modal-update">2024-05-20</span></div>
                        </div>
                        <div>
                            <div class="info-item"><strong style="color: #3498db;">物资列表：</strong></div>
                            <table style="width: 100%; border-collapse: collapse; margin-top: 10px; border: 1px solid #4a5f7a;">
                                <thead>
                                    <tr style="background-color: #34495e;">
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">物资名称</th>
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">数量</th>
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">单位</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">单柱式标志牌</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">20</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">套</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">反光路锥</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">100</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">个</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">防撞水马</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">50</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">个</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">平板拖车5吨</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">2</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">托吊型清障车48吨</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">1</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">移动式应急发电机</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">3</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">高空作业车</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">1</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 救援队伍详情模态框 -->
    <div id="rescue-details-modal" class="modal" style="display: none; position: fixed; z-index: 10000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.7);">
        <div class="modal-content" style="background-color: #2c3e50; margin: 3% auto; padding: 0; border-radius: 8px; width: 90%; max-width: 1200px; max-height: 90vh; overflow-y: auto; box-shadow: 0 4px 20px rgba(0,0,0,0.5); border: 1px solid #34495e;">
            <div class="modal-header" style="background-color: #34495e; color: #ecf0f1; padding: 20px; border-radius: 8px 8px 0 0; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #4a5f7a;">
                <div style="display: flex; align-items: center; gap: 15px;">
                    <button onclick="returnToProjectModal()" style="background-color: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; font-size: 14px;">
                        <i class="fas fa-arrow-left"></i> 返回项目详情
                    </button>
                    <h3 style="margin: 0; font-size: 20px; color: #3498db;">附近救援队伍详情</h3>
                </div>
                <span class="close-button" style="color: #ecf0f1; font-size: 28px; font-weight: bold; cursor: pointer; line-height: 1;" onclick="closeRescueModal()">&times;</span>
            </div>
            <div class="modal-body" style="padding: 25px; background-color: #2c3e50; color: #ecf0f1; font-size: 15px;">
                <!-- 附近救援队伍 -->
                <div class="info-section" style="margin-bottom: 25px;">
                    <h4 style="color: #3498db; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px; font-size: 18px;">附近救援队伍</h4>
                    <div style="background-color: #2c3e50; padding: 20px; border-radius: 8px; border: 1px solid #4a5f7a;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">名称：</strong><span id="rescue-modal-name">防城港市交通应急救援队</span></div>
                            <div class="info-item"><strong style="color: #3498db;">地址：</strong><span id="rescue-modal-address">防城港市港口区渔万路168号</span></div>
                            <div class="info-item"><strong style="color: #3498db;">距离：</strong><span id="rescue-modal-distance">25公里</span></div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">负责人：</strong><span id="rescue-modal-manager">李队长</span></div>
                            <div class="info-item"><strong style="color: #3498db;">联系方式：</strong><span id="rescue-modal-contact">13907715001</span></div>
                            <div class="info-item"><strong style="color: #3498db;">更新时间：</strong><span id="rescue-modal-update">2024-05-20</span></div>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">队伍人数：</strong><span id="rescue-modal-members">35人</span></div>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <div class="info-item"><strong style="color: #3498db;">装备列表：</strong></div>
                            <table style="width: 100%; border-collapse: collapse; margin-top: 10px; border: 1px solid #4a5f7a;">
                                <thead>
                                    <tr style="background-color: #34495e;">
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">装备名称</th>
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">数量</th>
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">单位</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">挖掘机</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">3</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">装载机</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">2</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">推土机</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">1</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">排水抢险车</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">2</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">运输车</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">5</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">吊车</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">2</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">应急照明车</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">3</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">台</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div>
                            <div class="info-item"><strong style="color: #3498db;">物资列表：</strong></div>
                            <table style="width: 100%; border-collapse: collapse; margin-top: 10px; border: 1px solid #4a5f7a;">
                                <thead>
                                    <tr style="background-color: #34495e;">
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">物资名称</th>
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">数量</th>
                                        <th style="border: 1px solid #4a5f7a; padding: 8px; text-align: left; color: #3498db;">单位</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">砂石料</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">500</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">吨</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">编织袋</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">2000</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">个</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">水泥</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">100</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">吨</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">防水布</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">1000</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">m</td>
                                    </tr>
                                    <tr>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">钢板</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">50</td>
                                        <td style="border: 1px solid #4a5f7a; padding: 8px;">吨</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 风险一张图专用JavaScript -->
    <script>
        // 全局变量，用于跟踪当前显示的标点类型
        let currentMarkerType = 'risks'; // 'risks' 或 'projects'

        // 调试函数，用于查看标点的显示状态
        function debugMarkers() {
            console.log('===== 标点显示状态调试 =====');
            console.log('当前标点类型:', currentMarkerType);

            // 检查风险隐患点
            const riskMarkers = document.querySelectorAll('.risk-marker');
            console.log('风险隐患点数量:', riskMarkers.length);
            let visibleRiskMarkers = 0;
            riskMarkers.forEach(marker => {
                if (marker.style.display !== 'none') {
                    visibleRiskMarkers++;
                }
            });
            console.log('可见风险隐患点数量:', visibleRiskMarkers);

            // 检查在建项目点
            const projectMarkers = document.querySelectorAll('.project-marker');
            console.log('在建项目点数量:', projectMarkers.length);
            let visibleProjectMarkers = 0;
            projectMarkers.forEach(marker => {
                if (marker.style.display !== 'none') {
                    visibleProjectMarkers++;
                }
            });
            console.log('可见在建项目点数量:', visibleProjectMarkers);

            // 检查全选复选框状态
            const checkbox = document.getElementById('res-type-all');
            console.log('全选复选框状态:', checkbox ? checkbox.checked : '未找到');

            console.log('===== 调试结束 =====');
        }

        // 全选/全不选复选框的处理函数
        function toggleAllMarkers() {
            const checkbox = document.getElementById('res-type-all');
            const isChecked = checkbox.checked;
            console.log('全选/全不选复选框状态:', isChecked);

            if (currentMarkerType === 'risks') {
                // 如果当前是风险隐患点
                document.querySelectorAll('.risk-marker').forEach(marker => {
                    marker.style.display = isChecked ? 'block' : 'none';
                    marker.style.visibility = isChecked ? 'visible' : 'hidden';
                    marker.style.opacity = isChecked ? '1' : '0';
                    console.log('风险一张图标点显示(全选):', marker.getAttribute('data-id'));
                });
            } else if (currentMarkerType === 'projects') {
                // 如果当前是在建项目
                document.querySelectorAll('.project-marker').forEach(marker => {
                    marker.style.display = isChecked ? 'block' : 'none';
                    marker.style.visibility = isChecked ? 'visible' : 'hidden';
                    marker.style.opacity = isChecked ? '1' : '0';
                    console.log('风险一张图项目点显示(全选):', marker.getAttribute('data-id'));
                });
            }
        }

        // 简单直接的函数，显示风险隐患点内容
        function showRiskHazards() {
            console.log('显示风险隐患点内容');
            // 直接调用紧急修复函数
            showAllRiskMarkers();
        }

        // 简单直接的函数，显示在建项目内容
        function showConstructionProjects() {
            console.log('显示在建项目内容');
            // 直接调用紧急修复函数
            showAllProjectMarkers();
        }

        // 紧急修复函数 - 显示所有风险隐患点
        function showAllRiskMarkers() {
            console.log('===== 开始显示风险隐患点 =====');

            // 检查风险隐患点数量
            const riskMarkers = document.querySelectorAll('.risk-marker');
            console.log('风险隐患点总数:', riskMarkers.length);

            // 检查在建项目点数量
            const projectMarkers = document.querySelectorAll('.project-marker');
            console.log('在建项目点总数:', projectMarkers.length);

            // 强制显示所有风险隐患点
            riskMarkers.forEach((marker, index) => {
                marker.style.display = 'block';
                marker.style.visibility = 'visible';
                marker.style.opacity = '1';
                console.log(`风险点 ${index+1} (${marker.getAttribute('data-id')}) 设置为显示`);
            });

            // 强制隐藏所有在建项目点
            projectMarkers.forEach((marker, index) => {
                marker.style.display = 'none';
                console.log(`项目点 ${index+1} (${marker.getAttribute('data-id')}) 设置为隐藏`);
            });

            console.log('已手动显示所有风险隐患点');

            // 更新全局变量
            currentMarkerType = 'risks';

            // 更新按钮状态
            document.querySelectorAll('.resource-type-tabs .resource-tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector('.resource-type-tabs .resource-tab-button:first-child').classList.add('active');

            // 显示风险隐患内容，隐藏在建项目内容
            document.getElementById('risk-hazards-content').style.display = 'block';
            document.getElementById('construction-projects-content').style.display = 'none';

            // 确保全选复选框为选中状态
            const allTypesCheckbox = document.getElementById('res-type-all');
            if (allTypesCheckbox) {
                allTypesCheckbox.checked = true;
            }

            // 再次检查风险隐患点显示状态
            let visibleRiskMarkers = 0;
            riskMarkers.forEach(marker => {
                if (marker.style.display !== 'none') {
                    visibleRiskMarkers++;
                }
            });
            console.log('可见风险隐患点数量:', visibleRiskMarkers);

            console.log('===== 结束显示风险隐患点 =====');
        }

        // 紧急修复函数 - 显示所有在建项目点
        function showAllProjectMarkers() {
            console.log('===== 开始显示在建项目点 =====');

            // 检查风险隐患点数量
            const riskMarkers = document.querySelectorAll('.risk-marker');
            console.log('风险隐患点总数:', riskMarkers.length);

            // 检查在建项目点数量
            const projectMarkers = document.querySelectorAll('.project-marker');
            console.log('在建项目点总数:', projectMarkers.length);

            // 强制隐藏所有风险隐患点
            riskMarkers.forEach((marker, index) => {
                marker.style.display = 'none';
                console.log(`风险点 ${index+1} (${marker.getAttribute('data-id')}) 设置为隐藏`);
            });

            // 强制显示所有在建项目点
            projectMarkers.forEach((marker, index) => {
                marker.style.display = 'block';
                marker.style.visibility = 'visible';
                marker.style.opacity = '1';
                console.log(`项目点 ${index+1} (${marker.getAttribute('data-id')}) 设置为显示`);
            });

            console.log('已手动显示所有在建项目点');

            // 更新全局变量
            currentMarkerType = 'projects';

            // 更新按钮状态 - 修复标签卡高亮问题
            const tabButtons = document.querySelectorAll('.resource-type-tabs .resource-tab-button');
            tabButtons.forEach(btn => {
                btn.classList.remove('active');
            });

            // 确保在建项目标签卡高亮
            const allButtons = document.querySelectorAll('.resource-tab-button');
            for (let i = 0; i < allButtons.length; i++) {
                if (allButtons[i].textContent.includes('在建项目')) {
                    allButtons[i].classList.add('active');
                    break;
                }
            }

            // 隐藏风险隐患内容，显示在建项目内容
            document.getElementById('risk-hazards-content').style.display = 'none';
            document.getElementById('construction-projects-content').style.display = 'block';

            // 确保全选复选框为选中状态
            const allTypesCheckbox = document.getElementById('res-type-all');
            if (allTypesCheckbox) {
                allTypesCheckbox.checked = true;
            }

            // 再次检查在建项目点显示状态
            let visibleProjectMarkers = 0;
            projectMarkers.forEach(marker => {
                if (marker.style.display !== 'none') {
                    visibleProjectMarkers++;
                }
            });
            console.log('可见在建项目点数量:', visibleProjectMarkers);

            console.log('===== 结束显示在建项目点 =====');
        }

        // 筛选标签切换函数
        function switchFilterTab(button, tabType) {
            // 移除所有按钮的active类
            const parentContainer = button.closest('.filter-tabs');
            if (parentContainer) {
                const siblingButtons = parentContainer.querySelectorAll('.filter-tab-button');
                siblingButtons.forEach(btn => btn.classList.remove('active'));
            }

            // 添加当前按钮的active类
            button.classList.add('active');

            // 获取目标内容ID
            const targetTabContentId = `filter-by-${tabType}-content`;

            // 获取相关的内容元素
            const parentSection = button.closest('.resource-condition-filter');
            if (parentSection) {
                const contentElements = parentSection.querySelectorAll('.filter-tab-content');

                contentElements.forEach(content => {
                    if (content.id === targetTabContentId) {
                        content.classList.add('active');
                    } else {
                        content.classList.remove('active');
                    }
                });
            }
        }

        // 告警标签切换函数
        function switchAlertTab(button, tabType) {
            console.log('切换告警标签:', tabType);

            // 移除所有按钮的active类并重置样式
            const parentContainer = button.closest('.alert-tabs');
            if (parentContainer) {
                const siblingButtons = parentContainer.querySelectorAll('.alert-tab-button');
                siblingButtons.forEach(btn => {
                    btn.classList.remove('active');
                    btn.style.borderBottom = '2px solid transparent';
                    btn.style.color = '#666';
                    btn.style.fontWeight = 'normal';
                });
            }

            // 添加当前按钮的active类并设置样式
            button.classList.add('active');
            button.style.borderBottom = '2px solid #007bff';
            button.style.color = '#333';
            button.style.fontWeight = 'bold';

            // 获取目标内容ID
            const targetTabContentId = `alert-${tabType}-content`;

            // 获取相关的内容元素
            const parentSection = button.closest('.alert-list-container');
            if (parentSection) {
                const contentElements = parentSection.querySelectorAll('.alert-tab-content');

                contentElements.forEach(content => {
                    if (content.id === targetTabContentId) {
                        content.style.display = 'block';
                        content.classList.add('active');
                        console.log('显示内容:', content.id);
                    } else {
                        content.style.display = 'none';
                        content.classList.remove('active');
                        console.log('隐藏内容:', content.id);
                    }
                });
            }
        }

        // 简单直接的函数，显示风险隐患详情
        function showRiskHazardsDetails() {
            console.log('显示风险隐患详情');
            // 更新按钮状态
            document.querySelectorAll('.details-tabs .details-tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector('.details-tabs .details-tab-button:first-child').classList.add('active');

            // 显示风险隐患详情，隐藏在建项目详情
            var riskContent = document.getElementById('details-risk-hazards-content');
            var projectContent = document.getElementById('details-projects-content');

            if (riskContent) {
                riskContent.style.display = 'block';
                console.log('风险隐患详情显示为block');
            }

            if (projectContent) {
                projectContent.style.display = 'none';
                console.log('在建项目详情显示为none');
            }
        }

        // 简单直接的函数，显示在建项目详情
        function showProjectsDetails() {
            console.log('显示在建项目详情');
            // 更新按钮状态
            document.querySelectorAll('.details-tabs .details-tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector('.details-tabs .details-tab-button:last-child').classList.add('active');

            // 隐藏风险隐患详情，显示在建项目详情
            var riskContent = document.getElementById('details-risk-hazards-content');
            var projectContent = document.getElementById('details-projects-content');

            if (riskContent) {
                riskContent.style.display = 'none';
                console.log('风险隐患详情显示为none');
            }

            if (projectContent) {
                projectContent.style.display = 'block';
                console.log('在建项目详情显示为block');
            }
        }

        // 风险点模态框相关函数
        function showRiskDetails(riskId) {
            console.log('显示风险点详情:', riskId);

            // 隐藏返回按钮（直接点击风险点时不需要返回按钮）
            document.getElementById('risk-return-button').style.display = 'none';

            // 模拟风险点数据（实际应用中应从后端获取）
            const riskData = getRiskData(riskId);

            // 填充模态框数据 - 使用新的元素ID
            document.getElementById('risk-province').textContent = riskData.province || '广西壮族自治区';
            document.getElementById('risk-city').textContent = riskData.city || '柳州市';
            document.getElementById('risk-road-type').textContent = riskData.roadType || '高速公路';
            document.getElementById('risk-road-number').textContent = riskData.roadNumber || 'G72';
            document.getElementById('risk-road-name').textContent = riskData.roadName || '泉南高速';
            document.getElementById('risk-start-stake').textContent = riskData.startStake || 'K1499+500';
            document.getElementById('risk-end-stake').textContent = riskData.endStake || 'K1501+200';
            document.getElementById('risk-length').textContent = riskData.length || '1.7';
            document.getElementById('risk-level').textContent = '高风险';
            document.getElementById('risk-level').style.color = '#dc3545';
            document.getElementById('risk-description').textContent = riskData.description;
            document.getElementById('risk-has-measures').textContent = riskData.hasMeasures || '是';
            document.getElementById('risk-report-time').textContent = riskData.foundTime;
            document.getElementById('risk-taken-measures').textContent = riskData.measures;
            document.getElementById('risk-province-responsible').textContent = riskData.provinceResponsible || '广西交通运输厅 / 张厅长 / 13907711001';
            document.getElementById('risk-review-responsible').textContent = riskData.reviewResponsible || '柳州市交通运输局 / 李局长 / 13907712002';
            document.getElementById('risk-inspect-responsible').textContent = riskData.inspectResponsible || '柳州高速公路管理处 / 王处长 / 13907713003';
            document.getElementById('risk-photos').textContent = riskData.photos || '隧道口照片.jpg, 山体状况.jpg';
            document.getElementById('risk-measure-files').textContent = riskData.measureFiles || '应急预案.pdf, 监测方案.pdf';

            // 整改信息
            document.getElementById('risk-is-fixed').textContent = riskData.status;
            document.getElementById('risk-fix-complete-time').textContent = riskData.fixCompleteTime || '-';
            document.getElementById('risk-fix-required-time').textContent = riskData.deadline;
            document.getElementById('risk-fix-measures').textContent = riskData.fixMeasures || '1. 山体加固工程；2. 排水系统完善；3. 监测设备安装；4. 应急通道建设';
            document.getElementById('risk-proof-files').textContent = riskData.proofFiles || '整改方案.pdf, 施工图纸.dwg';

            // 显示模态框
            document.getElementById('risk-details-modal').style.display = 'block';
        }

        function closeRiskModal() {
            document.getElementById('risk-details-modal').style.display = 'none';
        }

        function notifyUnits() {
            alert('通知相关单位功能开发中，敬请期待！');
        }

        // 项目点模态框相关函数
        function showProjectDetails(projectId) {
            console.log('显示项目点详情:', projectId);

            // 记住当前项目ID
            currentProjectId = projectId;

            // 模拟项目点数据（实际应用中应从后端获取）
            const projectData = getProjectData(projectId);

            // 填充模态框数据 - 使用新的元素ID
            document.getElementById('project-sequence').textContent = projectData.sequence || '1';
            document.getElementById('project-site-name').textContent = projectData.siteName || '上峒路№JL1 总监办';
            document.getElementById('project-site-type').textContent = projectData.siteType || '总监办驻地';
            document.getElementById('project-coordinates').textContent = projectData.coordinates;
            document.getElementById('project-name').textContent = projectData.name;
            document.getElementById('project-type').textContent = projectData.type;
            document.getElementById('project-builder').textContent = projectData.builder;
            document.getElementById('project-contractor').textContent = projectData.contractor;
            document.getElementById('project-site-address').textContent = projectData.siteAddress || '广西壮族自治区防城港市上思县思阳镇 5# 主路';
            document.getElementById('project-admin-area').textContent = projectData.adminArea || '广西壮族自治区防城港市上思县思阳镇';
            document.getElementById('project-site-people').textContent = projectData.sitePeople || '13';
            document.getElementById('project-risk-level').textContent = projectData.riskLevel || '低风险 (Ⅰ 级)';
            document.getElementById('project-building-type').textContent = projectData.buildingType || '板房';
            document.getElementById('project-department').textContent = projectData.department || '无';
            document.getElementById('project-inspected').textContent = projectData.inspected || '否';
            document.getElementById('project-water-cliff').textContent = projectData.waterCliff || '否';
            document.getElementById('project-collapse-area').textContent = projectData.collapseArea || '否';
            document.getElementById('project-relocated').textContent = projectData.relocated || '否';
            document.getElementById('project-guard-contact').textContent = projectData.guardContact || '洪深山 / 17352867199';
            document.getElementById('project-builder-responsibility').textContent = projectData.builderResponsibility || '陈林 / 13875867713';
            document.getElementById('project-contractor-responsibility').textContent = projectData.contractorResponsibility || '无';
            document.getElementById('project-site-responsibility').textContent = projectData.siteResponsibility || '洪深山 / 17352867199';
            document.getElementById('project-province-contact').textContent = projectData.provinceContact || '陈大勇 / 1387580331';

            // 显示模态框
            document.getElementById('project-details-modal').style.display = 'block';
        }

        function closeProjectModal() {
            document.getElementById('project-details-modal').style.display = 'none';
        }

        function viewProjectProgress() {
            alert('查看项目进度详情功能开发中，敬请期待！');
        }

        // 新增的跳转和返回功能
        function viewSupplyDetails() {
            alert('查看物资详情功能开发中，敬请期待！');
        }

        function viewRescueDetails() {
            alert('查看救援队伍功能开发中，敬请期待！');
        }

        function viewRelatedRisk() {
            // 关闭项目模态框，打开风险模态框
            closeProjectModal();
            showRiskDetails('risk001'); // 显示关联的风险详情
            // 显示返回按钮
            document.getElementById('risk-return-button').style.display = 'block';
        }

        function returnFromRisk() {
            // 关闭风险模态框，返回项目模态框
            closeRiskModal();
            showProjectDetails('project001'); // 返回项目详情
        }

        // 全局变量，用于记住当前的项目ID
        let currentProjectId = null;

        function viewProjectSupplyDetails() {
            console.log('显示项目物资详情');
            // 关闭项目模态框
            closeProjectModal();
            // 显示物资详情模态框
            document.getElementById('supply-details-modal').style.display = 'block';
        }

        function viewProjectRescueDetails() {
            console.log('显示项目救援详情');
            // 关闭项目模态框
            closeProjectModal();
            // 显示救援详情模态框
            document.getElementById('rescue-details-modal').style.display = 'block';
        }

        function closeSupplyModal() {
            document.getElementById('supply-details-modal').style.display = 'none';
        }

        function closeRescueModal() {
            document.getElementById('rescue-details-modal').style.display = 'none';
        }

        function returnToProjectModal() {
            // 关闭当前模态框
            closeSupplyModal();
            closeRescueModal();
            closeRiskModal();
            // 重新显示项目模态框
            if (currentProjectId) {
                showProjectDetails(currentProjectId);
            }
        }

        // 模拟风险点数据获取函数
        function getRiskData(riskId) {
            const riskDataMap = {
                'risk001': {
                    id: 'RISK-2024-001',
                    level: '低风险',
                    levelColor: '#28a745',
                    type: '边坡稳定性',
                    road: 'G72泉南高速',
                    unit: '玉林市交通运输局',
                    foundTime: '2024-05-15 09:30',
                    location: '玉林市玉州区G72高速K1499+500处',
                    stake: 'K1499+500-K1499+600',
                    coordinates: '110.1234°E, 22.6789°N',
                    impact: '单向车道，影响通行效率',
                    status: '整改中',
                    statusColor: '#ffc107',
                    deadline: '2024-06-30',
                    description: '该路段边坡存在轻微松动现象，雨季时可能出现小规模滑坡，需要加强监测和防护。目前已设置警示标志，限制大型车辆通行。',
                    measures: '1. 设置边坡防护网；2. 加强排水系统；3. 定期巡查监测；4. 雨季加强值守；5. 制定应急预案。',
                    responsiblePerson: '张建国',
                    contactPhone: '13907712001'
                },
                'risk002': {
                    id: 'RISK-2024-002',
                    level: '低风险',
                    levelColor: '#28a745',
                    type: '排水设施',
                    road: 'G72泉南高速',
                    unit: '北海市交通运输局',
                    foundTime: '2024-05-12 14:20',
                    location: '北海市合浦县G72高速K1520+300处',
                    stake: 'K1520+300-K1520+400',
                    coordinates: '109.1234°E, 21.6789°N',
                    impact: '雨季排水不畅，可能积水',
                    status: '待整改',
                    statusColor: '#dc3545',
                    deadline: '2024-06-15',
                    description: '排水沟部分堵塞，雨季时排水不畅，可能导致路面积水，影响行车安全。',
                    measures: '1. 清理排水沟；2. 更换损坏的排水管；3. 加强日常维护；4. 建立定期清理制度。',
                    responsiblePerson: '李明华',
                    contactPhone: '13907713002'
                },
                'risk003': {
                    id: 'RISK-2024-003',
                    level: '中风险',
                    levelColor: '#ffc107',
                    type: '桥梁结构',
                    road: 'G72泉南高速',
                    unit: '钦州市交通运输局',
                    foundTime: '2024-05-10 16:45',
                    location: '钦州市钦南区G72高速钦州大桥',
                    stake: 'K1600+200-K1600+800',
                    coordinates: '108.6234°E, 21.9789°N',
                    impact: '桥梁承载能力下降，限制重载车辆',
                    status: '整改中',
                    statusColor: '#ffc107',
                    deadline: '2024-07-15',
                    description: '桥梁伸缩缝出现异常，部分桥墩存在轻微裂缝，需要进行专业检测和维修加固。',
                    measures: '1. 桥梁结构检测；2. 伸缩缝更换；3. 桥墩裂缝修补；4. 限载通行；5. 加强监测。',
                    responsiblePerson: '王桥梁',
                    contactPhone: '13907713003'
                },
                'risk005': {
                    id: 'RISK-2024-005',
                    level: '高风险',
                    levelColor: '#dc3545',
                    type: '隧道安全',
                    road: 'G72泉南高速',
                    unit: '百色市交通运输局',
                    foundTime: '2024-05-08 11:20',
                    location: '百色市右江区G72高速百色隧道',
                    stake: 'K1750+100-K1752+300',
                    coordinates: '106.6234°E, 23.8789°N',
                    impact: '隧道通风系统故障，限速通行',
                    status: '紧急整改',
                    statusColor: '#dc3545',
                    deadline: '2024-06-01',
                    description: '隧道内通风设备出现故障，部分照明系统不稳定，存在安全隐患，需要紧急维修。',
                    measures: '1. 通风系统检修；2. 照明设备更换；3. 应急预案启动；4. 24小时值守；5. 限速通行。',
                    responsiblePerson: '陈隧道',
                    contactPhone: '13907714004'
                }
            };

            return riskDataMap[riskId] || {
                id: riskId,
                level: '未知',
                levelColor: '#6c757d',
                type: '未分类',
                road: '未知路段',
                unit: '未知单位',
                foundTime: '未知',
                location: '未知位置',
                stake: '未知',
                coordinates: '未知',
                impact: '未知',
                status: '未知',
                statusColor: '#6c757d',
                deadline: '未知',
                description: '暂无详细描述信息。',
                measures: '暂无应对措施信息。',
                responsiblePerson: '未知',
                contactPhone: '未知'
            };
        }

        // 模拟项目点数据获取函数
        function getProjectData(projectId) {
            const projectDataMap = {
                'project001': {
                    id: 'PROJ-2024-001',
                    name: '梧州大桥加固改造工程',
                    type: '桥梁工程',
                    builder: '广西交通建设集团',
                    contractor: '中建八局',
                    supervisor: '广西公路工程监理公司',
                    status: '施工中',
                    statusColor: '#ffc107',
                    startTime: '2024-03-15',
                    endTime: '2024-12-30',
                    progress: '65%',
                    investment: '2.8亿元',
                    riskStatus: '存在风险',
                    riskStatusColor: '#dc3545',
                    description: '对梧州大桥进行全面加固改造，包括桥墩加固、桥面重铺、护栏更换等工程，提升桥梁承载能力和使用寿命。',
                    location: '梧州市万秀区G72高速梧州大桥',
                    stake: 'K1580+200-K1582+800',
                    coordinates: '111.2979°E, 23.4745°N',
                    impact: '施工期间单向通行，通行能力下降50%',
                    manager: '王建设',
                    contactPhone: '13907714001'
                },
                'project002': {
                    id: 'PROJ-2024-002',
                    name: '北海港疏港公路边坡加固工程',
                    type: '边坡加固',
                    builder: '北海市交通运输局',
                    contractor: '广西路桥集团',
                    supervisor: '广西交通工程监理公司',
                    status: '施工中',
                    statusColor: '#ffc107',
                    startTime: '2024-04-01',
                    endTime: '2024-08-31',
                    progress: '45%',
                    investment: '1.2亿元',
                    riskStatus: '存在风险',
                    riskStatusColor: '#dc3545',
                    description: '对北海港疏港公路沿线边坡进行加固处理，防止雨季滑坡，确保道路安全通行。',
                    location: '北海市银海区疏港公路K15+300处',
                    stake: 'K15+300-K16+800',
                    coordinates: '109.1193°E, 21.4733°N',
                    impact: '施工期间限速通行，大型车辆绕行',
                    manager: '陈工程',
                    contactPhone: '13907715001'
                },
                'project003': {
                    id: 'PROJ-2024-003',
                    name: '崇左市G72高速路面改造工程',
                    type: '道路工程',
                    builder: '崇左市交通运输局',
                    contractor: '广西交通建设集团',
                    supervisor: '广西公路工程监理公司',
                    status: '已开工',
                    statusColor: '#28a745',
                    startTime: '2024-02-20',
                    endTime: '2024-10-15',
                    progress: '75%',
                    investment: '1.8亿元',
                    riskStatus: '无风险',
                    riskStatusColor: '#28a745',
                    description: '对G72高速崇左段进行路面大修，包括路面铣刨、重新铺装、标线施划等工程，提升道路通行质量。',
                    location: '崇左市江州区G72高速K1650+000处',
                    stake: 'K1650+000-K1655+000',
                    coordinates: '107.3534°E, 22.4041°N',
                    impact: '施工期间半幅通行，通行效率略有影响',
                    manager: '李路面',
                    contactPhone: '13907715002'
                },
                'project005': {
                    id: 'PROJ-2024-005',
                    name: '柳州市G72高速服务区扩建工程',
                    type: '道路工程',
                    builder: '柳州市交通运输局',
                    contractor: '广西建工集团',
                    supervisor: '广西交通工程监理公司',
                    status: '已开工',
                    statusColor: '#28a745',
                    startTime: '2024-01-10',
                    endTime: '2024-09-30',
                    progress: '80%',
                    investment: '0.8亿元',
                    riskStatus: '无风险',
                    riskStatusColor: '#28a745',
                    description: '对柳州服务区进行扩建改造，增加停车位、完善服务设施，提升服务水平。',
                    location: '柳州市柳江区G72高速柳州服务区',
                    stake: 'K1400+500-K1401+200',
                    coordinates: '109.4281°E, 24.3264°N',
                    impact: '施工期间服务区部分区域封闭',
                    manager: '赵服务',
                    contactPhone: '13907715003'
                }
            };

            return projectDataMap[projectId] || {
                id: projectId,
                name: '未知项目',
                type: '未知类型',
                builder: '未知单位',
                contractor: '未知单位',
                supervisor: '未知单位',
                status: '未知',
                statusColor: '#6c757d',
                startTime: '未知',
                endTime: '未知',
                progress: '未知',
                investment: '未知',
                riskStatus: '未知',
                riskStatusColor: '#6c757d',
                description: '暂无项目描述信息。',
                location: '未知位置',
                stake: '未知',
                coordinates: '未知',
                impact: '未知',
                manager: '未知',
                contactPhone: '未知'
            };
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('风险一张图页面已加载');

            // 默认显示风险隐患点
            showAllRiskMarkers();

            // 为所有风险隐患点添加点击事件
            document.querySelectorAll('.risk-marker').forEach(marker => {
                marker.addEventListener('click', function() {
                    const riskId = this.getAttribute('data-id');
                    showRiskDetails(riskId);
                });

                // 添加鼠标悬停效果
                marker.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.2)';
                    this.style.zIndex = '1001';
                });

                marker.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.zIndex = '1000';
                });
            });

            // 为所有在建项目点添加点击事件
            document.querySelectorAll('.project-marker').forEach(marker => {
                marker.addEventListener('click', function() {
                    const projectId = this.getAttribute('data-id');
                    showProjectDetails(projectId);
                });

                // 添加鼠标悬停效果
                marker.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.2)';
                    this.style.zIndex = '1001';
                });

                marker.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.zIndex = '1000';
                });
            });

            // 点击模态框外部关闭模态框
            window.addEventListener('click', function(event) {
                const riskModal = document.getElementById('risk-details-modal');
                const projectModal = document.getElementById('project-details-modal');
                const supplyModal = document.getElementById('supply-details-modal');
                const rescueModal = document.getElementById('rescue-details-modal');

                if (event.target === riskModal) {
                    closeRiskModal();
                }

                if (event.target === projectModal) {
                    closeProjectModal();
                }

                if (event.target === supplyModal) {
                    closeSupplyModal();
                }

                if (event.target === rescueModal) {
                    closeRescueModal();
                }
            });
        });

        // 初始化导航栏
        NavigationComponent.init('risk-map');
    </script>
</body>
</html>