<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>单位人员管理 - 应急管理系统</title>
  <!-- 引入样式 -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
  <!-- Removed common.css link -->
  <style>
    html, body {
        height: 100%;
        margin: 0;
    }
    body {
      font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
    }
    /* Removed old layout styles (.sidebar, .main-content, etc.) */

    /* 表格和状态标签样式 (Keep) */
    .status-badge {
      padding: 0.25rem 0.5rem;
      border-radius: 9999px;
      font-size: 0.75rem;
      font-weight: 600;
      display: inline-block;
    }
    .status-badge.active {
      background-color: #DEF7EC;
      color: #03543E;
    }
    .status-badge.leave {
      background-color: #FEF3C7;
      color: #92400E;
    }
    .status-badge.retired {
      background-color: #FEE2E2;
      color: #991B1B;
    }
    /* 自定义 Tree Select 样式 (Keep) */
    .el-tree-select {
        width: 100% !important;
    }
    .el-select-dropdown__wrap {
        max-height: 400px;
    }
    .el-tree-node__content {
        height: 32px;
    }
    .el-tree-node__label {
        font-size: 14px;
    }
  </style>
</head>
<body class="bg-gray-100 min-h-screen">
  <!-- Removed old sidebar -->
  
  <!-- Removed old content wrapper and header -->

  <!-- Navbar Placeholder -->
  <div id="navbar-placeholder"></div>

  <!-- Flex Container for Sidebar and Main Content -->
  <div class="flex-container h-full" style="display: flex;">
      <!-- Sidebar Placeholder -->
      <div id="sidebar-placeholder"></div>

      <!-- Main Content Area -->
      <main id="main-content" class="flex-grow pb-8 px-6 bg-gray-100">
          <div class="py-6">
              <!-- 页面标题 -->
              <div class="flex justify-between items-center mb-6">
                <div>
                  <h2 class="text-2xl font-semibold text-gray-800">人员列表</h2>
                  <p class="text-sm text-gray-500 mt-1">管理系统内的人员信息</p>
                </div>
                <button id="btnAdd" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  <i class="fas fa-plus mr-2"></i>添加人员
                </button>
              </div>

              <!-- 过滤栏 -->
              <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label for="orgFilter" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                    <div id="orgFilterApp">
                        <el-tree-select
                            v-model="selectedUnit"
                            :data="orgOptions"
                            :multiple="false"
                            :check-strictly="true"
                            :props="{ value: 'value', label: 'label', children: 'children' }"
                            placeholder="请选择单位"
                            class="block w-full"
                            clearable
                            @change="handleOrgChange"
                        />
                    </div>
                  </div>
                  <div>
                    <label for="filterDepartment" class="block text-sm font-medium text-gray-700 mb-1">部门</label>
                    <select id="filterDepartment" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                      <option value="">全部部门</option>
                      <option value="1-1">应急指挥科</option>
                      <option value="1-2">应急保障科</option>
                      <option value="2-1">特勤中队</option>
                    </select>
                  </div>
                  <div>
                    <label for="filterPosition" class="block text-sm font-medium text-gray-700 mb-1">职位</label>
                    <select id="filterPosition" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                      <option value="">全部职位</option>
                      <option value="director">主任</option>
                      <option value="deputy">副主任</option>
                      <option value="chief">科长</option>
                      <option value="staff">工作人员</option>
                    </select>
                  </div>
                  <div>
                    <label for="filterStatus" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                    <select id="filterStatus" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                      <option value="">全部状态</option>
                      <option value="active">正常</option>
                      <option value="leave">停用</option>
                    </select>
                  </div>
                  <div class="flex items-end space-x-2">
                    <button id="btnFilter" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                      <i class="fas fa-filter mr-1"></i> 筛选
                    </button>
                    <button id="btnResetFilter" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                      <i class="fas fa-undo mr-1"></i> 重置
                    </button>
                  </div>
                </div>
              </div>

              <!-- 数据表格 -->
              <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="overflow-x-auto">
                  <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                      <tr>
                        <th scope="col" class="w-16 px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                        <th scope="col" class="w-16 px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">性别</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职位</th>
                        <th scope="col" class="w-40 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                        <th scope="col" class="w-24 px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        <th scope="col" class="w-32 px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                      <tr>
                        <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">1</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李明</td>
                        <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center">男</td>
                        <td class="px-6 py-4 text-sm text-gray-900">广西壮族自治区交通运输厅</td>
                        <td class="px-6 py-4 text-sm text-gray-900">安监科</td>
                        <td class="px-6 py-4 text-sm text-gray-900">科长</td>
                        <td class="w-40 px-6 py-4 text-sm text-gray-900">13900139001</td>
                        <td class="w-24 px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">正常</td>
                        <td class="w-32 px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit" data-id="1">
                            <i class="fas fa-edit"></i> 编辑
                          </button>
                          <button class="text-red-600 hover:text-red-900 btn-delete" data-id="1">
                            <i class="fas fa-trash-alt"></i> 删除
                          </button>
                        </td>
                      </tr>
                      <tr>
                        <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">2</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王芳</td>
                        <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center">女</td>
                        <td class="px-6 py-4 text-sm text-gray-900">广西壮族自治区交通运输厅</td>
                        <td class="px-6 py-4 text-sm text-gray-900">安监科</td>
                        <td class="px-6 py-4 text-sm text-gray-900">科员</td>
                        <td class="w-40 px-6 py-4 text-sm text-gray-900">13800138002</td>
                        <td class="w-24 px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">正常</td>
                        <td class="w-32 px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit" data-id="2">
                            <i class="fas fa-edit"></i> 编辑
                          </button>
                          <button class="text-red-600 hover:text-red-900 btn-delete" data-id="2">
                            <i class="fas fa-trash-alt"></i> 删除
                          </button>
                        </td>
                      </tr>
                      <tr>
                        <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">3</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张伟</td>
                        <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center">男</td>
                        <td class="px-6 py-4 text-sm text-gray-900">广西壮族自治区交通运输厅</td>
                        <td class="px-6 py-4 text-sm text-gray-900">安监科</td>
                        <td class="px-6 py-4 text-sm text-gray-900">科员</td>
                        <td class="w-40 px-6 py-4 text-sm text-gray-900">13800138003</td>
                        <td class="w-24 px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">正常</td>
                        <td class="w-32 px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit" data-id="3">
                            <i class="fas fa-edit"></i> 编辑
                          </button>
                          <button class="text-red-600 hover:text-red-900 btn-delete" data-id="3">
                            <i class="fas fa-trash-alt"></i> 删除
                          </button>
                        </td>
                      </tr>
                      <tr class="hover:bg-gray-50">
                        <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">4</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">赵丽</td>
                        <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center">女</td>
                        <td class="px-6 py-4 text-sm text-gray-900">广西壮族自治区交通运输厅</td>
                        <td class="px-6 py-4 text-sm text-gray-900">安监科</td>
                        <td class="px-6 py-4 text-sm text-gray-900">科员</td>
                        <td class="w-40 px-6 py-4 text-sm text-gray-900">13800138003</td>
                        <td class="w-24 px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">停用</td>
                        <td class="w-32 px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit" data-id="4">
                            <i class="fas fa-edit"></i> 编辑
                          </button>
                          <button class="text-red-600 hover:text-red-900 btn-delete" data-id="4">
                            <i class="fas fa-trash-alt"></i> 删除
                          </button>
                        </td>
                      </tr>
                      <tr class="hover:bg-gray-50">
                        <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">5</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">陈刚</td>
                        <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center">男</td>
                        <td class="px-6 py-4 text-sm text-gray-900">广西壮族自治区交通运输厅</td>
                        <td class="px-6 py-4 text-sm text-gray-900">安监科</td>
                        <td class="px-6 py-4 text-sm text-gray-900">科员</td>
                        <td class="w-40 px-6 py-4 text-sm text-gray-900">13800138003</td>
                        <td class="w-24 px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">正常</td>
                        <td class="w-32 px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                           <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit" data-id="5">
                            <i class="fas fa-edit"></i> 编辑
                          </button>
                          <button class="text-red-600 hover:text-red-900 btn-delete" data-id="5">
                            <i class="fas fa-trash-alt"></i> 删除
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                
                <!-- 分页控件 -->
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            上一页
                        </a>
                        <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            下一页
                        </a>
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                显示第 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">12</span> 条记录
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">上一页</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                                <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    1
                                </a>
                                <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    2
                                </a>
                                <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    3
                                </a>
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                    ...
                                </span>
                                <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    8
                                </a>
                                <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">下一页</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </nav>
                        </div>
                    </div>
                </div>
              </div>
          </div> <!-- Close py-6 -->
      </main>
  </div> <!-- Close flex-container -->

  <!-- Modal should be outside the main content flow -->
  <div id="personnelModalApp">
      <el-dialog
          v-model="dialogVisible"
          :title="isEditMode ? '编辑人员' : '新增人员'"
          width="60%" 
          @closed="resetForm" 
          :close-on-click-modal="false"
      >
        <!-- Add Form Content Inside Dialog -->
        <el-form :model="personnelForm" ref="personnelFormRef" label-width="100px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="姓名" required prop="name">
                        <el-input v-model="personnelForm.name" placeholder="请输入姓名"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="性别" required prop="gender">
                         <el-radio-group v-model="personnelForm.gender">
                            <el-radio label="male">男</el-radio>
                            <el-radio label="female">女</el-radio>
                         </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>
             <el-form-item label="所属单位" prop="orgUnit">
                 <el-tree-select
                    v-model="personnelForm.orgUnit"
                    :data="orgOptions" 
                    :multiple="false"
                    :check-strictly="true"
                    placeholder="请选择所属单位"
                    clearable
                 />
            </el-form-item>
             <el-row :gutter="20">
                 <el-col :span="12">
                    <el-form-item label="部门" prop="department">
                        <el-input v-model="personnelForm.department" placeholder="请输入部门"></el-input>
                    </el-form-item>
                 </el-col>
                 <el-col :span="12">
                     <el-form-item label="职位" prop="position">
                        <el-input v-model="personnelForm.position" placeholder="请输入职位"></el-input>
                    </el-form-item>
                 </el-col>
             </el-row>
             <el-row :gutter="20">
                 <el-col :span="12">
                    <el-form-item label="联系电话" required prop="phone">
                        <el-input v-model="personnelForm.phone" placeholder="请输入联系电话"></el-input>
                    </el-form-item>
                </el-col>
                <!-- <el-col :span="12">
                    <el-form-item label="电子邮箱" prop="email">
                        <el-input v-model="personnelForm.email" placeholder="请输入电子邮箱"></el-input>
                    </el-form-item>
                </el-col> -->
             </el-row>
             <el-form-item label="状态" required prop="status">
                <el-select v-model="personnelForm.status" placeholder="请选择状态">
                    <el-option label="正常" value="active"></el-option>
                    <el-option label="停用" value="leave"></el-option>
                </el-select>
            </el-form-item>
             <!-- Add other form items based on personnelForm structure -->
             <!-- <el-form-item label="员工编号" prop="employeeId">
                <el-input v-model="personnelForm.employeeId" placeholder="请输入员工编号"></el-input>
            </el-form-item> -->
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="closeModal">取消</el-button>
                <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
            </span>
        </template>
      </el-dialog>
  </div>

  <!-- Load Libraries First -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
  <script src="https://unpkg.com/element-plus"></script>
  
  <!-- Page Specific Scripts -->
  <script>
    // Define unit options globally for reuse
    const standardUnitOptions = [
        { value: '1', label: '广西壮族自治区交通运输厅', children: [
            { value: '1.1', label: '直属事业单位及专项机构', children: [
                { value: '1.1.1', label: '自治区公路发展中心' },
                { value: '1.1.2', label: '自治区高速公路发展中心' },
                { value: '1.1.3', label: '自治区道路运输发展中心' }
            ]},
            { value: '1.2', label: '市级交通运输局', children: [
                { value: '1.2.1', label: '钦州市交通运输局' },
                { value: '1.2.2', label: '南宁市交通运输局' },
                { value: '1.2.3', label: '玉林市交通运输局' }
            ]}
        ]}
        // Add other top-level orgs if needed
    ];

    // Vue App for Org Filter
    const OrgFilterApp = {
        data() {
            return {
                selectedUnit: null, // Changed from selectedOrgs
                orgOptions: standardUnitOptions // Use standard data
            };
        },
        methods: {
            handleOrgChange(value) {
                console.log('Selected org:', value);
            }
        }
    };
    const orgFilterVm = Vue.createApp(OrgFilterApp);
    orgFilterVm.use(ElementPlus);
    orgFilterVm.mount('#orgFilterApp');

    // Define the Vue app for the Modal
    const PersonnelModalApp = {
        data() {
            return {
                dialogVisible: false,
                isEditMode: false,
                personnelForm: { // Define structure based on form fields
                    id: null,
                    name: '',
                    gender: 'male', // Default value
                    orgUnit: null, // Changed from array
                    department: '',
                    position: '',
                    employeeId: '', // Assuming there's an employee ID field
                    phone: '',
                    email: '',
                    status: 'active' // Default value
                    // Add other fields if they exist in the modal form
                },
                 orgOptions: standardUnitOptions // Use standard data
            };
        },
        methods: {
            openModal(isEdit = false, personnelData = null) {
                this.isEditMode = isEdit;
                if (isEdit && personnelData) {
                    // Populate form with existing data
                    // Ensure the structure matches personnelForm
                    this.personnelForm = { 
                        ...this.personnelForm, 
                        ...personnelData, 
                        orgUnit: personnelData.orgUnit // Ensure single value if coming from mock/API
                    }; 
                } else {
                    // Reset form for new entry
                    this.resetForm();
                }
                this.dialogVisible = true;
            },
            closeModal() {
                this.dialogVisible = false;
            },
            submitForm() {
                 console.log('Submitting form...');
                 // Find the form reference using $refs if available, or assume validation elsewhere
                 // Example validation (replace with actual logic if using el-form rules)
                 if (!this.personnelForm.name || !this.personnelForm.department /* Add other required fields */) {
                     ElementPlus.ElMessage.error('请填写所有必填项!');
                     return;
                 }
                 console.log('Form Submitted:', this.personnelForm);
                 // TODO: Add actual save/update logic here (e.g., AJAX call)
                 this.closeModal();
                 ElementPlus.ElMessage.success(this.isEditMode ? '人员信息更新成功！' : '人员添加成功！');
                 // Optionally refresh the table
            },
            resetForm() {
                // Reset form fields to default/empty
                this.personnelForm = {
                    id: null, name: '', gender: 'male', orgUnit: null, department: '', 
                    position: '', employeeId: '', phone: '', email: '', status: 'active'
                 };
                 // If using el-form, clear validation:
                 // if (this.$refs.personnelFormRef) {
                 //    this.$refs.personnelFormRef.clearValidate();
                 // }
            }
        }
    };

    const personnelModalVm = Vue.createApp(PersonnelModalApp);
    personnelModalVm.use(ElementPlus);
    // Store the mounted instance
    const mountedModalComponent = personnelModalVm.mount('#personnelModalApp');
    // Log the mounted instance
    console.log('Mounted Modal Component:', mountedModalComponent);

    // Button Event Listeners (Keep)
    document.addEventListener('DOMContentLoaded', function() {
      // Removed old sidebar toggle logic

      const btnAdd = document.getElementById('btnAdd');
      if(btnAdd){
          btnAdd.addEventListener('click', () => {
              // Log instance inside listener
              console.log('Add button clicked. Modal component instance:', mountedModalComponent);
              // Use the mounted component instance
              mountedModalComponent.openModal(false);
          });
      }

      // Event delegation for table buttons
      const tableBody = document.querySelector('tbody');
      if (tableBody) {
          tableBody.addEventListener('click', (event) => {
              const button = event.target.closest('button');
              if (!button) return;

              const personnelId = button.dataset.id;
              if (button.classList.contains('btn-edit')) {
                  console.log('Edit personnel:', personnelId);
                  // Mock data for editing
                  const mockData = {
                      id: personnelId,
                      name: '李四',
                      gender: '男',
                      orgUnit: ['1'], // Assuming single selection for edit
                      department: '技术部',
                      position: '软件工程师',
                      employeeId: '1002',
                      phone: '13912345679',
                      email: '<EMAIL>',
                      status: 'active'
                  };
                   // Use the mounted component instance
                  console.log('Edit button clicked. Modal component instance:', mountedModalComponent);
                  mountedModalComponent.openModal(true, mockData);
              } else if (button.classList.contains('btn-delete')) {
                  console.log('Delete personnel:', personnelId);
                  if (confirm(`确定要删除人员ID ${personnelId} 吗？`)) {
                      console.log('Deletion confirmed for', personnelId);
                      // Add API call for deletion
                  }
              }
          });
      }
    });
  </script>

  <!-- Load component HTML first -->
  <script src="js/navbarComponent.js"></script>
  <script src="js/sidebarComponent.js"></script>
  <!-- Then load the script to inject them and highlight links -->
  <script src="js/loadComponents.js"></script>
</body>
</html>