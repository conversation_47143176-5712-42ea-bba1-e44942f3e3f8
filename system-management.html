<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统管理 - 广西交通运输应急管理系统</title>

    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 引入公共样式 -->
    <link rel="stylesheet" href="css/emergency-common.css">
    <link rel="stylesheet" href="new_style.css">
    
    <!-- 引入导航栏组件 -->
    <script src="js/navigation-component.js"></script>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏容器 -->
        <div id="navigation-container"></div>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 70vh; text-align: center; padding: 40px 20px; background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%); border-radius: 15px; margin: 20px; color: white; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                <div style="font-size: 120px; margin-bottom: 30px; opacity: 0.8;">
                    <i class="fas fa-cogs"></i>
                </div>
                
                <h1 style="font-size: 48px; font-weight: bold; margin-bottom: 20px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">系统管理</h1>
                <h2 style="font-size: 24px; margin-bottom: 30px; opacity: 0.9;">System Management</h2>
                
                <p style="font-size: 18px; line-height: 1.8; max-width: 600px; margin-bottom: 40px; opacity: 0.8;">
                    系统管理模块正在开发中，将为您提供完善的用户权限管理、系统配置和运维监控功能。
                </p>
                
                <div style="background: rgba(255,255,255,0.1); border-radius: 10px; padding: 20px; margin: 20px 0; text-align: left; max-width: 500px;">
                    <h4 style="color: #fff; margin-bottom: 15px; font-size: 20px;"><i class="fas fa-star"></i> 核心功能</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="padding: 8px 0; border-bottom: 1px solid rgba(255,255,255,0.1); font-size: 16px;"><i class="fas fa-check" style="margin-right: 10px; color: #ffd700;"></i> 用户权限管理</li>
                        <li style="padding: 8px 0; border-bottom: 1px solid rgba(255,255,255,0.1); font-size: 16px;"><i class="fas fa-check" style="margin-right: 10px; color: #ffd700;"></i> 系统配置设置</li>
                        <li style="padding: 8px 0; border-bottom: 1px solid rgba(255,255,255,0.1); font-size: 16px;"><i class="fas fa-check" style="margin-right: 10px; color: #ffd700;"></i> 数据备份恢复</li>
                        <li style="padding: 8px 0; border-bottom: 1px solid rgba(255,255,255,0.1); font-size: 16px;"><i class="fas fa-check" style="margin-right: 10px; color: #ffd700;"></i> 日志审计功能</li>
                        <li style="padding: 8px 0; font-size: 16px;"><i class="fas fa-check" style="margin-right: 10px; color: #ffd700;"></i> 系统监控运维</li>
                    </ul>
                </div>
                
                <a href="risk-map.html" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid rgba(255,255,255,0.3); padding: 15px 30px; border-radius: 50px; font-size: 18px; cursor: pointer; text-decoration: none; display: inline-flex; align-items: center; gap: 10px; transition: all 0.3s ease;">
                    <i class="fas fa-arrow-left"></i>
                    返回风险一张图
                </a>
            </div>
        </main>
    </div>

    <script>
        // 初始化导航栏
        NavigationComponent.init('system-management');
    </script>
</body>
</html>
