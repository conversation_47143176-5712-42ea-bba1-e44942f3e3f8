<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应急资源总览 - 应急管理系统</title>
    <!-- 引入样式 -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/common.css"> <!-- 假设 common.css 存在且包含必要的基本样式 -->
    <link rel="stylesheet" href="css/unified_header.css">
    <!-- 添加 Element Plus 样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        html, body {
            height: 100%;
            margin: 0;
            font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
        }
        /* Common Tab styles */
        .main-tab-btn {
            padding: 0.75rem 1.25rem; /* Increased padding for main tabs */
            margin-right: 0.5rem;
            cursor: pointer;
            transition: background-color 0.2s, color 0.2s, border-color 0.2s;
            border-bottom: 3px solid transparent; /* Thicker border for main tabs */
            display: inline-flex;
            align-items: center;
            font-size: 1rem; /* Larger font for main tabs */
            color: #4b5563; /* Default text-gray-600 */
        }
        .main-tab-btn.active {
            color: #2563EB; /* text-blue-600 */
            border-bottom-color: #2563EB; /* border-blue-600 */
            font-weight: 600;
        }
        .main-tab-content {
            display: none;
        }
        .main-tab-content.active {
            display: block;
        }

        /* Styles for nested tabs (e.g., within Vehicle & Dispatch) if needed later */
        .sub-tab-btn {
             padding: 0.5rem 1rem;
             margin-right: 0.5rem;
             /* border-radius: 0.375rem 0.375rem 0 0; */ /* Top radius might not be needed if only bottom border */
             cursor: pointer;
             transition: background-color 0.2s, color 0.2s, border-color 0.2s;
             border-bottom: 2px solid transparent;
             display: inline-flex;
             align-items: center;
             font-size: 0.875rem; /* text-sm */
             color: #4b5563;
         }
         .sub-tab-btn.active {
             color: #2563EB;
             border-bottom-color: #2563EB;
             font-weight: 600;
         }
         .sub-tab-content {
             display: none;
         }
         .sub-tab-content.active {
             display: block;
         }

        /* Ensure Element Plus Dialog is on top */
        .el-overlay {
            z-index: 2000 !important;
        }
        .el-dialog {
           z-index: 2001 !important;
        }
       /* Custom Tree Select style (if carried over from other pages) */
        .el-tree-select {
            width: 100% !important;
        }
        .el-select-dropdown__wrap {
            max-height: 400px;
        }
        .el-tree-node__content {
            height: 32px;
        }
        .el-tree-node__label {
            font-size: 14px;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">

    <!-- Navbar Start -->
    <header class="top-nav">
        <div class="system-title">
            <strong>广西公路水路安全畅通与应急处置系统</strong>
        </div>
        <nav class="tab-navigation">
            <a href="new_index.html" class="tab-button">风险一张图</a>
            <a href="my_check_tasks.html" class="tab-button">风险隐患管理</a>
            <a href="plan_list.html" class="tab-button active">应急处置</a>
            <button class="tab-button" onclick="alert('应急演练功能暂未开放');">应急演练</button>
        </nav>
    </header>
    <!-- Navbar End -->

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container h-full" style="display: flex;">
        <!-- Sidebar Placeholder (will be populated by loadComponents.js) -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area (pt-16 for navbar height is handled by loadComponents.js) -->
        <main id="main-content" class="flex-grow pb-8 px-6 bg-gray-100">
            <div class="py-6">
                <!-- 页面标题 -->
                <div class="mb-6">
                    <h2 class="text-3xl font-bold text-gray-800">应急资源总览</h2>
                    <p class="text-gray-600 mt-1">集中管理仓库、车辆、调度点及各类应急物资</p>
                </div>

                <!-- Main Tab 导航 -->
                <div class="mb-6 border-b border-gray-300">
                    <nav class="-mb-px flex space-x-2" aria-label="MainResourceTabs">
                        <button class="main-tab-btn active" data-main-tab="warehouse-section">
                            <i class="fas fa-warehouse mr-2"></i>仓库管理
                        </button>
                        <button class="main-tab-btn" data-main-tab="material-section">
                            <i class="fas fa-boxes mr-2"></i>物资管理
                        </button>
                        <button class="main-tab-btn" data-main-tab="vehicle-dispatch-section">
                            <i class="fas fa-truck-moving mr-2"></i>车辆与调度点
                        </button>
                    </nav>
                </div>

                <!-- Main Tab 内容区域 -->
                <div id="main-tab-content-container">
                    <!-- 仓库管理内容 -->
                    <div id="warehouse-section" class="main-tab-content active">
                        <!-- Content for Warehouse Management will be added here -->
                                                <!-- 页面标题 -->
                                                <div class="flex justify-between items-center mb-6 pt-4"> <!-- Added pt-4 for spacing from tab nav -->
                                                    <div>
                                                        <h3 class="text-xl font-semibold text-gray-700">仓库列表与维护</h3>
                                                        <p class="text-gray-500 mt-1 text-sm">管理应急物资存放仓库的基础信息</p>
                                                    </div>
                                                    <div>
                                                        <button id="btnAddWarehouse" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                            <i class="fas fa-plus mr-2"></i> 新增仓库
                                                        </button>
                                                    </div>
                                                </div>

                                                <!-- 搜索栏 -->
                                                <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                                                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                                                        <div>
                                                            <label for="warehouse_name_filter" class="block text-sm font-medium text-gray-700 mb-1">仓库名称</label>
                                                            <input type="text" id="warehouse_name_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入仓库名称">
                                                        </div>
                                                        <div>
                                                            <label for="warehouse_org_unit_filter" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                                                            <div id="warehouse-filter-app"> <!-- MODIFIED ID -->
                                                                <el-tree-select
                                                                    v-model="selectedUnit"
                                                                    :data="unitOptions"
                                                                    :multiple="false"
                                                                    :check-strictly="true"
                                                                    placeholder="请选择单位"
                                                                    class="block w-full"
                                                                    clearable
                                                                    @change="handleChange"
                                                                />
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <label for="warehouse_type_filter" class="block text-sm font-medium text-gray-700 mb-1">仓库类型</label>
                                                            <select id="warehouse_type_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                                <option value="">全部</option>
                                                                <option value="1">中心库</option>
                                                                <option value="2">区域库</option>
                                                                <option value="3">前置库</option>
                                                            </select>
                                                        </div>
                                                        <div>
                                                            <button type="button" id="btnSearchWarehouse" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                                <i class="fas fa-search mr-1"></i> 搜索
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 仓库列表表格 -->
                                                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                                                    <div class="overflow-x-auto">
                                                        <table class="min-w-full divide-y divide-gray-200">
                                                            <thead class="bg-gray-50">
                                                                <tr>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库名称</th>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路段编号</th>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起始桩号</th>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库地址</th>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库类型</th>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                                                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody class="bg-white divide-y divide-gray-200">
                                                                <!-- 表格行示例 -->
                                                                <tr>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">南宁市应急物资中心库</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">南宁市应急管理局</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">G7211</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">K15+200</td>
                                                                    <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs">南宁市青秀区民族大道100号</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">中心库</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">王主任</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">138xxxxxxxx</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                                                        <button class="text-blue-600 hover:text-blue-900 btn-edit-warehouse" data-id="1" title="编辑"><i class="fas fa-edit"></i></button>
                                                                        <button class="text-red-600 hover:text-red-900 btn-delete-warehouse" data-id="1" title="删除"><i class="fas fa-trash"></i></button>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">钦州港应急物资前置库</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">钦州市交通运输局</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">G75</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">K2100+000</td>
                                                                    <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs">钦州市钦南区港口路1号</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">前置库</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">李科长</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">139xxxxxxxx</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                                                        <button class="text-blue-600 hover:text-blue-900 btn-edit-warehouse" data-id="2" title="编辑"><i class="fas fa-edit"></i></button>
                                                                        <button class="text-red-600 hover:text-red-900 btn-delete-warehouse" data-id="2" title="删除"><i class="fas fa-trash"></i></button>
                                                                    </td>
                                                                </tr>
                                                                <!-- 更多行... -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                    <!-- 分页 -->
                                                    <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                                                        <div class="text-sm text-gray-600">
                                                            显示 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条记录
                                                        </div>
                                                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                                                <span class="sr-only">上一页</span>
                                                                <i class="fas fa-chevron-left h-5 w-5"></i>
                                                            </a>
                                                            <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                                                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                                                <span class="sr-only">下一页</span>
                                                                <i class="fas fa-chevron-right h-5 w-5"></i>
                                                            </a>
                                                        </nav>
                                                    </div>
                                                </div>

                                                <!-- 添加/编辑仓库 Modal -->
                                                <div id="warehouse-modal-app"> <!-- MODIFIED ID -->
                                                    <el-dialog
                                                        v-model="dialogVisible"
                                                        :title="isEditMode ? '编辑仓库信息' : '新增仓库'"
                                                        width="50%"
                                                        @closed="resetForm"
                                                        :close-on-click-modal="false"
                                                    >
                                                        <el-form :model="warehouseForm" ref="warehouseFormRef" label-width="100px" label-position="right">
                                                            <el-row :gutter="20">
                                                                <el-col :span="12">
                                                                    <el-form-item label="仓库名称" required prop="name">
                                                                        <el-input v-model="warehouseForm.name" placeholder="请输入仓库名称"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="12">
                                                                    <el-form-item label="仓库类型" required prop="type">
                                                                        <el-select v-model="warehouseForm.type" placeholder="请选择仓库类型" style="width: 100%;">
                                                                            <el-option label="中心库" value="1"></el-option>
                                                                            <el-option label="区域库" value="2"></el-option>
                                                                            <el-option label="前置库" value="3"></el-option>
                                                                        </el-select>
                                                                    </el-form-item>
                                                                </el-col>
                                                            </el-row>
                                                            <el-form-item label="所属单位" required prop="orgUnit">
                                                                <el-tree-select
                                                                    v-model="warehouseForm.orgUnit"
                                                                    :data="unitOptions"
                                                                    :multiple="false"
                                                                    :check-strictly="true"
                                                                    placeholder="请选择所属单位"
                                                                    style="width: 100%;"
                                                                    clearable
                                                                />
                                                            </el-form-item>
                                                            <el-form-item label="仓库地址" required prop="address">
                                                                <el-input v-model="warehouseForm.address" placeholder="请输入仓库详细地址"></el-input>
                                                            </el-form-item>
                                                            <el-row :gutter="20">
                                                                <el-col :span="12">
                                                                    <el-form-item label="负责人" required prop="manager">
                                                                        <el-input v-model="warehouseForm.manager" placeholder="请输入负责人姓名"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="12">
                                                                    <el-form-item label="联系电话" required prop="phone">
                                                                        <el-input v-model="warehouseForm.phone" placeholder="请输入负责人联系电话"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                            </el-row>
                                                            <el-row :gutter="20">
                                                                <el-col :span="12">
                                                                    <el-form-item label="路段编号" required prop="roadSection">
                                                                        <el-select v-model="warehouseForm.roadSection" placeholder="请选择路段编号" style="width: 100%;">
                                                                            <el-option label="G7211" value="G7211"></el-option>
                                                                            <el-option label="G75" value="G75"></el-option>
                                                                            <el-option label="G80" value="G80"></el-option>
                                                                            <el-option label="其他" value="other"></el-option>
                                                                        </el-select>
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="12">
                                                                    <el-form-item label="起始桩号" required prop="startStake">
                                                                        <el-input v-model="warehouseForm.startStake" placeholder="例如: K15+200"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                            </el-row>
                                                            <el-form-item label="备注" prop="remarks">
                                                                <el-input type="textarea" :rows="3" v-model="warehouseForm.remarks" placeholder="请输入备注信息"></el-input>
                                                            </el-form-item>
                                                        </el-form>
                                                        <template #footer>
                                                            <span class="dialog-footer">
                                                                <el-button @click="dialogVisible = false">取消</el-button>
                                                                <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                                                            </span>
                                                        </template>
                                                    </el-dialog>
                                                </div>
                    </div>

                    <!-- 车辆与调度点管理内容 -->
                    <div id="vehicle-dispatch-section" class="main-tab-content">
                        <!-- Content for Vehicle & Dispatch Point Management will be added here -->
                                                <!-- Nested Tab 导航 -->
                                                <div class="mb-6 border-b border-gray-200 mt-4"> <!-- Added mt-4 -->
                                                    <nav class="-mb-px flex space-x-4" aria-label="VehicleDispatchTabs">
                                                        <button class="sub-tab-btn active" data-sub-tab="vehicle-content">
                                                            <i class="fas fa-truck mr-2"></i> 车辆管理
                                                        </button>
                                                        <button class="sub-tab-btn" data-sub-tab="tow-point-content">
                                                            <i class="fas fa-map-marker-alt mr-2"></i> 调度点管理
                                                        </button>
                                                    </nav>
                                                </div>

                                                <!-- Nested Tab 内容区域 -->
                                                <div id="sub-tab-content-container">
                                                    <!-- 车辆管理内容 -->
                                                    <div id="vehicle-content" class="sub-tab-content active">
                                                        <!-- 车辆搜索栏 -->
                                                        <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                                                            <div class="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
                                                                <div>
                                                                    <label for="vehicle_org_unit_filter" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                                                                    <div id="vehicle-filter-app"> <!-- MODIFIED ID -->
                                                                        <el-tree-select
                                                                            v-model="selectedUnit"
                                                                            :data="unitOptions"
                                                                            :multiple="false"
                                                                            :check-strictly="true"
                                                                            placeholder="请选择单位"
                                                                            class="block w-full"
                                                                            clearable
                                                                            @change="handleChange"
                                                                        />
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <label for="vehicle_type_filter" class="block text-sm font-medium text-gray-700 mb-1">车辆类型</label>
                                                                    <select id="vehicle_type_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                                        <option value="">全部</option>
                                                                        <option value="1">救援车</option>
                                                                        <option value="2">消防车</option>
                                                                        <option value="3">救护车</option>
                                                                        <option value="4">工程车</option>
                                                                        <option value="5">指挥车</option>
                                                                    </select>
                                                                </div>
                                                                <div>
                                                                    <label for="vehicle_road_id_filter" class="block text-sm font-medium text-gray-700 mb-1">路段编号</label>
                                                                    <input type="text" id="vehicle_road_id_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入路段编号">
                                                                </div>
                                                                <div>
                                                                    <label for="vehicle_status_filter" class="block text-sm font-medium text-gray-700 mb-1">车辆状态</label>
                                                                    <select id="vehicle_status_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                                        <option value="">全部</option>
                                                                        <option value="1">正常</option>
                                                                        <option value="0">停用</option>
                                                                    </select>
                                                                </div>
                                                                <div>
                                                                    <button type="button" id="btnSearchVehicle" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                                        <i class="fas fa-search mr-1"></i> 搜索车辆
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- 新增车辆按钮 & 车辆列表 -->
                                                        <div class="flex justify-end mb-4">
                                                            <button id="btnAddVehicle" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center">
                                                                <i class="fas fa-plus mr-2"></i> 新增车辆
                                                            </button>
                                                        </div>
                                                        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                                                            <div class="overflow-x-auto">
                                                                <table class="min-w-full divide-y divide-gray-200">
                                                                    <thead class="bg-gray-50">
                                                                        <tr>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">车牌号码</th>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">车辆类型</th>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前状态</th>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系电话</th>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">地址</th>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路段编号</th>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起始桩号</th>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                                                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody class="bg-white divide-y divide-gray-200">
                                                                        <!-- 表格行示例 -->
                                                                        <tr>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">桂A88888</td>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">救护车</td>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">南宁市应急管理局</td>
                                                                            <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">正常</span></td>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">张医生</td>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">13812345678</td>
                                                                            <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs" title="南宁市青秀区模拟地址1">南宁市青秀区模拟地址1</td>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">G7211</td>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">K10+000</td>
                                                                            <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs" title="定期保养">定期保养</td>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                                                                <button class="text-blue-600 hover:text-blue-900 btn-edit-vehicle" data-id="1" title="编辑"><i class="fas fa-edit"></i></button>
                                                                                <button class="text-red-600 hover:text-red-900 btn-delete-vehicle" data-id="1" title="删除"><i class="fas fa-trash"></i></button>
                                                                            </td>
                                                                        </tr>
                                                                         <tr>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">桂K12345</td>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">消防车</td>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">玉林市消防救援支队</td>
                                                                            <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">停用</span></td>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">李队长</td>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">13987654321</td>
                                                                            <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs" title="玉林市玉州区模拟地址2">玉林市玉州区模拟地址2</td>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">G75</td>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">K2000+500</td>
                                                                            <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs" title="水泵维修中">水泵维修中</td>
                                                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                                                                <button class="text-blue-600 hover:text-blue-900 btn-edit-vehicle" data-id="2" title="编辑"><i class="fas fa-edit"></i></button>
                                                                                <button class="text-red-600 hover:text-red-900 btn-delete-vehicle" data-id="2" title="删除"><i class="fas fa-trash"></i></button>
                                                                            </td>
                                                                        </tr>
                                                                        <!-- 更多行... -->
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                            <!-- 车辆分页 -->
                                                            <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                                                                <div class="text-sm text-gray-600">
                                                                    显示 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条记录
                                                                </div>
                                                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                                                    <span class="sr-only">上一页</span><i class="fas fa-chevron-left h-5 w-5"></i></a>
                                                                    <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                                                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                                                    <span class="sr-only">下一页</span><i class="fas fa-chevron-right h-5 w-5"></i></a>
                                                                </nav>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- 调度点管理内容 -->
                                                    <div id="tow-point-content" class="sub-tab-content">
                                                        <!-- 调度点筛选栏 -->
                                                        <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                                                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                                                                <div>
                                                                    <label for="tow_point_org_unit_filter" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                                                                    <div id="tow-point-filter-org-app"> <!-- ID RETAINED, ENSURE UNIQUE VUE INSTANCE -->
                                                                        <el-tree-select
                                                                            v-model="selectedUnit"
                                                                            :data="unitOptions"
                                                                            :multiple="false"
                                                                            :check-strictly="true"
                                                                            placeholder="请选择单位"
                                                                            style="width: 100%;"
                                                                            clearable
                                                                        />
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <label for="tow_point_name_filter" class="block text-sm font-medium text-gray-700 mb-1">名称</label>
                                                                    <input type="text" id="tow_point_name_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入调度点名称">
                                                                </div>
                                                                <div>
                                                                    <label for="tow_point_road_id_filter" class="block text-sm font-medium text-gray-700 mb-1">路段编号</label>
                                                                    <input type="text" id="tow_point_road_id_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入路段编号">
                                                                </div>
                                                                <div class="flex space-x-2 justify-end">
                                                                     <button id="btnResetTowPointFilters" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                                                        <i class="fas fa-undo mr-1"></i> 重置
                                                                    </button>
                                                                    <button id="btnSearchTowPoints" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                                        <i class="fas fa-search mr-1"></i> 查询调度点
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- 新增调度点按钮 & 调度点列表 -->
                                                        <div class="flex justify-end mb-4">
                                                            <button id="btnAddTowPoint" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center">
                                                                <i class="fas fa-plus mr-2"></i> 新增调度点
                                                            </button>
                                                        </div>
                                                        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                                                            <div class="overflow-x-auto">
                                                                <table class="min-w-full divide-y divide-gray-200">
                                                                    <thead class="bg-gray-50">
                                                                        <tr>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路段编号</th>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起始桩号</th>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">地址</th>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                                                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody class="bg-white divide-y divide-gray-200">
                                                                        <!-- 调度点示例数据 -->
                                                                        <tr>
                                                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                                                           <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">G72高速公路拖车点A</td>
                                                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">自治区高速公路发展中心</td>
                                                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">G72</td>
                                                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">K1500+000</td>
                                                                           <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs" title="G72高速K1500服务区">G72高速K1500服务区</td>
                                                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">张工</td>
                                                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">13500135000</td>
                                                                           <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                                                               <button class="text-blue-600 hover:text-blue-900 btn-edit-tow-point" data-id="tp1" title="编辑"><i class="fas fa-edit"></i></button>
                                                                               <button class="text-red-600 hover:text-red-900 btn-delete-tow-point" data-id="tp1" title="删除"><i class="fas fa-trash"></i></button>
                                                                           </td>
                                                                        </tr>
                                                                        <tr>
                                                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                                                           <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">G80高速公路拖车点B</td>
                                                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">自治区公路发展中心</td>
                                                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">G80</td>
                                                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">K200+000</td>
                                                                           <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs" title="G80高速K200收费站旁">G80高速K200收费站旁</td>
                                                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">钱工</td>
                                                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">13600136000</td>
                                                                           <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                                                               <button class="text-blue-600 hover:text-blue-900 btn-edit-tow-point" data-id="tp2" title="编辑"><i class="fas fa-edit"></i></button>
                                                                               <button class="text-red-600 hover:text-red-900 btn-delete-tow-point" data-id="tp2" title="删除"><i class="fas fa-trash"></i></button>
                                                                           </td>
                                                                        </tr>
                                                                        <!-- 更多调度点 -->
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                            <!-- 调度点分页 -->
                                                            <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                                                               <div class="text-sm text-gray-600">
                                                                   显示 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条记录
                                                               </div>
                                                               <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                                                   <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                                                   <span class="sr-only">上一页</span><i class="fas fa-chevron-left h-5 w-5"></i></a>
                                                                   <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                                                                   <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                                                   <span class="sr-only">下一页</span><i class="fas fa-chevron-right h-5 w-5"></i></a>
                                                               </nav>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 添加/编辑车辆 Modal -->
                                                <div id="vehicle-modal-app"> <!-- MODIFIED ID -->
                                                    <el-dialog
                                                        v-model="dialogVisible"
                                                        :title="isEditMode ? '编辑车辆信息' : '新增车辆'"
                                                        width="60%"
                                                        @closed="resetForm"
                                                        :close-on-click-modal="false"
                                                    >
                                                        <el-form :model="vehicleForm" ref="vehicleFormRef" label-width="100px" label-position="right">
                                                            <el-row :gutter="20">
                                                                <el-col :span="12">
                                                                    <el-form-item label="车牌号码" required prop="plateNumber">
                                                                        <el-input v-model="vehicleForm.plateNumber" placeholder="例如: 桂A88888"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="12">
                                                                    <el-form-item label="车辆类型" required prop="type">
                                                                        <el-select v-model="vehicleForm.type" placeholder="请选择车辆类型" style="width: 100%;">
                                                                            <el-option label="救援车" value="1"></el-option>
                                                                            <el-option label="消防车" value="2"></el-option>
                                                                            <el-option label="救护车" value="3"></el-option>
                                                                            <el-option label="工程车" value="4"></el-option>
                                                                            <el-option label="指挥车" value="5"></el-option>
                                                                            <el-option label="其他" value="99"></el-option>
                                                                        </el-select>
                                                                    </el-form-item>
                                                                </el-col>
                                                            </el-row>
                                                            <el-row :gutter="20">
                                                                <el-col :span="12">
                                                                    <el-form-item label="所属单位" required prop="orgUnit">
                                                                        <el-tree-select
                                                                            v-model="vehicleForm.orgUnit"
                                                                            :data="unitOptions"
                                                                            :multiple="false"
                                                                            :check-strictly="true"
                                                                            placeholder="请选择所属单位"
                                                                            style="width: 100%;"
                                                                        />
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="12">
                                                                    <el-form-item label="车辆状态" required prop="status">
                                                                        <el-select v-model="vehicleForm.status" placeholder="请选择车辆状态" style="width: 100%;">
                                                                            <el-option label="正常" value="1"></el-option>
                                                                            <el-option label="停用" value="0"></el-option>
                                                                        </el-select>
                                                                    </el-form-item>
                                                                </el-col>
                                                            </el-row>
                                                            <el-row :gutter="20">
                                                                <el-col :span="12">
                                                                    <el-form-item label="负责人" required prop="manager">
                                                                        <el-input v-model="vehicleForm.manager" placeholder="请输入负责人姓名"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="12">
                                                                    <el-form-item label="联系电话" required prop="phone">
                                                                        <el-input v-model="vehicleForm.phone" placeholder="请输入负责人联系电话"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                            </el-row>
                                                            <el-row :gutter="20">
                                                                <el-col :span="12">
                                                                    <el-form-item label="地址" prop="address">
                                                                        <el-input v-model="vehicleForm.address" placeholder="请输入车辆停放或所在地址"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="12">
                                                                    <el-form-item label="路段编号" prop="roadId">
                                                                        <el-input v-model="vehicleForm.roadId" placeholder="例如: G72"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                            </el-row>
                                                            <el-form-item label="起始桩号" prop="startStake">
                                                                <el-input v-model="vehicleForm.startStake" placeholder="例如: K15+200"></el-input>
                                                            </el-form-item>
                                                            <el-form-item label="备注" prop="remarks">
                                                                <el-input type="textarea" :rows="3" v-model="vehicleForm.remarks" placeholder="请输入备注信息"></el-input>
                                                            </el-form-item>
                                                        </el-form>
                                                        <template #footer>
                                                            <span class="dialog-footer">
                                                                <el-button @click="dialogVisible = false">取消</el-button>
                                                                <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                                                            </span>
                                                        </template>
                                                    </el-dialog>
                                                </div>

                                                <!-- 添加/编辑调度点 Modal -->
                                                <div id="tow-point-modal-app"> <!-- MODIFIED ID -->
                                                    <el-dialog
                                                        v-model="dialogVisible"
                                                        :title="isEditMode ? '编辑调度点' : '新增调度点'"
                                                        width="50%"
                                                        @closed="resetForm"
                                                        :close-on-click-modal="false"
                                                    >
                                                        <el-form :model="towPointForm" ref="towPointFormRef" label-width="100px">
                                                            <el-form-item label="调度点名称" required prop="name">
                                                                <el-input v-model="towPointForm.name" placeholder="请输入调度点名称"></el-input>
                                                            </el-form-item>
                                                            <el-form-item label="所属单位" required prop="orgUnit">
                                                                <el-tree-select
                                                                    v-model="towPointForm.orgUnit"
                                                                    :data="unitOptions"
                                                                    :multiple="false"
                                                                    :check-strictly="true"
                                                                    placeholder="请选择所属单位"
                                                                    style="width: 100%;"
                                                                />
                                                            </el-form-item>
                                                            <el-form-item label="路段编号" prop="roadId">
                                                                <el-input v-model="towPointForm.roadId" placeholder="例如: G72"></el-input>
                                                            </el-form-item>
                                                            <el-form-item label="起始桩号" prop="startMarker">
                                                                <el-input v-model="towPointForm.startMarker" placeholder="例如: K1500+000"></el-input>
                                                            </el-form-item>
                                                            <el-form-item label="地址" prop="address">
                                                                <el-input v-model="towPointForm.address" placeholder="请输入调度点地址"></el-input>
                                                            </el-form-item>
                                                            <el-form-item label="负责人" prop="manager">
                                                                <el-input v-model="towPointForm.manager" placeholder="请输入负责人姓名"></el-input>
                                                            </el-form-item>
                                                            <el-form-item label="联系方式" prop="phone">
                                                                <el-input v-model="towPointForm.phone" placeholder="请输入负责人联系方式"></el-input>
                                                            </el-form-item>
                                                            <el-form-item label="备注" prop="remarks">
                                                                <el-input type="textarea" :rows="3" v-model="towPointForm.remarks" placeholder="请输入备注信息"></el-input>
                                                            </el-form-item>
                                                        </el-form>
                                                        <template #footer>
                                                            <span class="dialog-footer">
                                                                <el-button @click="dialogVisible = false">取消</el-button>
                                                                <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                                                            </span>
                                                        </template>
                                                    </el-dialog>
                                                </div>
                    </div>

                    <!-- 物资管理内容 -->
                    <div id="material-section" class="main-tab-content">
                        <!-- Content for Material Management will be added here -->
                                                <!-- 页面标题和操作按钮 -->
                                                <div class="flex justify-between items-center mb-6 pt-4"> <!-- Added pt-4 -->
                                                    <div>
                                                        <h3 class="text-xl font-semibold text-gray-700">应急物资列表与维护</h3>
                                                        <p class="text-gray-500 mt-1 text-sm">管理应急物资库存信息，支持搜索、筛选和库存维护</p>
                                                    </div>
                                                    <div class="flex">
                                                        <button id="btnExportMaterials" class="mr-3 bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                            <i class="fas fa-file-export mr-2"></i> 导出
                                                        </button>
                                                        <button id="btnAddMaterial" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                            <i class="fas fa-plus mr-2"></i> 新增物资
                                                        </button>
                                                    </div>
                                                </div>

                                                <!-- 搜索和筛选区域 -->
                                                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                                        <div>
                                                            <label for="material_name_filter" class="block text-sm font-medium text-gray-700 mb-1">物资名称</label>
                                                            <input type="text" id="material_name_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入物资名称关键字">
                                                        </div>
                                                        <div>
                                                            <label for="material_model_filter" class="block text-sm font-medium text-gray-700 mb-1">规格型号</label>
                                                            <input type="text" id="material_model_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入规格型号">
                                                        </div>
                                                        <div>
                                                            <label for="material_type_filter" class="block text-sm font-medium text-gray-700 mb-1">物资类别</label>
                                                            <select id="material_type_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                                <option value="">所有类别</option>
                                                                <option value="1">防疫物资</option>
                                                                <option value="2">防洪物资</option>
                                                                <option value="3">消防物资</option>
                                                                <option value="4">救灾物资</option>
                                                                <option value="5">通信设备</option>
                                                                <option value="6">应急装备</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-4">
                                                        <div>
                                                            <label for="material_warehouse_filter" class="block text-sm font-medium text-gray-700 mb-1">所属仓库</label>
                                                            <select id="material_warehouse_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                                <option value="">所有仓库</option>
                                                                <option value="1">中心仓库</option>
                                                                <option value="2">东区仓库</option>
                                                                <option value="3">南区仓库</option>
                                                                <option value="4">西区仓库</option>
                                                                <option value="5">北区仓库</option>
                                                            </select>
                                                        </div>
                                                        <div>
                                                            <label for="material_status_filter" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                                                            <select id="material_status_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                                <option value="">所有状态</option>
                                                                <option value="1">正常</option>
                                                                <option value="2">维修中</option>
                                                                <option value="3">报废</option>
                                                            </select>
                                                        </div>
                                                        <div class="flex items-end">
                                                            <div class="flex justify-end w-full">
                                                                <button id="btnResetMaterialFilters" class="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm mr-2 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                                                    <i class="fas fa-redo mr-1"></i> 重置
                                                                </button>
                                                                <button id="btnSearchMaterials" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                                    <i class="fas fa-search mr-1"></i> 查询
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 物资列表表格 -->
                                                <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                                                    <div class="overflow-x-auto">
                                                        <table class="min-w-full divide-y divide-gray-200">
                                                            <thead class="bg-gray-50">
                                                                <tr>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物资名称</th>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规格型号</th>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物资类别</th>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属仓库</th>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">有效期</th>
                                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                                                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody class="bg-white divide-y divide-gray-200">
                                                                <tr>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">医用防护口罩</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">N95</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">防疫物资</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">中心仓库</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">10000</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">个</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">正常</span></td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2025-12-31</td>
                                                                    <td class="px-6 py-4 text-sm text-gray-500 truncate max-w-xs" title="标准N95防护级别">标准N95防护级别</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                                                        <button class="text-blue-600 hover:text-blue-800 btn-edit-material" data-id="1">修改</button>
                                                                        <button class="text-red-600 hover:text-red-800 btn-delete-material" data-id="1">删除</button>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">救生衣</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">成人通用</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">防洪物资</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">东区仓库</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">500</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">件</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">正常</span></td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2026-06-30</td>
                                                                    <td class="px-6 py-4 text-sm text-gray-500 truncate max-w-xs" title="符合国标GB25804">符合国标GB25804</td>
                                                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                                                        <button class="text-blue-600 hover:text-blue-800 btn-edit-material" data-id="2">修改</button>
                                                                        <button class="text-red-600 hover:text-red-800 btn-delete-material" data-id="2">删除</button>
                                                                    </td>
                                                                </tr>
                                                                <!-- 更多物资行... -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                    <!-- 分页 -->
                                                    <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                                                        <div class="text-sm text-gray-600">
                                                            显示 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条记录
                                                        </div>
                                                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                                                <span class="sr-only">上一页</span>
                                                                <i class="fas fa-chevron-left h-5 w-5"></i>
                                                            </a>
                                                            <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                                                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                                                <span class="sr-only">下一页</span>
                                                                <i class="fas fa-chevron-right h-5 w-5"></i>
                                                            </a>
                                                        </nav>
                                                    </div>
                                                </div>

                                                <!-- 新增/编辑物资 Modal -->
                                                <div id="material-modal-app"> <!-- MODIFIED ID -->
                                                    <el-dialog
                                                        v-model="dialogVisible"
                                                        :title="isEditMode ? '编辑物资信息' : '新增物资'"
                                                        width="60%"
                                                        @closed="resetForm"
                                                        :close-on-click-modal="false"
                                                    >
                                                        <el-form :model="materialForm" ref="materialFormRef" label-width="100px" label-position="right">
                                                            <el-row :gutter="20">
                                                                <el-col :span="12">
                                                                    <el-form-item label="物资名称" required prop="name">
                                                                        <el-input v-model="materialForm.name" placeholder="请输入物资名称"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="12">
                                                                    <el-form-item label="规格型号" prop="model">
                                                                        <el-input v-model="materialForm.model" placeholder="请输入规格型号"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                            </el-row>
                                                            <el-row :gutter="20">
                                                                <el-col :span="12">
                                                                    <el-form-item label="物资类别" required prop="type">
                                                                        <el-select v-model="materialForm.type" placeholder="请选择物资类别" style="width: 100%;">
                                                                            <el-option label="防疫物资" value="1"></el-option>
                                                                            <el-option label="防洪物资" value="2"></el-option>
                                                                            <el-option label="消防物资" value="3"></el-option>
                                                                            <el-option label="救灾物资" value="4"></el-option>
                                                                            <el-option label="通信设备" value="5"></el-option>
                                                                            <el-option label="应急装备" value="6"></el-option>
                                                                        </el-select>
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="12">
                                                                    <el-form-item label="所属仓库" required prop="warehouseId">
                                                                        <el-select v-model="materialForm.warehouseId" placeholder="请选择所属仓库" style="width: 100%;">
                                                                            <el-option label="中心仓库" value="1"></el-option>
                                                                            <el-option label="东区仓库" value="2"></el-option>
                                                                            <el-option label="南区仓库" value="3"></el-option>
                                                                            <el-option label="西区仓库" value="4"></el-option>
                                                                            <el-option label="北区仓库" value="5"></el-option>
                                                                        </el-select>
                                                                    </el-form-item>
                                                                </el-col>
                                                            </el-row>
                                                            <el-row :gutter="20">
                                                                <el-col :span="8">
                                                                    <el-form-item label="数量" required prop="quantity">
                                                                        <el-input-number v-model="materialForm.quantity" :min="0" placeholder="请输入数量" style="width: 100%;"></el-input-number>
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="8">
                                                                    <el-form-item label="单位" required prop="unit">
                                                                        <el-input v-model="materialForm.unit" placeholder="例如: 个, 件, 套"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="8">
                                                                    <el-form-item label="状态" required prop="status">
                                                                        <el-select v-model="materialForm.status" placeholder="请选择状态" style="width: 100%;">
                                                                            <el-option label="正常" value="1"></el-option>
                                                                            <el-option label="维修中" value="2"></el-option>
                                                                            <el-option label="报废" value="3"></el-option>
                                                                        </el-select>
                                                                    </el-form-item>
                                                                </el-col>
                                                            </el-row>
                                                            <el-row :gutter="20">
                                                                <el-col :span="8">
                                                                    <el-form-item label="有效期" prop="expiryDate">
                                                                        <el-date-picker
                                                                            v-model="materialForm.expiryDate"
                                                                            type="date"
                                                                            placeholder="选择有效期"
                                                                            format="YYYY-MM-DD"
                                                                            value-format="YYYY-MM-DD"
                                                                            style="width: 100%;">
                                                                        </el-date-picker>
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="16">
                                                                    <el-form-item label="备注" prop="remarks">
                                                                        <el-input type="textarea" :rows="1" v-model="materialForm.remarks" placeholder="请输入备注信息"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                            </el-row>
                                                        </el-form>
                                                        <template #footer>
                                                            <span class="dialog-footer">
                                                                <el-button @click="closeModal">取消</el-button>
                                                                <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                                                            </span>
                                                        </template>
                                                    </el-dialog>
                                                </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入 Vue 和 Element Plus -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <script src="https://unpkg.com/element-plus"></script>

    <!-- 全局 standardUnitOptions 定义 -->
    <script>
        const standardUnitOptions = [
            { value: '1', label: '广西壮族自治区交通运输厅', children: [
                { value: '1.1', label: '直属事业单位及专项机构', children: [
                    { value: '1.1.1', label: '自治区公路发展中心' },
                    { value: '1.1.2', label: '自治区高速公路发展中心' },
                    { value: '1.1.3', label: '自治区道路运输发展中心' }
                ]},
                { value: '1.2', label: '市级交通运输局', children: [
                    { value: '1.2.1', label: '钦州市交通运输局' },
                    { value: '1.2.2', label: '南宁市交通运输局' },
                    { value: '1.2.3', label: '玉林市交通运输局' }
                ]}
            ]}
            // 可以根据需要添加其他顶级组织
        ];
    </script>

    <!-- 各模块脚本占位符 -->
    <script id="warehouse-module-scripts">
                // Warehouse Filter Vue App
                const WarehouseFilterAppDefinition = {
            data() {
                return {
                    selectedUnit: null,
                    unitOptions: standardUnitOptions // Assumes standardUnitOptions is globally defined
                };
            },
            methods: {
                handleChange(value) {
                    console.log('仓库筛选 - 选中单位:', value);
                    // Add filter logic here
                }
            }
        };
        const warehouseFilterApp = Vue.createApp(WarehouseFilterAppDefinition);
        warehouseFilterApp.use(ElementPlus);
        warehouseFilterApp.mount('#warehouse-filter-app');

        // Warehouse Modal Vue App
        const WarehouseModalDefinition = {
            data() {
                return {
                    dialogVisible: false,
                    isEditMode: false,
                    warehouseForm: {
                        id: null,
                        name: '',
                        type: '',
                        orgUnit: null,
                        address: '',
                        manager: '',
                        phone: '',
                        roadSection: '',
                        startStake: '',
                        remarks: ''
                    },
                    unitOptions: standardUnitOptions // Assumes standardUnitOptions is globally defined
                };
            },
            methods: {
                openModal(isEdit = false, warehouseData = null) {
                    this.isEditMode = isEdit;
                    if (isEdit && warehouseData) {
                        this.warehouseForm = { ...warehouseData };
                    } else {
                        this.resetForm();
                    }
                    this.dialogVisible = true;
                },
                closeModal() {
                    this.dialogVisible = false;
                },
                submitForm() {
                    // In a real app, this.$refs.warehouseFormRef.validate would be used
                    // For simplicity in this combined script, direct submission:
                    console.log('仓库模态框 - 表单提交:', this.warehouseForm);
                    // TODO: Add actual save/update logic here
                    alert('仓库信息已' + (this.isEditMode ? '保存' : '添加') + ' (模拟)');
                    this.closeModal();
                },
                resetForm() {
                    this.warehouseForm = {
                        id: null, name: '', type: '', orgUnit: null, address: '',
                        manager: '', phone: '', roadSection: '', startStake: '', remarks: ''
                    };
                    // if (this.$refs.warehouseFormRef) { // refs might not be immediately available if template is complex
                    //    this.$refs.warehouseFormRef.clearValidate();
                    // }
                }
            }
        };
        const warehouseModalApp = Vue.createApp(WarehouseModalDefinition);
        warehouseModalApp.use(ElementPlus);
        const warehouseModalInstance = warehouseModalApp.mount('#warehouse-modal-app');

        // Event Listeners for Warehouse Tab buttons
        document.getElementById('warehouse-section').addEventListener('click', function(event) {
            if (event.target.closest('#btnAddWarehouse')) {
                 warehouseModalInstance.openModal(false);
            }

            const editButton = event.target.closest('.btn-edit-warehouse');
            if (editButton) {
                const warehouseId = editButton.dataset.id;
                console.log("编辑仓库 ID:", warehouseId);
                const mockData = {
                    id: warehouseId,
                    name: `示例仓库 ${warehouseId}`,
                    type: (parseInt(warehouseId) % 3 + 1).toString(),
                    orgUnit: '1.1.1', // Example
                    address: `${warehouseId}号路模拟地址`,
                    manager: `负责人 ${warehouseId}`,
                    phone: `1300000000${warehouseId}`,
                    roadSection: 'G7211',
                    startStake: `K${warehouseId}0+000`,
                    remarks: `这是仓库 ${warehouseId} 的备注信息。`
                };
                warehouseModalInstance.openModal(true, mockData);
            }

            const deleteButton = event.target.closest('.btn-delete-warehouse');
            if (deleteButton) {
                const warehouseId = deleteButton.dataset.id;
                if (confirm(`确定要删除仓库 ID ${warehouseId} 吗？`)) {
                    console.log("删除仓库 ID:", warehouseId);
                     alert('删除仓库 ID: ' + warehouseId + ' (模拟)');
                }
            }

            if (event.target.closest('#btnSearchWarehouse')) {
                const name = document.getElementById('warehouse_name_filter').value;
                const type = document.getElementById('warehouse_type_filter').value;
                // const orgUnit = warehouseFilterAppInstance.selectedUnit; // Access Vue data if needed for complex filter
                console.log('搜索仓库:', { name, type /*, orgUnit */ });
                alert('触发仓库搜索 (模拟)');
            }
        });
    </script>
    <script id="vehicle-dispatch-module-scripts">
                // ----- Vehicle & Dispatch Point Module Scripts -----

        // Vue App for Vehicle Filter Org Unit
        const VehicleFilterAppDefinition = {
            data() {
                return {
                    selectedUnit: null,
                    unitOptions: standardUnitOptions
                }
            },
            methods: {
                handleChange(value) {
                    console.log('车辆筛选 - 选中单位:', value);
                }
            }
        };
        const vehicleFilterApp = Vue.createApp(VehicleFilterAppDefinition);
        vehicleFilterApp.use(ElementPlus);
        vehicleFilterApp.mount('#vehicle-filter-app'); // Mount to new ID

        // Vue App for Vehicle Modal
         const VehicleModalDefinition = {
             data() {
                 return {
                     dialogVisible: false,
                     isEditMode: false,
                     vehicleForm: { id: null, plateNumber: '', type: '', orgUnit: null, status: '1', manager: '', phone: '', address: '', roadId: '', startStake: '', remarks: '' },
                     unitOptions: standardUnitOptions
                 };
             },
             methods: {
                 openModal(isEdit = false, vehicleData = null) {
                     this.isEditMode = isEdit;
                     if (isEdit && vehicleData) { this.vehicleForm = { ...vehicleData }; }
                     else { this.resetForm(); }
                     this.dialogVisible = true;
                 },
                 resetForm() {
                     this.vehicleForm = { id: null, plateNumber: '', type: '', orgUnit: null, status: '1', manager: '', phone: '', address: '', roadId: '', startStake: '', remarks: '' };
                     // if (this.$refs.vehicleFormRef) { this.$refs.vehicleFormRef.resetFields(); }
                 },
                 submitForm() {
                     // if (this.$refs.vehicleFormRef.validate(...)) // Simplified for integration
                     console.log('车辆模态框 - 表单提交:', this.vehicleForm);
                     alert('车辆信息已' + (this.isEditMode ? '保存' : '添加') + ' (模拟)');
                     this.dialogVisible = false;
                 }
             }
         };
        const vehicleModalApp = Vue.createApp(VehicleModalDefinition);
        vehicleModalApp.use(ElementPlus);
        const vehicleModalInstance = vehicleModalApp.mount('#vehicle-modal-app'); // Mount to new ID

        // Vue App for Tow Point Filter Org Unit
         const TowPointFilterAppDefinition = {
            data() {
                return {
                     selectedUnit: null,
                     unitOptions: standardUnitOptions
                 }
             },
             methods: {
                 handleChange(value) {
                     console.log('调度点筛选 - 选中单位:', value);
                 }
             }
         };
         const towPointFilterApp = Vue.createApp(TowPointFilterAppDefinition);
         towPointFilterApp.use(ElementPlus);
         towPointFilterApp.mount('#tow-point-filter-org-app'); // Mount to its ID

        // Vue App for Tow Point Modal
         const TowPointModalDefinition = {
             data() {
                 return {
                     dialogVisible: false,
                     isEditMode: false,
                     towPointForm: { id: null, name: '', orgUnit: null, roadId: '', startMarker: '', manager: '', phone: '', address: '', remarks: '' },
                     unitOptions: standardUnitOptions
                 };
             },
             methods: {
                 openModal(isEdit = false, towPointData = null) {
                     this.isEditMode = isEdit;
                     if (isEdit && towPointData) { this.towPointForm = { ...towPointData }; }
                     else { this.resetForm(); }
                     this.dialogVisible = true;
                 },
                 resetForm() {
                     this.towPointForm = { id: null, name: '', orgUnit: null, roadId: '', startMarker: '', manager: '', phone: '', address: '', remarks: '' };
                     // if (this.$refs.towPointFormRef) { this.$refs.towPointFormRef.resetFields(); }
                 },
                 submitForm() {
                     // if (this.$refs.towPointFormRef.validate(...)) // Simplified
                     console.log('调度点模态框 - 表单提交:', this.towPointForm);
                     alert('调度点信息已' + (this.isEditMode ? '保存' : '添加') + ' (模拟)');
                     this.dialogVisible = false;
                 }
             }
         };
         const towPointModalApp = Vue.createApp(TowPointModalDefinition);
         towPointModalApp.use(ElementPlus);
         const towPointModalInstance = towPointModalApp.mount('#tow-point-modal-app'); // Mount to new ID

        // Nested Tab Switching Logic (within vehicle-dispatch-section)
        const vehicleDispatchSection = document.getElementById('vehicle-dispatch-section');
        if (vehicleDispatchSection) {
            const subTabButtons = vehicleDispatchSection.querySelectorAll('.sub-tab-btn');
            const subTabContents = vehicleDispatchSection.querySelectorAll('.sub-tab-content');

             // Ensure initial state for sub-tabs
             let subActiveFound = false;
             subTabButtons.forEach(btn => {
                 if (btn.classList.contains('active')) subActiveFound = true;
             });
             subTabContents.forEach(content => {
                 if (!content.classList.contains('active')) {
                     content.style.display = 'none';
                 } else {
                     content.style.display = 'block'; // Make sure active is displayed
                 }
             });
             if (!subActiveFound && subTabButtons.length > 0) {
                 subTabButtons[0].classList.add('active');
                 const firstSubTabContentId = subTabButtons[0].dataset.subTab;
                 const firstSubTabContent = vehicleDispatchSection.querySelector('#' + firstSubTabContentId);
                 if (firstSubTabContent) {
                     firstSubTabContent.style.display = 'block';
                     firstSubTabContent.classList.add('active');
                 }
             }


            subTabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetSubTab = button.dataset.subTab;

                    subTabButtons.forEach(btn => {
                        btn.classList.remove('active');
                    });
                    button.classList.add('active');

                    subTabContents.forEach(content => {
                        content.style.display = 'none'; // Hide all sub-tabs first
                        content.classList.remove('active');
                    });

                    const activeSubContent = vehicleDispatchSection.querySelector('#' + targetSubTab);
                    if (activeSubContent) {
                        activeSubContent.style.display = 'block'; // Show the target sub-tab
                        activeSubContent.classList.add('active');
                    }
                });
            });
        }

        // Event Listeners for Vehicle & Dispatch Point Tab buttons/actions
        vehicleDispatchSection.addEventListener('click', function(event) {
            // --- Vehicle Actions ---
            if (event.target.closest('#btnAddVehicle')) {
                vehicleModalInstance.openModal(false);
            }
            const editVehicleButton = event.target.closest('.btn-edit-vehicle');
            if (editVehicleButton) {
                const vehicleId = editVehicleButton.dataset.id;
                const mockVehicleData = { id: vehicleId, plateNumber: '桂A' + vehicleId + '1234', type: '3', orgUnit: '1.2.2', status: '1', manager: '模拟 ' + vehicleId, phone: '1300000000' + vehicleId, address: '地址 ' + vehicleId, roadId: 'G72', startStake: 'K10+000', remarks: '编辑模拟数据' };
                vehicleModalInstance.openModal(true, mockVehicleData);
            }
            const deleteVehicleButton = event.target.closest('.btn-delete-vehicle');
            if (deleteVehicleButton) {
                const vehicleId = deleteVehicleButton.dataset.id;
                if (confirm(`确定要删除车辆 ID ${vehicleId} 吗？`)) { console.log("删除车辆 ID:", vehicleId); alert('删除车辆 ID: ' + vehicleId + ' (模拟)'); }
            }
            if (event.target.closest('#btnSearchVehicle')) {
                console.log('搜索车辆 (模拟)'); alert('触发车辆搜索 (模拟)');
            }


            // --- Tow Point Actions ---
            if (event.target.closest('#btnAddTowPoint')) {
                towPointModalInstance.openModal(false);
            }
             const editTowPointButton = event.target.closest('.btn-edit-tow-point');
             if (editTowPointButton) {
                 const towPointId = editTowPointButton.dataset.id;
                 const mockTowPointData = { id: towPointId, name: '模拟调度点 ' + towPointId, orgUnit: '1.1.1', roadId: 'G72', startMarker: 'K100+000', manager: '调度员 ' + towPointId, phone: '1310000000' + towPointId.substring(2), address: '调度点地址 ' + towPointId, remarks: '编辑模拟数据' };
                 towPointModalInstance.openModal(true, mockTowPointData);
             }
             const deleteTowPointButton = event.target.closest('.btn-delete-tow-point');
             if (deleteTowPointButton) {
                 const towPointId = deleteTowPointButton.dataset.id;
                 if (confirm(`确定要删除调度点 ID ${towPointId} 吗？`)) { console.log("删除调度点 ID:", towPointId); alert('删除调度点 ID: ' + towPointId + ' (模拟)'); }
             }
             if (event.target.closest('#btnSearchTowPoints')) {
                 console.log('搜索调度点 (模拟)'); alert('触发调度点搜索 (模拟)');
             }
             if (event.target.closest('#btnResetTowPointFilters')) {
                document.getElementById('tow_point_name_filter').value = '';
                document.getElementById('tow_point_road_id_filter').value = '';
                // Reset tow point filter Vue instance data if possible/needed
                // towPointFilterApp.selectedUnit = null; // This direct access might not work, would need method in Vue app
                console.log('重置调度点筛选 (模拟)'); alert('调度点筛选已重置 (模拟)');
             }
        });

    </script>
    <script id="material-module-scripts">
                // Material Modal Vue App
                const MaterialModalDefinition = {
            data() {
                return {
                    dialogVisible: false,
                    isEditMode: false,
                    materialForm: {
                        id: null,
                        name: '',
                        model: '',
                        type: '',
                        warehouseId: '',
                        quantity: 0,
                        unit: '',
                        status: '1', // Default to '正常'
                        remarks: '',
                        expiryDate: null
                    }
                };
            },
            methods: {
                openModal(isEdit = false, materialData = null) {
                    this.isEditMode = isEdit;
                    if (isEdit && materialData) {
                        this.materialForm = { ...materialData };
                    } else {
                        this.resetForm();
                    }
                    this.dialogVisible = true;
                },
                closeModal() {
                    this.dialogVisible = false;
                },
                submitForm() {
                    // In a real app, this.$refs.materialFormRef.validate would be used
                    console.log('物资模态框 - 表单提交:', this.materialForm);
                    // TODO: Add actual save/update logic here
                     alert('物资信息已' + (this.isEditMode ? '更新' : '添加') + ' (模拟)');
                    this.closeModal();
                },
                resetForm() {
                    this.materialForm = {
                        id: null, name: '', model: '', type: '', warehouseId: '',
                        quantity: 0, unit: '', status: '1', remarks: '', expiryDate: null
                    };
                    // if (this.$refs.materialFormRef) { // Similar to warehouse, refs might need careful handling
                    //     this.$refs.materialFormRef.clearValidate();
                    // }
                }
            }
        };
        const materialModalApp = Vue.createApp(MaterialModalDefinition);
        materialModalApp.use(ElementPlus);
        const materialModalInstance = materialModalApp.mount('#material-modal-app');

        // Event Listeners for Material Tab buttons
        document.getElementById('material-section').addEventListener('click', function(event) {
            if (event.target.closest('#btnAddMaterial')) {
                 materialModalInstance.openModal(false);
            }

            const editButton = event.target.closest('.btn-edit-material');
            if (editButton) {
                const materialId = editButton.dataset.id;
                console.log("编辑物资 ID:", materialId);
                const mockData = {
                    id: materialId,
                    name: `示例物资 ${materialId}`,
                    model: `型号 ${materialId}`,
                    type: (parseInt(materialId) % 6 + 1).toString(),
                    warehouseId: (parseInt(materialId) % 5 + 1).toString(),
                    quantity: parseInt(materialId) * 50,
                    unit: '件',
                    status: (parseInt(materialId) % 3 + 1).toString(),
                    remarks: `这是物资 ${materialId} 的详细备注。`,
                    expiryDate: `202${6 + (materialId % 2)}-${(materialId % 12 + 1).toString().padStart(2, '0')}-20`
                };
                materialModalInstance.openModal(true, mockData);
            }

            const deleteButton = event.target.closest('.btn-delete-material');
            if (deleteButton) {
                const materialId = deleteButton.dataset.id;
                if (confirm(`确定要删除物资 ID ${materialId} 吗？`)) {
                    console.log("删除物资 ID:", materialId);
                    alert('删除物资 ID: ' + materialId + ' (模拟)');
                }
            }

            if (event.target.closest('#btnSearchMaterials')) {
                const name = document.getElementById('material_name_filter').value;
                const model = document.getElementById('material_model_filter').value;
                const type = document.getElementById('material_type_filter').value;
                const warehouse = document.getElementById('material_warehouse_filter').value;
                const status = document.getElementById('material_status_filter').value;
                console.log('搜索物资:', { name, model, type, warehouse, status });
                alert('触发物资搜索 (模拟)');
            }
            if (event.target.closest('#btnResetMaterialFilters')) {
                 document.getElementById('material_name_filter').value = '';
                 document.getElementById('material_model_filter').value = '';
                 document.getElementById('material_type_filter').value = '';
                 document.getElementById('material_warehouse_filter').value = '';
                 document.getElementById('material_status_filter').value = '';
                 console.log('重置物资筛选条件');
                 alert('物资筛选已重置 (模拟)');
            }
            if (event.target.closest('#btnExportMaterials')) {
                console.log('导出物资列表');
                alert('触发导出物资列表操作 (模拟)');
            }
        });
    </script>

    <!-- 主标签页切换逻辑 -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const mainTabButtons = document.querySelectorAll('.main-tab-btn');
            const mainTabContents = document.querySelectorAll('.main-tab-content');

            // 确保初始时只有标记为 active 的标签页内容显示
            mainTabContents.forEach(content => {
                if (!content.classList.contains('active')) {
                    content.style.display = 'none';
                }
            });
            // 确保初始时标记为 active 的按钮正确，并且其对应内容显示
            const initialActiveButton = document.querySelector('.main-tab-btn.active');
            if (initialActiveButton) {
                const initialTargetTab = initialActiveButton.dataset.mainTab;
                const initialActiveContent = document.getElementById(initialTargetTab);
                if (initialActiveContent) {
                    initialActiveContent.style.display = 'block';
                }
            } else if (mainTabButtons.length > 0) { // Fallback if no button is initially active
                mainTabButtons[0].classList.add('active');
                const firstTabContentId = mainTabButtons[0].dataset.mainTab;
                const firstTabContent = document.getElementById(firstTabContentId);
                if (firstTabContent) {
                    firstTabContent.style.display = 'block';
                    firstTabContent.classList.add('active'); // Ensure content also gets active class
                }
            }


            mainTabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetMainTab = button.dataset.mainTab;

                    mainTabButtons.forEach(btn => {
                        btn.classList.remove('active');
                    });
                    button.classList.add('active');

                    mainTabContents.forEach(content => {
                        content.style.display = 'none'; // Hide all
                        content.classList.remove('active');
                    });
                    const activeContent = document.getElementById(targetMainTab);
                    if (activeContent) {
                        activeContent.style.display = 'block'; // Show target
                        activeContent.classList.add('active');
                    }
                });
            });
        });
    </script>

    <!-- 首先加载组件HTML定义 (导航栏, 侧边栏) -->
    <script src="js/navbarComponent.js"></script>
    <script src="js/sidebarComponent_emergency.js"></script>
    <!-- 然后加载注入组件和高亮链接的脚本 -->
    <script src="js/loadComponents.js"></script>
</body>
</html>
