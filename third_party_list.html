<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>应急救援队伍 - 应急管理系统</title>
  <!-- 引入样式 -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
  <style>
    html, body {
        height: 100%;
        margin: 0;
    }
    body {
      font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
    }
    .sidebar-menu-item {
      display: flex;
      align-items: center;
      padding: 0.5rem 1rem;
      color: #4b5563;
      transition: all 0.2s;
    }
    
    .sidebar-menu-item:hover, .sidebar-menu-item.active {
      background-color: #f3f4f6;
      color: #1f2937;
    }
    
    .sidebar-menu-item.active {
      border-left: 3px solid #2563eb;
    }
    
    .main-content {
      transition: margin-left 0.3s;
    }
    
    .sidebar {
      width: 250px;
      transition: all 0.3s;
      z-index: 40;
    }
    
    .sidebar.collapsed {
      width: 0px;
      transform: translateX(-100%);
    }
    
    body.sidebar-expanded .main-content {
      margin-left: 250px;
    }
    
    body.sidebar-collapsed .main-content {
      margin-left: 0;
    }
    
    @media (max-width: 768px) {
      body .main-content {
        margin-left: 0;
      }
      
      .sidebar {
        transform: translateX(-100%);
      }
      
      .sidebar.expanded {
        transform: translateX(0);
      }
    }
    
    /* 表格和状态标签样式 */
    .status-badge {
      padding: 0.25rem 0.5rem;
      border-radius: 9999px;
      font-size: 0.75rem;
      font-weight: 600;
      display: inline-block;
    }
    
    /* 自定义 Tree Select 样式 */
    .el-tree-select {
        width: 100% !important;
    }
    .el-select-dropdown__wrap {
        max-height: 400px;
    }
    .el-tree-node__content {
        height: 32px;
    }
    .el-tree-node__label {
        font-size: 14px;
    }
  </style>
</head>
<body class="bg-gray-100 min-h-screen">
  <!-- Removed old sidebar and header -->

  <!-- Navbar Placeholder -->
  <div id="navbar-placeholder"></div>

  <!-- Flex Container for Sidebar and Main Content -->
  <div class="flex-container h-full" style="display: flex;">
      <!-- Sidebar Placeholder -->
      <div id="sidebar-placeholder"></div>

      <!-- Main Content Area -->
      <main id="main-content" class="flex-grow pb-8 px-6 bg-gray-100">
          <!-- Removed old wrapper -->
          <div class="py-6">
              <!-- 页面标题 -->
              <div class="flex justify-between items-center mb-6">
                <div>
                  <h2 class="text-2xl font-semibold text-gray-800">应急救援队伍人员列表</h2>
                  <p class="text-sm text-gray-500 mt-1">管理系统内的应急救援队伍人员信息</p>
                </div>
                <button id="btnAdd" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  <i class="fas fa-plus mr-2"></i>添加应急救援人员
                </button>
              </div>

              <!-- 过滤栏 -->
              <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                  <div>
                    <label for="filterName" class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                    <input type="text" id="filterName" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="输入姓名关键词">
                  </div>
                  <div>
                    <label for="orgFilter" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                    <div id="orgFilterApp">
                        <el-tree-select
                            v-model="selectedUnit"
                            :data="orgOptions"
                            :multiple="false"
                            :check-strictly="true"
                            :props="{ value: 'value', label: 'label', children: 'children' }"
                            placeholder="请选择单位"
                            class="block w-full"
                            clearable
                            @change="handleOrgChange"
                        />
                    </div>
                  </div>
                  <div>
                    <label for="filterThirdPartyCompany" class="block text-sm font-medium text-gray-700 mb-1">所属三方公司</label>
                    <select id="filterThirdPartyCompany" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                      <option value="">全部公司</option>
                      <option value="companyA">XX消防技术服务公司</option>
                      <option value="companyB">XX医疗急救中心</option>
                      <option value="companyC">XX电力工程公司</option>
                      <option value="companyD">XX运输有限公司</option>
                      <option value="companyE">XX环境监测评估公司</option>
                    </select>
                  </div>
                  <div class="flex items-end space-x-2 col-start-5">
                    <button id="btnFilter" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                      <i class="fas fa-search mr-1"></i> 查询
                    </button>
                    <button id="btnResetFilter" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                      <i class="fas fa-undo mr-1"></i> 重置
                    </button>
                  </div>
                </div>
              </div>

              <!-- 数据表格 -->
              <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="overflow-x-auto">
                  <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                      <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属三方公司</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职务</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系电话</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">技能特长</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                      <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">王军</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">广西壮族自治区交通运输厅</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">xxxx</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">现场负责人</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13900139001</td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs" title="灭火救援, 现场指挥">灭火救援, 现场指挥</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">正常</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                          <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit" data-id="1">
                            <i class="fas fa-edit"></i> 编辑
                          </button>
                          <button class="text-red-600 hover:text-red-900 btn-delete" data-id="1">
                            <i class="fas fa-trash-alt"></i> 删除
                          </button>
                        </td>
                      </tr>
                      <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">李红</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">自治区公路发展中心</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">xxxx</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">医护队长</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13800138002</td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs" title="急救处理, 包扎">急救处理, 包扎</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">正常</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                          <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit" data-id="2">
                            <i class="fas fa-edit"></i> 编辑
                          </button>
                          <button class="text-red-600 hover:text-red-900 btn-delete" data-id="2">
                            <i class="fas fa-trash-alt"></i> 删除
                          </button>
                        </td>
                      </tr>
                      <tr>
                         <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">张伟</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">自治区高速公路发展中心</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">xxxx</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">项目经理</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13800138003</td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs" title="电力抢修, 高压作业">电力抢修, 高压作业</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">停用</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                          <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit" data-id="3">
                            <i class="fas fa-edit"></i> 编辑
                          </button>
                          <button class="text-red-600 hover:text-red-900 btn-delete" data-id="3">
                            <i class="fas fa-trash-alt"></i> 删除
                          </button>
                        </td>
                      </tr>
                      <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">陈晓</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钦州市交通运输局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">xxxx</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">调度员</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13800138004</td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs" title="车辆调度, 路线规划">车辆调度, 路线规划</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">正常</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                          <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit" data-id="4">
                            <i class="fas fa-edit"></i> 编辑
                          </button>
                          <button class="text-red-600 hover:text-red-900 btn-delete" data-id="4">
                            <i class="fas fa-trash-alt"></i> 删除
                          </button>
                        </td>
                      </tr>
                      <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">赵明</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">南宁市交通运输局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">xxxx</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">技术工程师</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13800138005</td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs" title="环境监测, 报告撰写">环境监测, 报告撰写</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">正常</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                          <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit" data-id="5">
                            <i class="fas fa-edit"></i> 编辑
                          </button>
                          <button class="text-red-600 hover:text-red-900 btn-delete" data-id="5">
                            <i class="fas fa-trash-alt"></i> 删除
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                
                <!-- 分页控件 -->
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            上一页
                        </a>
                        <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            下一页
                        </a>
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                显示第 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">12</span> 条记录
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">上一页</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                                <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    1
                                </a>
                                <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    2
                                </a>
                                <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    3
                                </a>
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                    ...
                                </span>
                                <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    8
                                </a>
                                <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">下一页</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </nav>
                        </div>
                    </div>
                </div>
              </div>
          </div> <!-- Close py-6 -->
      </main>
  </div> <!-- Close flex-container -->

  <!-- Modals outside main content -->
  <!-- 添加三方人员模态框 -->
  <div id="addThirdPartyModal">
      <el-dialog
          v-model="dialogVisible"
          :title="modalTitle"
          width="60%"
          @closed="resetForm"
          :close-on-click-modal="false"
      >
          <el-form :model="thirdPartyForm" ref="thirdPartyFormRef" label-width="120px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="姓名" required prop="name">
                            <el-input v-model="thirdPartyForm.name" placeholder="请输入人员姓名"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="性别" required prop="gender">
                             <el-radio-group v-model="thirdPartyForm.gender">
                                <el-radio label="male">男</el-radio>
                                <el-radio label="female">女</el-radio>
                             </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                     <el-col :span="12">
                        <el-form-item label="所属单位" prop="organizationId">
                            <el-tree-select
                                v-model="thirdPartyForm.organizationId"
                                :data="orgOptions"
                                :multiple="false"
                                check-strictly
                                placeholder="请选择所属单位 (非必填)"
                                class="w-full"
                             />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="所属三方公司" required prop="company">
                            <el-select v-model="thirdPartyForm.company" placeholder="请选择所属公司" class="w-full">
                                <el-option label="XX消防技术服务公司" value="companyA"></el-option>
                                <el-option label="XX医疗急救中心" value="companyB"></el-option>
                                <el-option label="XX电力工程公司" value="companyC"></el-option>
                                <el-option label="XX运输有限公司" value="companyD"></el-option>
                                <el-option label="其他" value="other"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                     <el-col :span="12">
                        <el-form-item label="职务/岗位" prop="position">
                            <el-input v-model="thirdPartyForm.position" placeholder="请输入职务或岗位"></el-input>
                        </el-form-item>
                    </el-col>
                     <el-col :span="12">
                        <el-form-item label="联系电话" required prop="phone">
                            <el-input v-model="thirdPartyForm.phone" placeholder="请输入联系电话"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                     <el-col :span="12">
                        <el-form-item label="状态" required prop="status">
                            <el-select v-model="thirdPartyForm.status" placeholder="请选择状态" class="w-full">
                                <el-option label="可用" value="available"></el-option>
                                <el-option label="任务中" value="on_mission"></el-option>
                                <el-option label="不可用" value="unavailable"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                     <el-col :span="12">
                        <el-form-item label="技能特长" prop="skills">
                            <el-input type="textarea" v-model="thirdPartyForm.skills" placeholder="请输入人员的技能或特长，多个请用逗号分隔"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="closeModal">取消</el-button>
                    <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                </span>
            </template>
      </el-dialog>
  </div>

  <!-- 删除确认模态框 -->
  <div id="deleteConfirmModal" class="fixed inset-0 z-[100] hidden"> <!-- Increased z-index -->
    <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
    <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
               <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">确认删除</h3>
                </div>
                <div class="px-6 py-4">
                    <p class="text-sm text-gray-700">您确定要删除该三方人员信息吗？此操作无法撤销。</p>
                </div>
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                    <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-modal">
                        取消
                    </button>
                    <button id="btnConfirmDelete" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                        删除
                    </button>
                </div>
            </div>
        </div>
    </div>
  </div>

  <!-- Load Libraries First -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
  <script src="https://unpkg.com/element-plus"></script>

  <!-- Page Specific Scripts -->
  <script>
    // Define unit options globally for reuse
    const standardUnitOptions = [
        { value: '1', label: '广西壮族自治区交通运输厅', children: [
            { value: '1.1', label: '直属事业单位及专项机构', children: [
                { value: '1.1.1', label: '自治区公路发展中心' },
                { value: '1.1.2', label: '自治区高速公路发展中心' },
                { value: '1.1.3', label: '自治区道路运输发展中心' }
            ]},
            { value: '1.2', label: '市级交通运输局', children: [
                { value: '1.2.1', label: '钦州市交通运输局' },
                { value: '1.2.2', label: '南宁市交通运输局' },
                { value: '1.2.3', label: '玉林市交通运输局' }
            ]}
        ]},
        { value: '2', label: '省消防总队' },
        // Add other relevant orgs for third-party context if needed
        // Example:
        { value: 'SF001', label: 'XX消防技术服务公司' },
        { value: 'MED001', label: 'XX医疗急救中心' },
        { value: 'POW001', label: 'XX电力工程公司' },
        { value: 'TRA001', label: 'XX运输有限公司' },
        { value: 'ENV001', label: 'XX环境监测评估公司' }
    ];

    // Vue App for Org Filter (if needed, or reuse logic/data)
    // Assuming it's similar to expert_list
    const OrgFilterApp = {
        data() {
            return {
                selectedOrgs: [],
                orgOptions: standardUnitOptions
            }
        },
        methods: {
            handleOrgChange(value) {
                console.log('筛选器选中的单位:', value);
            }
        }
    };

    // Vue App for Third Party Modal
    const ThirdPartyModalApp = {
        data() {
            return {
                dialogVisible: false,
                isEditMode: false,
                modalTitle: '添加三方人员',
                thirdPartyForm: {
                    id: null,
                    name: '',
                    gender: 'male',
                    company: '',
                    position: '',
                    phone: '',
                    status: 'available',
                    remarks: '',
                    organizationId: null
                },
                orgOptions: standardUnitOptions
            };
        },
        methods: {
            openModal(isEdit = false, personData = null) {
                this.isEditMode = isEdit;
                this.modalTitle = isEdit ? '编辑三方人员' : '添加三方人员';
                if (isEdit && personData) {
                    this.thirdPartyForm = { ...this.thirdPartyForm, ...personData };
                } else {
                    this.resetForm();
                }
                this.dialogVisible = true;
            },
            closeModal() {
                this.dialogVisible = false;
            },
            submitForm() {
                 console.log('Submitting third party form...');
                 // Add validation logic here if needed
                 if (!this.thirdPartyForm.name || !this.thirdPartyForm.company || !this.thirdPartyForm.phone) {
                     ElementPlus.ElMessage.error('请填写姓名、所属公司和联系电话!');
                     return;
                 }
                 console.log('Form Submitted:', this.thirdPartyForm);
                 // TODO: Add actual save/update logic here
                 this.closeModal();
                 ElementPlus.ElMessage.success(this.isEditMode ? '三方人员信息更新成功！' : '三方人员添加成功！');
                 // Optionally refresh table
            },
            resetForm() {
                this.thirdPartyForm = {
                    id: null, name: '', gender: 'male', company: '', position: '',
                    phone: '', status: 'available', remarks: '', organizationId: null
                };
                 // if (this.$refs.thirdPartyFormRef) { ... }
            }
        }
    };

    const thirdPartyModalVm = Vue.createApp(ThirdPartyModalApp);
    thirdPartyModalVm.use(ElementPlus);
    const mountedThirdPartyModal = thirdPartyModalVm.mount('#addThirdPartyModal');

    // Mount Org Filter App (if #orgFilterApp exists)
    const orgFilterElement = document.getElementById('orgFilterApp');
    if (orgFilterElement) {
        const orgFilterVm = Vue.createApp(OrgFilterApp);
        orgFilterVm.use(ElementPlus);
        orgFilterVm.mount('#orgFilterApp');
    }

    // --- Event Listeners --- 
    document.addEventListener('DOMContentLoaded', function() {
        // Add button
        const btnAdd = document.getElementById('btnAdd');
        if (btnAdd) {
            btnAdd.addEventListener('click', () => {
                mountedThirdPartyModal.openModal(false);
            });
        }

        // Close modals
        const deleteConfirmModal = document.getElementById('deleteConfirmModal');
        document.querySelectorAll('.btn-close-modal').forEach(button => {
            button.addEventListener('click', () => {
                 if(deleteConfirmModal) deleteConfirmModal.classList.add('hidden');
            });
        });

        // Table button actions (delegation)
        const tableBody = document.querySelector('tbody');
        if (tableBody) {
            tableBody.addEventListener('click', (event) => {
                const button = event.target.closest('button');
                if (!button) return;
                const personId = button.dataset.id;

                if (button.classList.contains('btn-edit')) {
                    console.log('Edit third party:', personId);
                     // Mock data for editing
                     const mockData = {
                        id: personId, name: '李四', gender: 'male', company: 'companyA',
                        position: '现场队长', phone: '13988887777', status: 'available',
                        skills: '高空作业, 焊接'
                     };
                    mountedThirdPartyModal.openModal(true, mockData);
                } else if (button.classList.contains('btn-delete')) {
                     console.log('Delete third party:', personId);
                     if(deleteConfirmModal) {
                         deleteConfirmModal.dataset.deleteId = personId;
                         deleteConfirmModal.classList.remove('hidden');
                     }
                }
            });
        }
        
        // Confirm Delete button
        const btnConfirmDelete = document.getElementById('btnConfirmDelete');
        if (btnConfirmDelete && deleteConfirmModal) {
            btnConfirmDelete.addEventListener('click', () => {
                const personIdToDelete = deleteConfirmModal.dataset.deleteId;
                if (personIdToDelete) {
                    console.log('Confirm delete third party:', personIdToDelete);
                     // TODO: Add actual delete API call
                     ElementPlus.ElMessage.success('三方人员删除成功！');
                     deleteConfirmModal.classList.add('hidden');
                     delete deleteConfirmModal.dataset.deleteId;
                     // Optionally refresh table
                 }
            });
        }
        
        // Filter/Reset Buttons (Keep existing or add logic)
        // const btnFilter = document.getElementById('btnFilter'); ...
        // const btnResetFilter = document.getElementById('btnResetFilter'); ...
    });
  </script>

  <!-- Load component HTML first -->
  <script src="js/navbarComponent.js"></script>
  <script src="js/sidebarComponent.js"></script>
  <!-- Then load the script to inject them and highlight links -->
  <script src="js/loadComponents.js"></script>
</body>
</html> 