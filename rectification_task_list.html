<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>整改任务管理 - 应急管理系统</title>
    <!-- 引入样式 -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- 添加 Element Plus 样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        html, body { /* Added for full height */
            height: 100%;
            margin: 0;
        }
         body { /* Keep base font */
             font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
        }
        /* 任务状态样式 */
        .status-badge {
          padding: 0.25rem 0.5rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 600;
          display: inline-block;
          text-align: center;
          min-width: 4.5rem; /* 调整宽度以适应文字 */
        }
        .status-pending { background-color: #FEF3C7; color: #92400E; } /* 黄色 - 待处理 */
        .status-progress { background-color: #DBEAFE; color: #1E40AF; } /* 蓝色 - 整改中 */
        .status-completed { background-color: #DEF7EC; color: #03543E; } /* 绿色 - 已完成 */
        .status-overdue { background-color: #FEE2E2; color: #991B1B; } /* 红色 - 已逾期 */

        /* 自定义 Tree Select 和 Date Picker 样式 */
        .el-tree-select, .el-date-editor {
            width: 100% !important;
        }
        .el-select-dropdown__wrap, .el-picker-panel__content {
            max-height: 400px;
        }
        .el-tree-node__content {
            height: 32px;
        }
        .el-tree-node__label {
            font-size: 14px;
        }
        /* Add specific styles for upload list if needed */
        .upload-list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Navbar Placeholder -->
    <div id="navbar-placeholder"></div>

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container h-full" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area -->
        <main id="main-content" class="flex-grow pb-8 px-6 bg-gray-100">
            <div class="py-6">
                <!-- 页面标题 -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 class="text-2xl font-semibold text-gray-800">整改任务列表</h2>
                        <p class="text-sm text-gray-500 mt-1">跟踪和管理隐患整改任务的进度</p>
                    </div>
                    <!-- Add New Task Button -->
                    <button id="btnAddRectificationTask" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                        <i class="fas fa-plus mr-2"></i> 添加任务
                    </button>
                </div>

                <!-- 过滤栏 -->
                <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label for="filterTaskStatus" class="block text-sm font-medium text-gray-700 mb-1">任务状态</label>
                            <select id="filterTaskStatus" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                <option value="">全部</option>
                                <option value="pending">待处理</option>
                                <option value="progress">整改中</option>
                                <option value="completed">已完成</option>
                                <option value="overdue">已逾期</option>
                            </select>
                        </div>
                        <div>
                            <label for="filterRespUnit" class="block text-sm font-medium text-gray-700 mb-1">责任单位</label>
                            <div id="filterOrgApp">
                                 <el-tree-select
                                    v-model="selectedUnit"
                                    :data="unitOptions"
                                    :multiple="false"
                                    :check-strictly="true"
                                    :props="{ value: 'value', label: 'label', children: 'children' }"
                                    placeholder="请选择责任单位"
                                    class="block w-full"
                                    clearable
                                    @change="handleFilterOrgChange"
                                />
                            </div>
                        </div>
                        <div>
                            <label for="filterDeadline" class="block text-sm font-medium text-gray-700 mb-1">整改期限</label>
                            <div id="filterDateApp">
                                <el-date-picker
                                    v-model="deadlineRange"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    class="block w-full"
                                    value-format="YYYY-MM-DD"
                                    @change="handleDateChange"
                                />
                            </div>
                        </div>
                        <div class="flex items-end space-x-2 justify-end">
                            <button id="btnFilter" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                              <i class="fas fa-search mr-1"></i> 查询
                            </button>
                            <button id="btnResetFilter" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                              <i class="fas fa-undo mr-1"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务ID</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联隐患</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">责任单位</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">责任人</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建日期</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">整改期限</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!-- 示例数据行 -->
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TASK001</td>
                                    <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs"><a href="hazard_check_list.html?hazard_id=1" class="text-blue-600 hover:underline" title="ID:1, K1500+200处边坡有落石风险">隐患ID:1</a></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">南宁市交通运输局</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李工</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-28</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-08-10</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-pending">待处理</span></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                        <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="TASK001" title="查看"><i class="fas fa-eye"></i></button>
                                        <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="TASK001" title="编辑/更新状态"><i class="fas fa-edit"></i></button>
                                        <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-complete" data-id="TASK001" title="标记完成"><i class="fas fa-check-circle"></i></button>
                                        <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete" data-id="TASK001" title="删除"><i class="fas fa-trash-alt"></i></button>
                                    </td>
                                </tr>
                                 <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TASK002</td>
                                    <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs"><a href="hazard_check_list.html?hazard_id=2" class="text-blue-600 hover:underline" title="ID:2, 桥梁限高标识不清">隐患ID:2</a></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钦州市交通运输局</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王工</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-29</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-08-05</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-progress">整改中</span></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                         <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="TASK002" title="查看"><i class="fas fa-eye"></i></button>
                                        <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="TASK002" title="编辑/更新状态"><i class="fas fa-edit"></i></button>
                                        <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-complete" data-id="TASK002" title="标记完成"><i class="fas fa-check-circle"></i></button>
                                        <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete" data-id="TASK002" title="删除"><i class="fas fa-trash-alt"></i></button>
                                    </td>
                                </tr>
                                  <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TASK003</td>
                                    <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs"><a href="hazard_check_list.html?hazard_id=3" class="text-blue-600 hover:underline" title="ID:3, XX桥梁伸缩缝堵塞">隐患ID:3</a></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">玉林市交通运输局</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张工</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-20</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-25</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-overdue">已逾期</span></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                        <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="TASK003" title="查看"><i class="fas fa-eye"></i></button>
                                        <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="TASK003" title="编辑/更新状态"><i class="fas fa-edit"></i></button>
                                        <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-complete" data-id="TASK003" title="标记完成"><i class="fas fa-check-circle"></i></button>
                                        <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete" data-id="TASK003" title="删除"><i class="fas fa-trash-alt"></i></button>
                                    </td>
                                </tr>
                                <!-- 更多数据行 -->
                            </tbody>
                        </table>
                    </div>
                    <!-- 分页 (与 hazard_check_list.html 一致) -->
                    <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">3</span> 条记录
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <!-- ... 分页控件 ... -->
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">上一页</span>
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-100">
                                        1
                                    </a>
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">下一页</span>
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 查看/编辑任务模态框 -->
    <div id="taskModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800" id="modalTitle">查看/编辑整改任务</h3>
                <button class="text-gray-400 hover:text-gray-500 focus:outline-none btn-close-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="px-6 py-4 max-h-[70vh] overflow-y-auto">
                <form id="taskForm">
                    <input type="hidden" id="taskId" name="taskId">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 pb-4 border-b">
                         <div><strong class="text-gray-600 block mb-1">任务ID:</strong> <span id="view-taskId"></span></div>
                         <div><strong class="text-gray-600 block mb-1">关联隐患ID:</strong> <a id="view-hazardLink" href="#" class="text-blue-600 hover:underline"></a></div>
                         <div>
                            <label for="modalRespUnit" class="block text-sm font-medium text-gray-700 mb-1">责任单位 <span class="text-red-500">*</span></label>
                            <div id="modalOrgApp">
                                <el-tree-select
                                    v-model="selectedUnit"
                                    :data="unitOptions"
                                    :multiple="false"
                                    :check-strictly="true"
                                    :props="{ value: 'value', label: 'label', children: 'children' }"
                                    placeholder="请选择单位"
                                    class="block w-full"
                                    clearable
                                    @change="handleModalOrgChange"
                                />
                            </div>
                        </div>
                         <div>
                             <label for="modalRespPerson" class="block text-sm font-medium text-gray-700 mb-1">责任人</label>
                             <input type="text" id="modalRespPerson" name="resp_person" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                         </div>
                         <div>
                             <label for="modalDeadline" class="block text-sm font-medium text-gray-700 mb-1">整改期限 <span class="text-red-500">*</span></label>
                             <div id="modalDateApp">
                                 <el-date-picker
                                    v-model="deadline"
                                    type="date"
                                    placeholder="选择日期"
                                    class="block w-full"
                                    value-format="YYYY-MM-DD"
                                />
                             </div>
                         </div>
                          <div>
                             <label for="modalTaskStatus" class="block text-sm font-medium text-gray-700 mb-1">任务状态 <span class="text-red-500">*</span></label>
                             <select id="modalTaskStatus" name="task_status" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="pending">待处理</option>
                                <option value="progress">整改中</option>
                                <option value="completed">已完成</option>
                                <option value="overdue">已逾期</option>
                             </select>
                         </div>
                    </div>
                    <div>
                        <label for="modalRemarks" class="block text-sm font-medium text-gray-700 mb-1">整改进度/备注</label>
                        <textarea id="modalRemarks" name="remarks" rows="4" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
                    </div>
                     <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">上传整改附件</label>
                        <input type="file" id="modalTaskFiles" name="task_files" multiple class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                        <div id="uploadedTaskFilesList" class="mt-2 text-sm text-gray-600"></div>
                    </div>
                </form>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-modal">
                  取消
                </button>
                <button id="btnSaveTask" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  保存更新
                </button>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 (与 hazard_check_list.html 一致) -->
    <div id="deleteConfirmModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center hidden">
         <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">确认删除</h3>
            </div>
            <div class="px-6 py-4">
                <p class="text-sm text-gray-700">您确定要删除这条整改任务吗？此操作无法撤销。</p>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-modal">
                  取消
                </button>
                <button id="btnConfirmDelete" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                  删除
                </button>
            </div>
        </div>
    </div>

    <!-- Load Libraries -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>

    <!-- Page Specific Scripts (Keep original logic) -->
    <script>
        // Define unit options globally for reuse
        const standardUnitOptions = [
            { value: '1', label: '广西壮族自治区交通运输厅', children: [
                { value: '1.1', label: '直属事业单位及专项机构', children: [
                    { value: '1.1.1', label: '自治区公路发展中心' },
                    { value: '1.1.2', label: '自治区高速公路发展中心' },
                    { value: '1.1.3', label: '自治区道路运输发展中心' }
                ]},
                { value: '1.2', label: '市级交通运输局', children: [
                    { value: '1.2.1', label: '钦州市交通运输局' },
                    { value: '1.2.2', label: '南宁市交通运输局' },
                    { value: '1.2.3', label: '玉林市交通运输局' }
                ]}
            ]}
            // Add other top-level orgs if needed
        ];

        // Vue for Filter Org Unit
        const filterOrgApp = Vue.createApp({
            data() { 
                return { 
                    selectedUnit: null, // Changed from selectedUnits
                    unitOptions: standardUnitOptions // Use standard data
                }
             },
            methods: { 
                handleFilterOrgChange(value) { 
                    console.log('Filter Org Unit:', value); 
                }
             }
        }).use(ElementPlus);
        filterOrgApp.mount('#filterOrgApp');

        // Vue for Filter Date Range
        const filterDateApp = Vue.createApp({
            data() { return { deadlineRange: [] } },
            methods: { handleDateChange(value) { console.log('Filter Deadline Range:', value); } }
        }).use(ElementPlus);
        filterDateApp.mount('#filterDateApp');

        // Vue for Modal Org Unit
        const modalOrgApp = Vue.createApp({
            data() { 
                return { 
                    selectedUnit: null, 
                    unitOptions: standardUnitOptions // Use standard data
                }
             },
            methods: { 
                handleModalOrgChange(value) { 
                    console.log('Modal Org Unit:', value); 
                } 
            }
        }).use(ElementPlus);
        modalOrgApp.mount('#modalOrgApp');

        // Vue for Modal Deadline
        const modalDateApp = Vue.createApp({
            data() { return { deadline: '' } }
        }).use(ElementPlus);
        modalDateApp.mount('#modalDateApp');

        document.addEventListener('DOMContentLoaded', function() {
            const taskModal = document.getElementById('taskModal');
            const deleteConfirmModal = document.getElementById('deleteConfirmModal');
            const taskForm = document.getElementById('taskForm');
            const modalTitle = document.getElementById('modalTitle');
            let currentTaskId = null;
            let taskIdToDelete = null;

            const openModal = (modal) => modal.classList.remove('hidden');
            const closeModal = (modal) => modal.classList.add('hidden');

            // Close modals
            document.querySelectorAll('.btn-close-modal').forEach(button => {
                button.addEventListener('click', () => {
                    closeModal(taskModal);
                    closeModal(deleteConfirmModal);
                });
            });

             // Table buttons
            document.querySelector('tbody')?.addEventListener('click', (event) => {
                const target = event.target.closest('button');
                if (!target) return;
                const id = target.getAttribute('data-id');

                if (target.classList.contains('btn-view') || target.classList.contains('btn-edit')) {
                    console.log('View/Edit Task ID:', id);
                    currentTaskId = id;
                    populateTaskModal(id); // Populate modal for viewing/editing
                    openModal(taskModal);
                } else if (target.classList.contains('btn-complete')) {
                    console.log('Complete Task ID:', id);
                    if (confirm(`确认将任务 ${id} 标记为完成吗?`)) {
                        // TODO: Send request to mark task as complete
                        alert(`任务 ${id} 已标记为完成 (模拟)`);
                        // TODO: Refresh list or update row status
                    }
                } else if (target.classList.contains('btn-delete')) {
                    console.log('Delete Task ID:', id);
                    taskIdToDelete = id;
                    openModal(deleteConfirmModal);
                }
            });

             // Save Task button
             document.getElementById('btnSaveTask')?.addEventListener('click', () => {
                // TODO: Form validation
                 const formData = new FormData(taskForm);
                 formData.append('organization_unit_id', modalOrgApp._instance.data.selectedUnit); // Get unit ID
                 formData.append('deadline', modalDateApp._instance.data.deadline); // Get deadline date

                // Handle files
                const taskFiles = document.getElementById('modalTaskFiles').files;
                for(let i=0; i<taskFiles.length; i++){ formData.append('task_files[]', taskFiles[i].name); } // Example: send names

                 const data = Object.fromEntries(formData.entries());
                 console.log('Saving Task ID:', currentTaskId, 'Data:', data);
                 // TODO: Send update request to backend
                 alert(`任务 ${currentTaskId} 更新成功 (模拟)`);
                 closeModal(taskModal);
                 // TODO: Refresh list
             });

             // Confirm Delete button
            document.getElementById('btnConfirmDelete')?.addEventListener('click', () => {
                if (taskIdToDelete) {
                    console.log('Confirm Delete Task ID:', taskIdToDelete);
                    // TODO: Send delete request
                    alert(`任务 ${taskIdToDelete} 删除成功 (模拟)`);
                    taskIdToDelete = null;
                    closeModal(deleteConfirmModal);
                    // TODO: Refresh list
                }
            });

             // Filter buttons
            document.getElementById('btnFilter')?.addEventListener('click', () => {
                const filterData = {
                    status: document.getElementById('filterTaskStatus').value,
                    units: filterOrgApp._instance.data.selectedUnit,
                    deadlineStart: filterDateApp._instance.data.deadlineRange ? filterDateApp._instance.data.deadlineRange[0] : '',
                    deadlineEnd: filterDateApp._instance.data.deadlineRange ? filterDateApp._instance.data.deadlineRange[1] : ''
                };
                console.log('Apply Filter:', filterData);
                 alert('应用筛选条件 (模拟)');
                // TODO: Apply filter and refresh list
            });

            document.getElementById('btnResetFilter')?.addEventListener('click', () => {
                document.getElementById('filterTaskStatus').value = '';
                filterOrgApp._instance.data.selectedUnit = null;
                filterDateApp._instance.data.deadlineRange = [];
                console.log('Reset Filter');
                alert('重置筛选条件 (模拟)');
                 // TODO: Reset filter and refresh list
            });

             // File upload display
            document.getElementById('modalTaskFiles')?.addEventListener('change', function(e) {
                displayUploadedFiles(e.target.files, 'uploadedTaskFilesList');
            });

             // --- Add Button Listener ---
             const btnAddTask = document.getElementById('btnAddRectificationTask');
             if (btnAddTask) {
                 btnAddTask.addEventListener('click', () => {
                     currentTaskId = null; // Indicate add mode
                     modalTitle.textContent = '添加整改任务'; // Set title for adding
                     taskForm.reset(); // Reset form fields
                     clearUploadedFiles('uploadedTaskFilesList'); // Clear file list display
                     // Reset Vue component data for modal
                     if (modalOrgApp && modalOrgApp._instance) {
                         modalOrgApp._instance.data.selectedUnit = null;
                     }
                     if (modalDateApp && modalDateApp._instance) {
                         modalDateApp._instance.data.deadline = '';
                     }
                      // Clear specific fields not handled by reset if needed
                     document.getElementById('view-taskId').textContent = '系统自动生成'; // Indicate auto-generated ID
                     const hazardLink = document.getElementById('view-hazardLink');
                     hazardLink.textContent = '请在下方选择或关联'; // Or maybe hide this part for adding? Needs clarification.
                     hazardLink.removeAttribute('href');

                     openModal(taskModal);
                 });
             }

        });

        // --- Helper Functions ---
        function populateTaskModal(taskId) {
            console.log(`Populating modal for Task ID: ${taskId}`);
            modalTitle.textContent = '编辑整改任务'; // Set title for editing
            taskForm.reset(); // Reset form first
            clearUploadedFiles('uploadedTaskFilesList');
            document.getElementById('taskId').value = taskId;
            document.getElementById('view-taskId').textContent = taskId;
            // Simulate fetching data
            let hazardId, respUnitId, respPerson, deadline, status, remarks;
            if (taskId === 'TASK001') {
                hazardId = '1'; respUnitId = '1.2.2'; respPerson = '李工'; deadline = '2024-08-10'; status = 'pending'; remarks = '尚未开始处理。';
            } else if (taskId === 'TASK002') {
                hazardId = '2'; respUnitId = '1.2.1'; respPerson = '王工'; deadline = '2024-08-05'; status = 'progress'; remarks = '已联系施工队，准备更换标识。';
            } else {
                 hazardId = '3'; respUnitId = '1.2.3'; respPerson = '张工'; deadline = '2024-07-25'; status = 'overdue'; remarks = '已过整改期，未完成。';
            }

            const hazardLink = document.getElementById('view-hazardLink');
            hazardLink.href = `hazard_check_list.html?hazard_id=${hazardId}`;
            hazardLink.textContent = `隐患ID:${hazardId}`;
            modalOrgApp._instance.data.selectedUnit = respUnitId; // Set tree select value
            document.getElementById('modalRespPerson').value = respPerson;
            modalDateApp._instance.data.deadline = deadline; // Set date picker value
            document.getElementById('modalTaskStatus').value = status;
            document.getElementById('modalRemarks').value = remarks;
            // Simulate existing files
            const filesList = document.getElementById('uploadedTaskFilesList');
            filesList.innerHTML += `<div class="upload-list-item"><span>previous_report.pdf</span> <button type="button" class="text-red-500 text-xs" onclick="this.parentNode.remove()">删除</button></div>`;
        }

        function displayUploadedFiles(files, listElementId) {
             const listElement = document.getElementById(listElementId);
             if (!listElement) return;
             // listElement.innerHTML = ''; // Option to clear existing
             for (let i = 0; i < files.length; i++) {
                 const file = files[i];
                 const listItem = document.createElement('div');
                 listItem.classList.add('upload-list-item');
                 listItem.innerHTML = `<span>${file.name}</span> <button type="button" class="text-red-500 text-xs" onclick="this.parentNode.remove()">删除</button>`;
                 listElement.appendChild(listItem);
             }
         }
         function clearUploadedFiles(listElementId) {
              const listElement = document.getElementById(listElementId);
              if (listElement) listElement.innerHTML = '';
         }

    </script>

    <!-- Load component HTML first -->
    <script src="js/navbarComponent_risk.js"></script>
    <script src="js/sidebarComponent_risk.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>
</body>
</html> 