<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物资管理 - 应急管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- 添加 Element Plus 样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        html, body {
            height: 100%;
            margin: 0;
        }
        .sidebar-menu-item { /* Keep for reference */
            display: flex;
            align-items: center;
            padding: 0.75rem 1.25rem;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            color: #4a5568;
        }
        .sidebar-menu-item:hover {
            background-color: rgba(59, 130, 246, 0.1);
        }
         /* Active state handled by JS */
         /* Removed old layout styles */
    </style>
</head>
<body class="bg-gray-100 min-h-screen">

     <!-- Navbar Placeholder -->
    <div id="navbar-placeholder"></div>

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container h-full" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area -->
        <main id="main-content" class="flex-grow pb-8 px-6 bg-gray-100">
        <div class="py-6">
            <!-- 页面标题和操作按钮 -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-bold text-gray-800">应急物资管理</h2>
                    <p class="text-gray-600 mt-1">管理应急物资库存信息，支持搜索、筛选和库存维护</p>
                </div>
                <div class="flex">
                    <button class="mr-3 bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-file-export mr-2"></i> 导出
                    </button>
                        <button id="btnAddMaterial" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-plus mr-2"></i> 新增物资
                    </button>
                </div>
            </div>
            
            <!-- 搜索和筛选区域 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="material_name" class="block text-sm font-medium text-gray-700 mb-1">物资名称</label>
                        <input type="text" id="material_name" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入物资名称关键字">
                    </div>
                    
                    <div>
                        <label for="material_model" class="block text-sm font-medium text-gray-700 mb-1">规格型号</label>
                        <input type="text" id="material_model" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入规格型号">
                    </div>
                    
                    <div>
                        <label for="material_type" class="block text-sm font-medium text-gray-700 mb-1">物资类别</label>
                        <select id="material_type" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="">所有类别</option>
                            <option value="1">防疫物资</option>
                            <option value="2">防洪物资</option>
                            <option value="3">消防物资</option>
                            <option value="4">救灾物资</option>
                            <option value="5">通信设备</option>
                            <option value="6">应急装备</option>
                        </select>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-4">
                    <div>
                        <label for="warehouse" class="block text-sm font-medium text-gray-700 mb-1">所属仓库</label>
                        <select id="warehouse" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                <option value="">所有仓库</option>
                                <option value="1">中心仓库</option>
                            <option value="2">东区仓库</option>
                            <option value="3">南区仓库</option>
                            <option value="4">西区仓库</option>
                            <option value="5">北区仓库</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                        <select id="status" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="">所有状态</option>
                            <option value="1">正常</option>
                            <option value="2">维修中</option>
                            <option value="3">报废</option>
                        </select>
                    </div>
                    
                    <div class="flex items-end">
                        <div class="flex justify-end w-full">
                            <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm mr-2 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                <i class="fas fa-redo mr-1"></i> 重置
                            </button>
                            <button class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <i class="fas fa-search mr-1"></i> 查询
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 物资列表表格 -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物资名称</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规格型号</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物资类别</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属仓库</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">有效期</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">医用防护口罩</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">N95</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">防疫物资</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">中心仓库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">10000</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">个</td>
                                    <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">正常</span></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2025-12-31</td>
                                    <td class="px-6 py-4 text-sm text-gray-500 truncate max-w-xs" title="标准N95防护级别">标准N95防护级别</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800 btn-edit-material" data-id="1">修改</button>
                                        <button class="text-red-600 hover:text-red-800 btn-delete-material" data-id="1">删除</button>
                                        <!-- <button class="text-indigo-600 hover:text-indigo-800">详情</button> -->
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">救生衣</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">成人通用</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">防洪物资</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">东区仓库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">500</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">件</td>
                                    <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">正常</span></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2026-06-30</td>
                                    <td class="px-6 py-4 text-sm text-gray-500 truncate max-w-xs" title="符合国标GB25804">符合国标GB25804</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800 btn-edit-material" data-id="2">修改</button>
                                        <button class="text-red-600 hover:text-red-800 btn-delete-material" data-id="2">删除</button>
                                        <!-- <button class="text-indigo-600 hover:text-indigo-800">详情</button> -->
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">灭火器</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">干粉 4kg</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">消防物资</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">中心仓库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">200</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">具</td>
                                    <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">维修中</span></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-10-01</td>
                                    <td class="px-6 py-4 text-sm text-gray-500 truncate max-w-xs" title="压力表指针不在绿色区域">压力表指针不在绿色区域</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800 btn-edit-material" data-id="3">修改</button>
                                        <button class="text-red-600 hover:text-red-800 btn-delete-material" data-id="3">删除</button>
                                        <!-- <button class="text-indigo-600 hover:text-indigo-800">详情</button> -->
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">应急帐篷</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">10人</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">救灾物资</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">南区仓库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">50</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">顶</td>
                                    <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">报废</span></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">N/A</td> <!-- 报废的可能没有有效期 -->
                                    <td class="px-6 py-4 text-sm text-gray-500 truncate max-w-xs" title="支架损坏无法修复">支架损坏无法修复</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800 btn-edit-material" data-id="4">修改</button>
                                        <button class="text-red-600 hover:text-red-800 btn-delete-material" data-id="4">删除</button>
                                        <!-- <button class="text-indigo-600 hover:text-indigo-800">详情</button> -->
                                </td>
                            </tr>
                        </tbody>
                    </table>
            </div>
            
            <!-- 分页 -->
                    <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            显示 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium">45</span> 条记录
                    </div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">上一页</span>
                                <i class="fas fa-chevron-left h-5 w-5"></i>
                            </a>
                            <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                1
                            </a>
                            <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                2
                            </a>
                            <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 hidden md:inline-flex relative items-center px-4 py-2 border text-sm font-medium">
                                3
                            </a>
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                ...
                            </span>
                            <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 hidden md:inline-flex relative items-center px-4 py-2 border text-sm font-medium">
                                5
                            </a>
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">下一页</span>
                                <i class="fas fa-chevron-right h-5 w-5"></i>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
        </main>
            </div>
            
    <!-- 新增/编辑物资 Modal -->
    <div id="modal-app">
        <el-dialog
            v-model="dialogVisible"
            :title="isEditMode ? '编辑物资信息' : '新增物资'"
            width="60%"
            @closed="resetForm"
            :close-on-click-modal="false"
        >
            <el-form :model="materialForm" ref="materialFormRef" label-width="100px" label-position="right">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="物资名称" required prop="name">
                            <el-input v-model="materialForm.name" placeholder="请输入物资名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="规格型号" prop="model">
                            <el-input v-model="materialForm.model" placeholder="请输入规格型号"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="物资类别" required prop="type">
                            <el-select v-model="materialForm.type" placeholder="请选择物资类别">
                                <el-option label="防疫物资" value="1"></el-option>
                                <el-option label="防洪物资" value="2"></el-option>
                                <el-option label="消防物资" value="3"></el-option>
                                <el-option label="救灾物资" value="4"></el-option>
                                <el-option label="通信设备" value="5"></el-option>
                                <el-option label="应急装备" value="6"></el-option>
                                <!-- Add more options as needed -->
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="所属仓库" required prop="warehouseId">
                            <el-select v-model="materialForm.warehouseId" placeholder="请选择所属仓库">
                                <!-- Fetch or define warehouse options -->
                                <el-option label="中心仓库" value="1"></el-option>
                                <el-option label="东区仓库" value="2"></el-option>
                                <el-option label="南区仓库" value="3"></el-option>
                                <el-option label="西区仓库" value="4"></el-option>
                                <el-option label="北区仓库" value="5"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="数量" required prop="quantity">
                            <el-input-number v-model="materialForm.quantity" :min="0" placeholder="请输入数量"></el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="单位" required prop="unit">
                            <el-input v-model="materialForm.unit" placeholder="例如: 个, 件, 套"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="状态" required prop="status">
                            <el-select v-model="materialForm.status" placeholder="请选择状态">
                                <el-option label="正常" value="1"></el-option>
                                <el-option label="维修中" value="2"></el-option>
                                <el-option label="报废" value="3"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="有效期" prop="expiryDate">
                            <el-date-picker
                                v-model="materialForm.expiryDate"
                                type="date"
                                placeholder="选择有效期"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                                style="width: 100%;">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="备注" prop="remarks">
                            <el-input type="textarea" :rows="1" v-model="materialForm.remarks" placeholder="请输入备注信息"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="closeModal">取消</el-button>
                    <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                </span>
            </template>
        </el-dialog>
                    </div>
                    
    <!-- 引入 Vue 和 Element Plus -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <script src="https://unpkg.com/element-plus"></script>

    <script>
        // Modal App Logic
        const MaterialModalApp = {
            data() {
                return {
                    dialogVisible: false,
                    isEditMode: false,
                    materialForm: {
                        id: null,
                        name: '',
                        model: '',
                        type: '',
                        warehouseId: '',
                        quantity: 0,
                        unit: '',
                        status: '1', // Default to '正常'
                        remarks: '',
                        expiryDate: null // 新增有效期字段
                    },
                    // Add options for selects if needed dynamically
                };
            },
            methods: {
                openModal(isEdit = false, materialData = null) {
                    this.isEditMode = isEdit;
                    if (isEdit && materialData) {
                        // Populate form with existing data
                        // Make sure the keys match the form model
                        this.materialForm = { ...materialData };
                    } else {
                        // Reset form for new entry
                        this.resetForm();
                    }
                    this.dialogVisible = true;
                },
                closeModal() {
                    this.dialogVisible = false;
                },
                submitForm() {
                    this.$refs.materialFormRef.validate((valid) => {
                        if (valid) {
                            console.log('Form Submitted:', this.materialForm);
                            // TODO: Add actual save/update logic here (e.g., AJAX call)
                            alert(`数据已${this.isEditMode ? '更新' : '添加'} (模拟)`);
                            this.closeModal();
                            // Optionally refresh the table
                        } else {
                            console.log('Error submitting form');
                            ElementPlus.ElMessage.error('请检查表单填写是否正确');
                            return false;
                        }
                    });
                },
                resetForm() {
                    // Reset form fields to default/empty
                    this.materialForm = {
                        id: null, name: '', model: '', type: '', warehouseId: '',
                        quantity: 0, unit: '', status: '1', remarks: '', expiryDate: null // 重置有效期
                    };
                    if (this.$refs.materialFormRef) {
                        this.$refs.materialFormRef.clearValidate(); // Clear validation errors
                    }
                }
            }
        };

        const materialModalVm = Vue.createApp(MaterialModalApp);
        materialModalVm.use(ElementPlus);
        const modalComponentInstance = materialModalVm.mount('#modal-app'); // Capture the component instance

        // Updated Event Listeners
        document.addEventListener('click', function(event) {
            // Open Add Modal
            if (event.target.closest('#btnAddMaterial')) {
                modalComponentInstance.openModal(false);
            }

            // Open Edit Modal
            const editButton = event.target.closest('.btn-edit-material');
            if (editButton) {
                const materialId = editButton.dataset.id;
                console.log("Edit material ID:", materialId);
                // TODO: Fetch actual material data by ID from your backend/data source
                // Mock data for demonstration:
                const mockData = {
                    id: materialId,
                    name: `物资 ${materialId}`, // Example name
                    model: `型号 ${materialId}`, // Example model
                    type: (materialId % 6 + 1).toString(), // Example type based on ID
                    warehouseId: (materialId % 5 + 1).toString(), // Example warehouse
                    quantity: materialId * 100, // Example quantity
                    unit: '个', // Example unit
                    status: (materialId % 3 + 1).toString(), // Example status
                    remarks: `这是物资 ${materialId} 的备注信息。`,
                    expiryDate: `202${5 + (materialId % 3)}-${(materialId % 12 + 1).toString().padStart(2, '0')}-15` // Example expiry date
                };
                modalComponentInstance.openModal(true, mockData);
            }

            // Handle Delete
            const deleteButton = event.target.closest('.btn-delete-material');
            if (deleteButton) {
                const materialId = deleteButton.dataset.id;
                 if (confirm(`确定要删除物资 ID ${materialId} 吗？`)) {
                    console.log('Delete button clicked for ID:', materialId);
                     alert(`触发删除操作 (模拟) - ID: ${materialId}`);
                     // Add logic to delete the item (e.g., remove row, API call)
                }
            }

            // Keep other non-modal button logic if needed
            // ... (Example: Export button, etc.)

        });
    </script>

    <!-- Load component HTML first -->
    <script src="js/navbarComponent.js"></script>
    <script src="js/sidebarComponent.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>
</body>
</html> 