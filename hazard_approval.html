<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风险隐患审批 - 应急管理系统</title>
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css"> <!-- Added Element Plus CSS -->
    <link rel="stylesheet" href="css/common.css"> <!-- Ensure common CSS is linked -->
    <link rel="stylesheet" href="css/unified_header.css">
    <style>
        html, body {
            height: 100%;
            margin: 0;
        }
        body {
             font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
        }
        .tab-content { display: none; }
        .tab-content.active { display: block; }

        /* Tree Select Styles (copied from hazard_check_list) */
        .el-tree-select {
            width: 100% !important;
        }
        .el-select-dropdown__wrap {
            max-height: 400px;
        }
        .el-tree-node__content {
            height: 32px;
        }
        .el-tree-node__label {
            font-size: 14px;
        }
        /* Status Badge Styles (copied from hazard_check_list) */
        .status-badge {
          padding: 0.25rem 0.5rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 600;
          display: inline-block;
          text-align: center;
          min-width: 4rem; /* Consistent width */
        }
        .status-high { background-color: #FEE2E2; color: #991B1B; } /* Red - High */
        .status-medium { background-color: #FEF3C7; color: #92400E; } /* Yellow - Medium */
        .status-low { background-color: #DBEAFE; color: #1E40AF; } /* Blue - Low */
        .status-none { background-color: #F3F4F6; color: #4B5563; } /* Gray - None/Rectified */
    </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
    <!-- Navbar Start -->
    <header class="top-nav">
        <div class="system-title">
            <strong>广西公路水路安全畅通与应急处置系统</strong>
        </div>
        <nav class="tab-navigation">
            <a href="new_index.html" class="tab-button">风险一张图</a>
            <a href="my_check_tasks.html" class="tab-button active">风险隐患管理</a>
            <a href="plan_list.html" class="tab-button">应急处置</a>
            <button class="tab-button" onclick="alert('应急演练功能暂未开放');">应急演练</button>
        </nav>
    </header>
    <!-- Navbar End -->

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container h-full" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area -->
        <main id="main-content" class="flex-grow pb-8 px-6 bg-gray-100">
            <div class="py-6">
                <!-- Page Title -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800">风险隐患审批</h2>
                        <p class="text-gray-600 mt-1">管理和审批风险隐患检查记录</p>
                    </div>
                </div>

                <!-- Tabs Navigation -->
                <div class="bg-white rounded-t-lg shadow-sm mb-0">
                    <div class="p-4 sm:px-6">
                        <nav class="flex space-x-4 overflow-x-auto pb-1">
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600 focus:outline-none" data-tab="pending">
                                待审批 <span class="ml-1 px-2 py-0.5 bg-red-100 text-red-800 rounded-full text-xs">3</span> <!-- Example count -->
                            </button>
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="approved">
                                已通过
                            </button>
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="rejected">
                                已驳回
                            </button>
                        </nav>
                    </div>

                    <!-- Filter Area (Adapted from hazard_check_list) -->
                    <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                             <div>
                                <label for="filterCity" class="block text-sm font-medium text-gray-700 mb-1">市</label>
                                <input type="text" id="filterCity" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="输入市名称">
                            </div>
                            <div>
                                <label for="filterCounty" class="block text-sm font-medium text-gray-700 mb-1">区/县</label>
                                <input type="text" id="filterCounty" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="输入区/县名称">
                            </div>
                            <div>
                                <label for="filterOrgUnit" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                                <div id="filterOrgApp">
                                    <el-tree-select
                                        v-model="selectedUnits"
                                        :data="unitOptions"
                                        multiple
                                        show-checkbox
                                        :props="{ value: 'value', label: 'label', children: 'children' }"
                                        placeholder="请选择单位"
                                        class="block w-full"
                                        @change="handleFilterOrgChange"
                                    />
                                </div>
                            </div>
                            <div>
                                <label for="filterCategory" class="block text-sm font-medium text-gray-700 mb-1">检查类别</label>
                                <div id="filterCategoryApp">
                                    <el-tree-select
                                        v-model="selectedCategories"
                                        :data="categoryOptions"
                                        multiple
                                        show-checkbox
                                        check-strictly="false"
                                        :props="{ value: 'value', label: 'label', children: 'children' }"
                                        placeholder="请选择检查类别"
                                        class="block w-full"
                                        @change="handleFilterCategoryChange"
                                    />
                                </div>
                            </div>
                            <div>
                                <label for="filterRiskLevel" class="block text-sm font-medium text-gray-700 mb-1">风险等级</label>
                                <select id="filterRiskLevel" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    <option value="">全部</option>
                                    <option value="high">高</option>
                                    <option value="medium">中</option>
                                    <option value="low">低</option>
                                    <option value="none">无风险/已整改</option>
                                </select>
                            </div>
                             <div>
                                <label for="filterIsHazard" class="block text-sm font-medium text-gray-700 mb-1">是否为隐患点</label>
                                <select id="filterIsHazard" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    <option value="">全部</option>
                                    <option value="yes">是</option>
                                    <option value="no">否</option>
                                </select>
                            </div>
                              <div>
                                <label for="filterRoadNumber" class="block text-sm font-medium text-gray-700 mb-1">路段编号</label>
                                <input type="text" id="filterRoadNumber" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="输入路段编号">
                            </div>
                             <div>
                                <label for="date_range" class="block text-sm font-medium text-gray-700 mb-1">检查时间</label>
                                <div class="flex items-center space-x-2">
                                    <input type="date" id="filter_check_date_from" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    <span class="text-gray-500">至</span>
                                    <input type="date" id="filter_check_date_to" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                            </div>
                        </div>
                        <div class="mt-4 flex justify-end">
                             <button id="btnResetFilter" class="bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mr-3">
                                <i class="fas fa-undo mr-1"></i> 重置
                            </button>
                            <button id="btnFilter" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <i class="fas fa-search mr-1"></i> 搜索
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Hazard Approval List Section -->
                <div class="bg-white rounded-b-lg shadow-md overflow-hidden">
                    <!-- Pending List -->
                    <div id="pending" class="tab-content active">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">市</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">区/县</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查类别</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路段编号</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险等级</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">是否隐患点</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险点描述</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提交人</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提交时间</th>
                                        <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- Example Pending Row 1 -->
                                    <tr>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">南宁市</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">青秀区</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">南宁市交通运输局</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">风险路段-地质灾害风险</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">G324</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm"><span class="status-badge status-high">高</span></td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-red-600 font-semibold">是</td>
                                        <td class="px-3 py-4 text-sm text-gray-900 truncate max-w-xs">K1500+200边坡有落石风险</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">张检查员</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-25 10:30</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                            <a href="#" class="text-blue-600 hover:text-blue-900 mr-3 btn-view-hazard" data-id="h1">查看</a> <!-- Link to detail or modal -->
                                            <button class="text-green-600 hover:text-green-900 mr-3 btn-approve" data-id="h1">通过</button>
                                            <button class="text-red-600 hover:text-red-900 mr-3 btn-reject" data-id="h1">驳回</button>
                                        </td>
                                    </tr>
                                    <!-- Example Pending Row 2 -->
                                    <tr>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">钦州市</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">灵山县</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">钦州市交通运输局</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">基础保障设施隐患-防洪标识</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">S211</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm"><span class="status-badge status-low">低</span></td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">否</td>
                                        <td class="px-3 py-4 text-sm text-gray-900 truncate max-w-xs">桥梁限高标识不清</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">李检查员</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-24 15:00</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                            <a href="#" class="text-blue-600 hover:text-blue-900 mr-3 btn-view-hazard" data-id="h2">查看</a>
                                            <button class="text-green-600 hover:text-green-900 mr-3 btn-approve" data-id="h2">通过</button>
                                            <button class="text-red-600 hover:text-red-900 mr-3 btn-reject" data-id="h2">驳回</button>
                                        </td>
                                    </tr>
                                     <!-- Example Pending Row 3 -->
                                    <tr>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">玉林市</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">福绵区</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">玉林市交通运输局</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">涉灾隐患点-桥梁</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">X456</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm"><span class="status-badge status-medium">中</span></td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-red-600 font-semibold">是</td>
                                        <td class="px-3 py-4 text-sm text-gray-900 truncate max-w-xs">XX桥梁伸缩缝堵塞</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">王检查员</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-23 09:00</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                            <a href="#" class="text-blue-600 hover:text-blue-900 mr-3 btn-view-hazard" data-id="h3">查看</a>
                                            <button class="text-green-600 hover:text-green-900 mr-3 btn-approve" data-id="h3">通过</button>
                                            <button class="text-red-600 hover:text-red-900 mr-3 btn-reject" data-id="h3">驳回</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-700">
                                        显示第 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">3</span> 条 <!-- Example count -->
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">上一页</span>
                                            <i class="fas fa-chevron-left text-xs"></i>
                                        </a>
                                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-50">1</a>
                                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">下一页</span>
                                            <i class="fas fa-chevron-right text-xs"></i>
                                        </a>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Approved List -->
                    <div id="approved" class="tab-content">
                        <div class="p-6 text-center text-gray-500">
                            已通过的风险隐患记录列表（结构类似，可能包含审批时间、审批人等信息）
                        </div>
                    </div>

                    <!-- Rejected List -->
                    <div id="rejected" class="tab-content">
                        <div class="p-6 text-center text-gray-500">
                            已驳回的风险隐患记录列表（结构类似，应包含驳回原因和操作）
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div> <!-- Close flex-container -->

    <!-- View Hazard Details Modal -->
    <div id="viewHazardModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">查看风险隐患详情</h3>
                <button class="text-gray-400 hover:text-gray-500 focus:outline-none btn-close-modal" data-modal-id="viewHazardModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="px-6 py-4 max-h-[70vh] overflow-y-auto">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 pb-4 border-b">
                    <div><strong class="text-gray-600">市:</strong> <span id="view-city"></span></div>
                    <div><strong class="text-gray-600">区/县:</strong> <span id="view-county"></span></div>
                    <div><strong class="text-gray-600">所属单位:</strong> <span id="view-orgUnit"></span></div>
                    <div><strong class="text-gray-600">检查类别:</strong> <span id="view-category"></span></div>
                    <div><strong class="text-gray-600">路段编号:</strong> <span id="view-roadNumber"></span></div>
                    <div><strong class="text-gray-600">起点桩号:</strong> <span id="view-startStake"></span></div>
                    <div><strong class="text-gray-600">止点桩号:</strong> <span id="view-endStake"></span></div>
                    <div><strong class="text-gray-600">风险等级:</strong> <span id="view-riskLevel"></span></div>
                    <div><strong class="text-gray-600">是否隐患点:</strong> <span id="view-isHazard" class="font-semibold"></span></div>
                    <div><strong class="text-gray-600">提交人:</strong> <span id="view-submitter"></span></div>
                    <div><strong class="text-gray-600">提交时间:</strong> <span id="view-submitTime"></span></div>
                    <div><strong class="text-gray-600">是否已采取措施:</strong> <span id="view-measuresTaken"></span></div>
                </div>
                 <div class="mb-4">
                    <strong class="text-gray-600 block mb-1">风险点描述:</strong>
                    <p id="view-riskDescription" class="text-gray-800 bg-gray-50 p-2 rounded min-h-[50px]"></p>
                 </div>
                  <div class="mb-4">
                    <strong class="text-gray-600 block mb-1">已（拟）采取的措施:</strong>
                    <p id="view-measures" class="text-gray-800 bg-gray-50 p-2 rounded min-h-[50px]"></p>
                 </div>
                 <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                     <div>
                         <strong class="text-gray-600 block mb-1">现场照片/附件:</strong>
                         <div id="view-photos" class="text-blue-600 min-h-[20px]"></div>
                     </div>
                      <div>
                         <strong class="text-gray-600 block mb-1">措施附件:</strong>
                         <div id="view-measureFiles" class="text-blue-600 min-h-[20px]"></div>
                     </div>
                 </div>
                 <div class="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
                     <div><strong class="text-gray-600">省级责任单位:</strong> <span id="view-provincialResp"></span></div>
                     <div><strong class="text-gray-600">复核责任单位:</strong> <span id="view-reviewResp"></span></div>
                     <div><strong class="text-gray-600">排查责任单位:</strong> <span id="view-inspectResp"></span></div>
                 </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 btn-close-modal" data-modal-id="viewHazardModal">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <script>
        // Unit Data (copied from hazard_check_list)
        const unitData = [{
            value: '1', label: '广西壮族自治区交通运输厅', children: [
                { value: '1.1', label: '直属事业单位及专项机构', children: [ /*...*/ ] },
                { value: '1.2', label: '市级交通运输局', children: [
                        { value: '1.2.1', label: '钦州市交通运输局' },
                        { value: '1.2.2', label: '南宁市交通运输局' },
                        { value: '1.2.3', label: '玉林市交通运输局' }
                ]}
            ]
        }];

        // Category Data (copied from hazard_check_list)
        const categoryData = [
            { value: 'risk_section', label: '风险路段', children: [ /*...*/ ] },
            { value: 'management_mechanism', label: '工作管理机制隐患' },
            { value: 'basic_facilities', label: '基础保障设施隐患', children: [ /*...*/ ] },
            { value: 'hazard_points', label: '涉灾隐患点', children: [ /*...*/ ] }
        ];

        // Vue App for Org Filter
        const filterOrgApp = Vue.createApp({
            data() { return { selectedUnits: [], unitOptions: unitData } },
            methods: { handleFilterOrgChange(value) { console.log('筛选选中的单位:', value); } }
        }).use(ElementPlus);
        filterOrgApp.mount('#filterOrgApp');

        // Vue App for Category Filter
        const filterCategoryApp = Vue.createApp({
            data() { return { selectedCategories: [], categoryOptions: categoryData } },
            methods: { handleFilterCategoryChange(value) { console.log('筛选选中的类别:', value); } }
        }).use(ElementPlus);
        filterCategoryApp.mount('#filterCategoryApp');

        // --- Modal Control Functions ---
        const openModal = (modalId) => {
            const modal = document.getElementById(modalId);
            if(modal) modal.classList.remove('hidden');
        };
        const closeModal = (modalId) => {
             const modal = document.getElementById(modalId);
             if(modal) modal.classList.add('hidden');
        };

        // Tab Switching Logic (from plan_approval)
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            // Default active tab
            if (tabButtons.length > 0 && tabContents.length > 0) {
                tabButtons[0].classList.add('text-blue-600', 'border-blue-600');
                tabButtons[0].classList.remove('text-gray-500', 'border-transparent');
                tabContents[0].classList.add('active');
            }

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Deactivate all tabs
                    tabButtons.forEach(btn => {
                        btn.classList.remove('text-blue-600', 'border-blue-600');
                        btn.classList.add('text-gray-500', 'border-transparent', 'hover:text-gray-700', 'hover:border-gray-300');
                    });
                    // Activate clicked tab
                    button.classList.remove('text-gray-500', 'border-transparent', 'hover:text-gray-700', 'hover:border-gray-300');
                    button.classList.add('text-blue-600', 'border-blue-600');
                    // Hide all content
                    tabContents.forEach(content => {
                        content.classList.remove('active');
                    });
                    // Show target content
                    const tabId = button.getAttribute('data-tab');
                    const targetContent = document.getElementById(tabId);
                    if (targetContent) {
                      targetContent.classList.add('active');
                    }
                });
            });

             // Basic action button listeners (add actual logic)
             document.querySelector('tbody')?.addEventListener('click', (event) => {
                const target = event.target.closest('button, a'); // Include links for '查看'
                if (!target) return;
                const id = target.getAttribute('data-id');

                if (target.classList.contains('btn-view-hazard')) {
                    event.preventDefault(); // Prevent link navigation for now
                    console.log('查看 ID:', id);
                    populateAndShowViewModal(id); // Call the new function
                } else if (target.classList.contains('btn-approve')) {
                    console.log('通过 ID:', id);
                    if(confirm(`确定要通过风险隐患记录 ${id} 吗？`)){
                         alert(`风险隐患 ${id} 已通过 (模拟)`);
                         // TODO: Send approval request
                    }
                } else if (target.classList.contains('btn-reject')) {
                    console.log('驳回 ID:', id);
                    const reason = prompt(`请输入驳回风险隐患记录 ${id} 的原因:`);
                    if(reason !== null){ // prompt returns null if cancelled
                         alert(`风险隐患 ${id} 已驳回，原因: ${reason} (模拟)`);
                         // TODO: Send rejection request with reason
                    }
                }
            });

             // Filter/Reset button listeners
             document.getElementById('btnFilter')?.addEventListener('click', () => {
                console.log('应用筛选 (待实现)');
                // TODO: Gather filter values and send request
             });
             document.getElementById('btnResetFilter')?.addEventListener('click', () => {
                console.log('重置筛选 (待实现)');
                // TODO: Reset filter inputs and reload list
             });

             // Add event listeners for modal close buttons
             document.querySelectorAll('.btn-close-modal').forEach(button => {
                 button.addEventListener('click', () => {
                     const modalId = button.getAttribute('data-modal-id');
                     if(modalId) closeModal(modalId);
                 });
             });

        });

        // --- Populate View Modal Function ---
        function populateAndShowViewModal(hazardId) {
            // ** SIMULATE getting data based on hazardId **
            // In a real app, you would fetch this data from a server/API
            const mockData = {
                h1: {
                    city: '南宁市', county: '青秀区', orgUnit: '南宁市交通运输局',
                    category: '风险路段-地质灾害风险', roadNumber: 'G324',
                    riskLevel: 'high', isHazard: '是', submitter: '张检查员',
                    submitTime: '2024-07-25 10:30', riskDescription: 'K1500+200边坡有落石风险, 需要紧急处理，潜在影响较大。',
                    startStake: 'K1500+200', endStake: 'K1500+500',
                    measuresTaken: '否', measures: '尚未采取措施',
                    photos: ['photo1.jpg', 'photo2.jpg'], measureFiles: [],
                    provincialResp: '省交通厅 - 李四', reviewResp: '市交通局 - 王五', inspectResp: '南宁市交通运输局 - 张三'
                },
                h2: {
                    city: '钦州市', county: '灵山县', orgUnit: '钦州市交通运输局',
                    category: '基础保障设施隐患-防洪标识', roadNumber: 'S211',
                    riskLevel: 'low', isHazard: '否', submitter: '李检查员',
                    submitTime: '2024-07-24 15:00', riskDescription: '桥梁入口处限高标识陈旧，字迹模糊不易辨认。',
                    startStake: 'K10+000', endStake: '',
                    measuresTaken: '是', measures: '已安排重新制作标识牌，计划下周安装。',
                    photos: [], measureFiles: ['measure_report.pdf'],
                    provincialResp: '省交通厅 - 李四', reviewResp: '市交通局 - 赵六', inspectResp: '钦州市交通运输局 - 钱七'
                },
                h3: {
                    city: '玉林市', county: '福绵区', orgUnit: '玉林市交通运输局',
                    category: '涉灾隐患点-桥梁', roadNumber: 'X456',
                    riskLevel: 'medium', isHazard: '是', submitter: '王检查员',
                    submitTime: '2024-07-23 09:00', riskDescription: 'XX桥第3跨伸缩缝被杂物严重堵塞，影响排水和结构。',
                    startStake: 'K5+100', endStake: 'K5+150',
                    measuresTaken: '否', measures: '已上报，等待清理方案批复',
                    photos: ['bridge_photo1.jpg'], measureFiles: [],
                    provincialResp: '省交通厅 - 李四', reviewResp: '市交通局 - 周八', inspectResp: '玉林市交通运输局 - 吴九'
                }
                // Add more mock data as needed
            };

            const data = mockData[hazardId]; // Get the data for the specific ID

            if (!data) {
                console.error('Could not find data for hazard ID:', hazardId);
                alert('无法加载详情信息！');
                return;
            }

            // Populate the modal fields
            document.getElementById('view-city').textContent = data.city || 'N/A';
            document.getElementById('view-county').textContent = data.county || 'N/A';
            document.getElementById('view-orgUnit').textContent = data.orgUnit || 'N/A';
            document.getElementById('view-category').textContent = data.category || 'N/A';
            document.getElementById('view-roadNumber').textContent = data.roadNumber || 'N/A';
            document.getElementById('view-startStake').textContent = data.startStake || 'N/A';
            document.getElementById('view-endStake').textContent = data.endStake || 'N/A';
            document.getElementById('view-submitter').textContent = data.submitter || 'N/A';
            document.getElementById('view-submitTime').textContent = data.submitTime || 'N/A';
            document.getElementById('view-riskDescription').textContent = data.riskDescription || 'N/A';
            document.getElementById('view-measuresTaken').textContent = data.measuresTaken || 'N/A';
            document.getElementById('view-measures').textContent = data.measures || 'N/A';
            document.getElementById('view-provincialResp').textContent = data.provincialResp || 'N/A';
            document.getElementById('view-reviewResp').textContent = data.reviewResp || 'N/A';
            document.getElementById('view-inspectResp').textContent = data.inspectResp || 'N/A';

            // Handle Risk Level Badge
            const riskLevelSpan = document.getElementById('view-riskLevel');
            riskLevelSpan.innerHTML = ''; // Clear previous badge
            const badge = document.createElement('span');
            badge.classList.add('status-badge');
            if (data.riskLevel === 'high') {
                badge.classList.add('status-high');
                badge.textContent = '高';
            } else if (data.riskLevel === 'medium') {
                badge.classList.add('status-medium');
                badge.textContent = '中';
            } else if (data.riskLevel === 'low') {
                badge.classList.add('status-low');
                badge.textContent = '低';
            } else {
                badge.classList.add('status-none');
                badge.textContent = '无风险'; // Or 'N/A'
            }
            riskLevelSpan.appendChild(badge);

             // Handle Is Hazard Point
             const isHazardSpan = document.getElementById('view-isHazard');
             isHazardSpan.textContent = data.isHazard === '是' ? '是' : '否';
             isHazardSpan.className = 'font-semibold'; // Reset classes
             if(data.isHazard === '是') {
                 isHazardSpan.classList.add('text-red-600');
             } else {
                 isHazardSpan.classList.add('text-gray-500');
             }

            // Example for handling photos (if you add the section)
            const photosDiv = document.getElementById('view-photos');
            photosDiv.innerHTML = ''; // Clear previous photos
            if (data.photos && data.photos.length > 0) {
                data.photos.forEach(photoName => {
                    const link = document.createElement('a');
                    link.href = '#'; // Replace with actual image path or viewer link
                    link.textContent = photoName;
                    link.classList.add('text-blue-600', 'hover:underline', 'mr-2');
                    photosDiv.appendChild(link);
                });
            } else {
                photosDiv.textContent = '无';
            }

            // Example for handling measure files
            const measureFilesDiv = document.getElementById('view-measureFiles');
            measureFilesDiv.innerHTML = ''; // Clear previous files
            if (data.measureFiles && data.measureFiles.length > 0) {
                data.measureFiles.forEach(fileName => {
                    const link = document.createElement('a');
                    link.href = '#'; // Replace with actual file path
                    link.textContent = fileName;
                    link.classList.add('text-blue-600', 'hover:underline', 'mr-2');
                    measureFilesDiv.appendChild(link);
                });
            } else {
                measureFilesDiv.textContent = '无';
            }

            // Open the modal
            openModal('viewHazardModal');
        }

    </script>
    <!-- Load component HTML first -->
    <script src="js/navbarComponent_risk.js"></script>
    <script src="js/sidebarComponent_risk.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>
</body>
</html>