<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>检查任务列表 (手机端模拟)</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f8f8f8;
        }
        .task-item {
            background-color: #fff;
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.08);
            display: block; /* Make the whole item clickable */
            text-decoration: none;
            color: inherit;
            transition: background-color 0.2s ease;
        }
        .task-item:hover {
            background-color: #f0f9ff; /* Lighter blue on hover */
        }
        .task-title {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        .task-meta {
            font-size: 13px;
            color: #666;
        }
        .task-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .status-pending {
            background-color: #fef3c7; /* yellow-100 */
            color: #92400e; /* yellow-800 */
        }
        .status-completed {
            background-color: #d1fae5; /* green-100 */
            color: #065f46; /* green-800 */
        }
         .status-overdue {
            background-color: #fee2e2; /* red-100 */
            color: #991b1b; /* red-800 */
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="bg-white shadow-sm sticky top-0 z-10">
        <h1 class="text-lg font-semibold text-center py-3 text-gray-800">我的检查任务</h1>
    </div>

    <!-- Task List -->
    <div class="p-3">
        <!-- Example Task 1 (Pending) -->
        <a href="mobile_hazard_report_form.html?taskId=1" class="task-item">
            <div class="flex justify-between items-center mb-1">
                <h2 class="task-title truncate">G324国道 K1500-K1510 段例行检查</h2>
                <span class="task-status status-pending">待检查</span>
            </div>
            <p class="task-meta">所属单位: 南宁市交通运输局</p>
            <p class="task-meta">计划完成时间: 2024-08-15</p>
        </a>

        <!-- Example Task 2 (Completed) -->
        <a href="mobile_hazard_report_form.html?taskId=2" class="task-item">
             <div class="flex justify-between items-center mb-1">
                <h2 class="task-title truncate">S211省道 桥梁专项检查</h2>
                 <span class="task-status status-completed">已完成</span>
            </div>
            <p class="task-meta">所属单位: 钦州市交通运输局</p>
             <p class="task-meta">完成时间: 2024-07-28</p>
        </a>
        
        <!-- Example Task 3 (Overdue) -->
        <a href="mobile_hazard_report_form.html?taskId=3" class="task-item">
             <div class="flex justify-between items-center mb-1">
                <h2 class="task-title truncate">X456县道 边坡隐患复查</h2>
                 <span class="task-status status-overdue">已逾期</span>
            </div>
            <p class="task-meta">所属单位: 玉林市交通运输局</p>
             <p class="task-meta">计划完成时间: 2024-07-30</p>
        </a>

         <!-- Add more task items as needed -->

    </div>

</body>
</html> 