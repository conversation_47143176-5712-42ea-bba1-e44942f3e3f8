/* 应急演练页面样式 */
.emergency-drill-container {
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 0;
    background-color: #f8f9fc;
    height: 100%;
    overflow-y: auto;
    width: 100%;
}

/* 修改tab-content样式，确保铺满页面 */
#emergency-drill-content.tab-content {
    padding: 0;
    margin: 0;
    width: 100%;
    height: 100%;
    display: none;
}

#emergency-drill-content.tab-content.active {
    display: flex;
}

/* 数据分析和演练评估部分 */
.drill-statistics-section {
    background-color: white;
    border-radius: 0;
    box-shadow: none;
    padding: 20px;
    margin: 0;
}

.drill-statistics-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.drill-statistics-grid {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.drill-stat-item {
    flex: 1;
    display: flex;
    align-items: center;
    background-color: #f8f9fc;
    border-radius: 0;
    padding: 12px;
    box-shadow: none;
    border: 1px solid #eee;
}

.drill-stat-icon {
    font-size: 24px;
    color: #4e73df;
    margin-right: 15px;
}

.drill-stat-content {
    display: flex;
    flex-direction: column;
}

.drill-stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.drill-stat-label {
    font-size: 14px;
    color: #666;
}

.drill-evaluation-summary {
    margin-top: 10px;
    border-top: 1px solid #eee;
    padding-top: 10px;
}

.evaluation-chart-container {
    display: flex;
    gap: 20px;
}

.evaluation-chart-placeholder {
    flex: 1;
    background-color: #f8f9fc;
    border-radius: 0;
    padding: 15px;
    min-height: 180px;
}

.chart-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
}

.chart-description {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.chart-score {
    display: flex;
    align-items: baseline;
    margin-bottom: 20px;
}

.score-value {
    font-size: 48px;
    font-weight: bold;
    color: #4e73df;
}

.score-label {
    font-size: 18px;
    color: #666;
    margin-left: 5px;
}

.score-details {
    width: 100%;
}

.score-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.score-item-label {
    color: #666;
}

.score-item-value {
    font-weight: bold;
    color: #333;
}

.chart-legend {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
}

.legend-item {
    display: flex;
    align-items: center;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    margin-right: 10px;
}

.legend-text {
    font-size: 14px;
    color: #666;
}

/* 演练计划列表和桌面推演列表部分 */
.drill-plan-section, .desktop-drill-section {
    background-color: white;
    border-radius: 0;
    box-shadow: none;
    padding: 20px;
    margin: 0;
    border-top: 1px solid #eee;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.section-actions {
    display: flex;
    gap: 15px;
}

.action-button {
    background-color: #4e73df;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.action-button i {
    margin-right: 5px;
}

.add-button {
    background-color: #1cc88a;
}

.start-button {
    background-color: #4e73df;
    padding: 5px 10px;
    font-size: 12px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.record-button {
    background-color: #f6c23e;
    padding: 5px 10px;
    font-size: 12px;
}

.search-box {
    display: flex;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.search-box input {
    border: none;
    padding: 8px 12px;
    width: 200px;
    outline: none;
}

.search-box button {
    background-color: #f8f9fc;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    border-left: 1px solid #ddd;
}

.drill-table-container {
    overflow-x: auto;
}

.drill-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.drill-table th {
    background-color: #f8f9fc;
    padding: 12px 15px;
    text-align: left;
    font-weight: bold;
    color: #333;
    border-bottom: 2px solid #e3e6f0;
}

.drill-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e3e6f0;
    color: #666;
}

.drill-table tbody tr:hover {
    background-color: #f8f9fc;
}

.status-tag {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.status-tag.completed {
    background-color: #e8f5e9;
    color: #1cc88a;
}

.status-tag.pending {
    background-color: #fff8e1;
    color: #f6c23e;
}

.file-link {
    color: #4e73df;
    text-decoration: none;
}

.file-link:hover {
    text-decoration: underline;
}

.view-button {
    background-color: #4e73df;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
}

.pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap: 5px;
}

.pagination-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ddd;
    background-color: white;
    border-radius: 4px;
    cursor: pointer;
}

.pagination-button.active {
    background-color: #4e73df;
    color: white;
    border-color: #4e73df;
}
