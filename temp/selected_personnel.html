<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>关联人员选择器</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
        }
        .toggle-btn {
            cursor: pointer;
            transition: transform 0.2s;
            display: inline-block;
            width: 16px;
            text-align: center;
        }
        .toggle-btn.collapsed .fa-caret-down {
            transform: rotate(-90deg);
        }
        .org-tree ul {
            list-style: none;
            padding-left: 0;
        }
        .org-tree li {
            position: relative;
        }
        .selected-item {
            background-color: #dbeafe;
            border-color: #93c5fd;
            color: #1e40af;
            padding: 2px 8px;
            border-radius: 9999px;
            font-size: 0.75rem;
            display: inline-flex;
            align-items: center;
            margin: 2px;
        }
        .selected-item button {
            margin-left: 4px;
            color: #3b82f6;
        }
        .selected-item button:hover {
            color: #1e40af;
        }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-6xl mx-auto bg-white p-6 rounded-lg shadow">
        <h2 class="text-xl font-bold mb-4">关联人员/单位选择</h2>
        
        <div class="bg-gray-50 p-3 rounded-lg border">
            <!-- 左右两栏布局 -->
            <div class="flex flex-col md:flex-row">
                <!-- 左侧单位树形图 -->
                <div class="w-full md:w-1/2 md:pr-3">
                    <div class="mb-2 flex justify-between items-center">
                        <h4 class="text-sm font-medium text-gray-700">组织机构</h4>
                        <button type="button" class="text-xs bg-blue-50 text-blue-600 py-1 px-2 rounded hover:bg-blue-100" id="select-all-orgs">
                            全选
                        </button>
                    </div>
                    <div class="h-64 overflow-y-auto bg-white p-2 rounded border">
                        <!-- 树形结构 -->
                        <ul class="org-tree">
                            <li class="mb-2">
                                <div class="flex items-center">
                                    <span class="toggle-btn mr-1" data-target="tree-1"><i class="fas fa-caret-down"></i></span>
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1">
                                        <span class="ml-2 text-sm">广西壮族自治区交通运输厅</span>
                                    </label>
                                </div>
                                <ul class="pl-6 mt-1" id="tree-1">
                                    <li class="mb-2">
                                        <div class="flex items-center">
                                            <span class="toggle-btn mr-1" data-target="tree-1-1"><i class="fas fa-caret-down"></i></span>
                                            <label class="inline-flex items-center">
                                                <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1-1">
                                                <span class="ml-2 text-sm">直属事业单位及专项机构</span>
                                            </label>
                                        </div>
                                        <ul class="pl-6 mt-1" id="tree-1-1">
                                            <li class="mb-1">
                                                <label class="inline-flex items-center">
                                                    <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1-1-1">
                                                    <span class="ml-2 text-sm">自治区公路发展中心</span>
                                                </label>
                                            </li>
                                            <li class="mb-1">
                                                <label class="inline-flex items-center">
                                                    <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1-1-2">
                                                    <span class="ml-2 text-sm">自治区高速公路发展中心</span>
                                                </label>
                                            </li>
                                            <li class="mb-1">
                                                <label class="inline-flex items-center">
                                                    <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1-1-3">
                                                    <span class="ml-2 text-sm">自治区道路运输发展中心</span>
                                                </label>
                                            </li>
                                        </ul>
                                    </li>
                                    <li class="mb-2">
                                        <div class="flex items-center">
                                            <span class="toggle-btn mr-1" data-target="tree-1-2"><i class="fas fa-caret-down"></i></span>
                                            <label class="inline-flex items-center">
                                                <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1-2">
                                                <span class="ml-2 text-sm">市级交通运输局</span>
                                            </label>
                                        </div>
                                        <ul class="pl-6 mt-1" id="tree-1-2">
                                            <li class="mb-1">
                                                <label class="inline-flex items-center">
                                                    <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1-2-1">
                                                    <span class="ml-2 text-sm">钦州市交通运输局</span>
                                                </label>
                                            </li>
                                            <li class="mb-1">
                                                <label class="inline-flex items-center">
                                                    <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1-2-2">
                                                    <span class="ml-2 text-sm">南宁市交通运输局</span>
                                                </label>
                                            </li>
                                            <li class="mb-1">
                                                <label class="inline-flex items-center">
                                                    <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1-2-3">
                                                    <span class="ml-2 text-sm">玉林市交通运输局</span>
                                                </label>
                                            </li>
                                        </ul>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- 右侧人员列表 -->
                <div class="w-full md:w-1/2 md:pl-3 mt-3 md:mt-0">
                    <div class="mb-2 flex justify-between items-center">
                        <h4 class="text-sm font-medium text-gray-700">人员列表</h4>
                        <div>
                            <input type="text" id="person-search" placeholder="搜索人员" class="text-xs border border-gray-300 rounded py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <button type="button" class="text-xs bg-blue-50 text-blue-600 py-1 px-2 rounded hover:bg-blue-100 ml-1" id="select-all-persons">
                                全选
                            </button>
                        </div>
                    </div>
                    <div class="h-64 overflow-y-auto bg-white p-2 rounded border">
                        <div class="grid grid-cols-1 gap-1 person-list">
                            <!-- 默认显示的是所有人员，后续会根据左侧选择的单位动态过滤 -->
                            <label class="inline-flex items-center p-2 hover:bg-gray-50 rounded">
                                <input type="checkbox" class="person-checkbox text-blue-600 focus:ring-blue-500" data-person-id="person-1" data-org-id="org-1-1-1">
                                <span class="ml-2 text-sm">李明（自治区公路发展中心）</span>
                            </label>
                            <label class="inline-flex items-center p-2 hover:bg-gray-50 rounded">
                                <input type="checkbox" class="person-checkbox text-blue-600 focus:ring-blue-500" data-person-id="person-2" data-org-id="org-1-1-1">
                                <span class="ml-2 text-sm">王芳（自治区公路发展中心）</span>
                            </label>
                            <label class="inline-flex items-center p-2 hover:bg-gray-50 rounded">
                                <input type="checkbox" class="person-checkbox text-blue-600 focus:ring-blue-500" data-person-id="person-3" data-org-id="org-1-1-2">
                                <span class="ml-2 text-sm">张伟（自治区高速公路发展中心）</span>
                            </label>
                            <label class="inline-flex items-center p-2 hover:bg-gray-50 rounded">
                                <input type="checkbox" class="person-checkbox text-blue-600 focus:ring-blue-500" data-person-id="person-4" data-org-id="org-1-1-3">
                                <span class="ml-2 text-sm">赵丽（自治区道路运输发展中心）</span>
                            </label>
                            <label class="inline-flex items-center p-2 hover:bg-gray-50 rounded">
                                <input type="checkbox" class="person-checkbox text-blue-600 focus:ring-blue-500" data-person-id="person-5" data-org-id="org-1-2-1">
                                <span class="ml-2 text-sm">陈刚（钦州市交通运输局）</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 已选择项展示区域 -->
            <div class="mt-3">
                <h4 class="text-sm font-medium text-gray-700 mb-2">已选择 <span class="text-xs text-gray-500">(点击标签可移除)</span></h4>
                <div class="min-h-8 p-2 bg-white rounded border flex flex-wrap gap-2 selected-items">
                    <!-- 这里会动态显示已选择的单位和人员 -->
                    <span class="selected-none text-sm text-gray-500">暂无选择</span>
                </div>
            </div>
        </div>

        <div class="mt-4 flex justify-end">
            <button class="bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2">
                取消
            </button>
            <button class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                确认
            </button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 树形结构折叠/展开
            const toggleButtons = document.querySelectorAll('.toggle-btn');
            toggleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-target');
                    const targetEl = document.getElementById(targetId);
                    
                    // 切换显示/隐藏
                    if (targetEl.style.display === 'none') {
                        targetEl.style.display = '';
                        this.classList.remove('collapsed');
                    } else {
                        targetEl.style.display = 'none';
                        this.classList.add('collapsed');
                    }
                });
            });

            // 组织机构选择逻辑
            const orgCheckboxes = document.querySelectorAll('.org-checkbox');
            orgCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const orgId = this.getAttribute('data-org-id');
                    const isChecked = this.checked;
                    
                    // 处理子项
                    const parentLi = this.closest('li');
                    const childCheckboxes = parentLi.querySelectorAll('.org-checkbox');
                    childCheckboxes.forEach(childCB => {
                        if (childCB !== this) {
                            childCB.checked = isChecked;
                            updateSelectedItem(childCB.getAttribute('data-org-id'), 
                                              childCB.nextElementSibling.textContent.trim(), 
                                              isChecked, 'org');
                        }
                    });
                    
                    // 更新选中项
                    updateSelectedItem(orgId, this.nextElementSibling.textContent.trim(), isChecked, 'org');
                    
                    // 更新人员列表的显示
                    filterPersonsByOrg();
                });
            });

            // 人员选择逻辑
            const personCheckboxes = document.querySelectorAll('.person-checkbox');
            personCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const personId = this.getAttribute('data-person-id');
                    const isChecked = this.checked;
                    
                    // 更新选中项
                    updateSelectedItem(personId, this.nextElementSibling.textContent.trim(), isChecked, 'person');
                });
            });

            // 搜索框逻辑
            const personSearch = document.getElementById('person-search');
            personSearch.addEventListener('input', function() {
                const searchText = this.value.toLowerCase();
                const personItems = document.querySelectorAll('.person-list label');
                
                personItems.forEach(item => {
                    const personName = item.textContent.toLowerCase();
                    if (personName.includes(searchText)) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });

            // 全选组织机构
            const selectAllOrgsBtn = document.getElementById('select-all-orgs');
            selectAllOrgsBtn.addEventListener('click', function() {
                const allChecked = Array.from(orgCheckboxes).every(cb => cb.checked);
                
                orgCheckboxes.forEach(cb => {
                    cb.checked = !allChecked;
                    updateSelectedItem(cb.getAttribute('data-org-id'), 
                                      cb.nextElementSibling.textContent.trim(), 
                                      !allChecked, 'org');
                });
                
                filterPersonsByOrg();
            });

            // 全选人员
            const selectAllPersonsBtn = document.getElementById('select-all-persons');
            selectAllPersonsBtn.addEventListener('click', function() {
                const visiblePersons = Array.from(document.querySelectorAll('.person-list label:not([style*="display: none"]) .person-checkbox'));
                const allChecked = visiblePersons.length > 0 && visiblePersons.every(cb => cb.checked);
                
                visiblePersons.forEach(cb => {
                    cb.checked = !allChecked;
                    updateSelectedItem(cb.getAttribute('data-person-id'), 
                                      cb.nextElementSibling.textContent.trim(), 
                                      !allChecked, 'person');
                });
            });

            // 更新已选择项目
            function updateSelectedItem(id, name, isChecked, type) {
                const selectedItemsContainer = document.querySelector('.selected-items');
                const selectedNone = document.querySelector('.selected-none');
                const existingItem = selectedItemsContainer.querySelector(`.selected-item[data-id="${id}"]`);
                
                if (isChecked && !existingItem) {
                    // 添加新项目
                    if (selectedNone) {
                        selectedNone.style.display = 'none';
                    }
                    
                    const item = document.createElement('span');
                    item.className = 'selected-item';
                    item.setAttribute('data-id', id);
                    item.setAttribute('data-type', type);
                    
                    item.innerHTML = `
                        ${name} <button type="button"><i class="fas fa-times"></i></button>
                    `;
                    
                    // 添加删除按钮事件
                    item.querySelector('button').addEventListener('click', function() {
                        if (type === 'org') {
                            const orgCB = document.querySelector(`.org-checkbox[data-org-id="${id}"]`);
                            if (orgCB) orgCB.checked = false;
                        } else {
                            const personCB = document.querySelector(`.person-checkbox[data-person-id="${id}"]`);
                            if (personCB) personCB.checked = false;
                        }
                        
                        item.remove();
                        
                        // 如果没有选择项，显示"暂无选择"
                        if (selectedItemsContainer.querySelectorAll('.selected-item').length === 0) {
                            if (selectedNone) {
                                selectedNone.style.display = '';
                            }
                        }
                    });
                    
                    selectedItemsContainer.appendChild(item);
                } else if (!isChecked && existingItem) {
                    // 移除项目
                    existingItem.remove();
                    
                    // 如果没有选择项，显示"暂无选择"
                    if (selectedItemsContainer.querySelectorAll('.selected-item').length === 0) {
                        if (selectedNone) {
                            selectedNone.style.display = '';
                        }
                    }
                }
            }

            // 根据选中的组织过滤人员
            function filterPersonsByOrg() {
                const selectedOrgs = Array.from(document.querySelectorAll('.org-checkbox:checked')).map(cb => cb.getAttribute('data-org-id'));
                const personItems = document.querySelectorAll('.person-list label');
                
                if (selectedOrgs.length === 0) {
                    // 如果没有选中组织，显示所有人员
                    personItems.forEach(item => {
                        item.style.display = '';
                    });
                } else {
                    // 只显示选中组织的人员
                    personItems.forEach(item => {
                        const checkbox = item.querySelector('.person-checkbox');
                        const orgId = checkbox.getAttribute('data-org-id');
                        
                        if (selectedOrgs.includes(orgId)) {
                            item.style.display = '';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                }
            }
        });
    </script>
</body>
</html> 