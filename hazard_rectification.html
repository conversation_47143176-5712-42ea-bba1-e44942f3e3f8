<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>隐患与整改 - 应急管理系统</title>
    <!-- 引入样式 -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- 添加 Element Plus 样式 (假设两个子页面都需要) -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <link rel="stylesheet" href="css/common.css"> <!-- Added common CSS for header -->
    <link rel="stylesheet" href="css/unified_header.css">
    <style>
        html, body { height: 100%; margin: 0; }
        body { font-family: "Microsoft YaHei", "PingFang SC", sans-serif; }
        /* Tab styles (borrowed from previous examples) */
        .tab-btn.active {
             color: #2563eb; /* blue-600 */
             border-bottom: 2px solid #2563eb;
         }
         .tab-content:not(.active) {
            display: none;
         }
         /* Minor adjustments for Element Plus components if needed */
         .el-tree-select, .el-date-editor { width: 100% !important; }

        /* Added from hazard_check_list.html */
        .status-badge {
          padding: 0.25rem 0.5rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 600;
          display: inline-block;
          text-align: center;
          min-width: 4rem; /* 保持一致宽度 */
        }
        .status-high { background-color: #FEE2E2; color: #991B1B; } /* 红色 - 高风险 */
        .status-medium { background-color: #FEF3C7; color: #92400E; } /* 黄色 - 中风险 */
        .status-low { background-color: #DBEAFE; color: #1E40AF; } /* 蓝色 - 低风险 */
        .status-none { background-color: #F3F4F6; color: #4B5563; } /* 灰色 - 无风险/已整改 */

        /* 自定义 Tree Select 样式 from hazard_check_list.html */
        /* .el-tree-select is already defined above, ensuring width: 100% */
        .el-select-dropdown__wrap {
            max-height: 400px;
        }
        .el-tree-node__content {
            height: 32px;
        }
        .el-tree-node__label {
            font-size: 14px;
        }
        .upload-list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }

        /* Styles from rectification_task_list.html START */
        /* 任务状态样式 */
        /* .status-badge is already defined, we might need to ensure these specific colors don't conflict or merge them if they are meant to be global */
        .status-pending { background-color: #FEF3C7; color: #92400E; } /* 黄色 - 待处理 */
        .status-progress { background-color: #DBEAFE; color: #1E40AF; } /* 蓝色 - 整改中 */
        .status-completed { background-color: #DEF7EC; color: #03543E; } /* 绿色 - 已完成 */
        .status-overdue { background-color: #FEE2E2; color: #991B1B; } /* 红色 - 已逾期 */
        /* Styles from rectification_task_list.html END */
    </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
    <!-- Navbar Start -->
    <header class="top-nav">
        <div class="system-title">
            <strong>广西公路水路安全畅通与应急处置系统</strong>
        </div>
        <nav class="tab-navigation">
            <a href="new_index.html" class="tab-button">风险一张图</a>
            <a href="my_check_tasks.html" class="tab-button active">风险隐患管理</a>
            <a href="plan_list.html" class="tab-button">应急处置</a>
            <button class="tab-button" onclick="alert('应急演练功能暂未开放');">应急演练</button>
        </nav>
    </header>
    <!-- Navbar End -->

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container h-full" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area -->
        <main id="main-content" class="flex-grow pb-8 px-6 bg-gray-100">
            <div class="py-6">
                <!-- 页面标题 -->
                <div class="mb-6">
                    <h2 class="text-2xl font-semibold text-gray-800">隐患与整改</h2>
                    <p class="text-sm text-gray-500 mt-1">整合查看隐患检查记录与整改任务</p>
                </div>

                <!-- 选项卡导航 -->
                <div class="bg-white rounded-t-lg shadow-sm mb-0">
                    <nav class="flex space-x-4 p-4 sm:px-6 border-b border-gray-200">
                        <button class="tab-btn px-3 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600 focus:outline-none active" data-tab="hazard-list-content">
                            <i class="fas fa-shield-alt mr-1"></i>隐患列表
                        </button>
                        <button class="tab-btn px-3 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="rectification-list-content">
                            <i class="fas fa-tasks mr-1"></i>整改任务列表
                        </button>
                    </nav>
                </div>

                <!-- 标签页内容区域 -->
                <div class="bg-white rounded-b-lg shadow-md overflow-hidden">
                    <!-- 隐患列表内容 -->
                    <section id="hazard-list-content" class="tab-content active p-6">
                        <!-- Content from hazard_check_list.html START -->
                        <div class="flex justify-between items-center mb-6">
                            <div>
                                <!-- Tab specific title can be minor if needed, or rely on tab button -->
                                <h3 class="text-xl font-semibold text-gray-700">隐患检查记录</h3>
                            </div>
                            <!-- <button id="btnAddHazard" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <i class="fas fa-plus mr-2"></i> 添加检查记录
                            </button> -->
                        </div>

                        <!-- 过滤栏 -->
                        <div class="bg-white p-4 rounded-lg shadow-sm mb-6 border border-gray-200">
                            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                                <div>
                                    <label for="filterCity" class="block text-sm font-medium text-gray-700 mb-1">市</label>
                                    <input type="text" id="filterCity" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入市名称">
                                </div>
                                <div>
                                    <label for="filterCounty" class="block text-sm font-medium text-gray-700 mb-1">区/县</label>
                                    <input type="text" id="filterCounty" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入区/县名称">
                                </div>
                                <div>
                                    <label for="filterOrgUnit" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                                    <div id="filterOrgApp">
                                        <el-tree-select
                                            v-model="selectedUnits"
                                            :data="unitOptions"
                                            multiple
                                            show-checkbox
                                            :props="{ value: 'value', label: 'label', children: 'children' }"
                                            placeholder="请选择单位"
                                            class="block w-full"
                                            @change="handleFilterOrgChange"
                                        />
                                    </div>
                                </div>
                                <div>
                                    <label for="filterCategory" class="block text-sm font-medium text-gray-700 mb-1">检查类别</label>
                                    <div id="filterCategoryApp">
                                        <el-tree-select
                                            v-model="selectedCategories"
                                            :data="categoryOptions"
                                            multiple
                                            show-checkbox
                                            check-strictly="false"
                                            :props="{ value: 'value', label: 'label', children: 'children' }"
                                            placeholder="请选择检查类别"
                                            class="block w-full"
                                            @change="handleFilterCategoryChange"
                                        />
                                    </div>
                                </div>
                                <div>
                                    <label for="filterRiskLevel" class="block text-sm font-medium text-gray-700 mb-1">风险等级</label>
                                    <select id="filterRiskLevel" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">全部</option>
                                        <option value="high">高</option>
                                        <option value="medium">中</option>
                                        <option value="low">低</option>
                                        <option value="none">无风险/已整改</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="filterIsHazard" class="block text-sm font-medium text-gray-700 mb-1">是否为隐患点</label>
                                    <select id="filterIsHazard" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">全部</option>
                                        <option value="yes">是</option>
                                        <option value="no">否</option>
                                    </select>
                                </div>
                            </div>
                             <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mt-4">
                                 <div>
                                    <label for="filterRoadNumber" class="block text-sm font-medium text-gray-700 mb-1">路段编号</label>
                                    <input type="text" id="filterRoadNumber" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入路段编号">
                                </div>
                                <div class="col-start-4 md:col-start-5 flex items-end space-x-2 justify-end">
                                    <button id="btnFilter" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                      <i class="fas fa-search mr-1"></i> 查询
                                    </button>
                                    <button id="btnResetFilter" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                      <i class="fas fa-undo mr-1"></i> 重置
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 数据表格 -->
                        <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">市</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">区/县</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查类别</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路段编号</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险等级</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">是否隐患点</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险点描述</th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="hazardListTableBody" class="bg-white divide-y divide-gray-200">
                                        <!-- 示例数据行 -->
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">南宁市</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">青秀区</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">南宁市交通运输局</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">风险路段-地质灾害风险</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">G324</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-high">高</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-semibold">是</td>
                                            <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">K1500+200处边坡有落石风险</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="1"><i class="fas fa-eye"></i></button>
                                                <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="1"><i class="fas fa-edit"></i></button>
                                                <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete" data-id="1"><i class="fas fa-trash-alt"></i></button>
                                                <button class="text-green-600 hover:text-green-800 focus:outline-none ml-2 btn-rectify" data-id="1" title="生成整改任务"><i class="fas fa-clipboard-list"></i></button>
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钦州市</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">灵山县</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钦州市交通运输局</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">基础保障设施隐患-防洪标识</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">S211</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-low">低</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">否</td>
                                            <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">桥梁限高标识不清</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="2"><i class="fas fa-eye"></i></button>
                                                <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="2"><i class="fas fa-edit"></i></button>
                                                <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete" data-id="2"><i class="fas fa-trash-alt"></i></button>
                                                <button class="text-green-600 hover:text-green-800 focus:outline-none ml-2 btn-rectify" data-id="2" title="生成整改任务"><i class="fas fa-clipboard-list"></i></button>
                                            </td>
                                        </tr>
                                         <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">玉林市</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">福绵区</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">玉林市交通运输局</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">涉灾隐患点-桥梁</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">X456</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-medium">中</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-semibold">是</td>
                                            <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">XX桥梁伸缩缝堵塞</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="3"><i class="fas fa-eye"></i></button>
                                                <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="3"><i class="fas fa-edit"></i></button>
                                                <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete" data-id="3"><i class="fas fa-trash-alt"></i></button>
                                                <button class="text-green-600 hover:text-green-800 focus:outline-none ml-2 btn-rectify" data-id="3" title="生成整改任务"><i class="fas fa-clipboard-list"></i></button>
                                            </td>
                                        </tr>
                                        <!-- 更多数据行 -->
                                    </tbody>
                                </table>
                            </div>
                            <!-- 分页 -->
                            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                                <div class="flex justify-between items-center">
                                    <div class="text-sm text-gray-700">
                                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">3</span> 条记录
                                    </div>
                                    <div>
                                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                                <span class="sr-only">上一页</span>
                                                <i class="fas fa-chevron-left"></i>
                                            </a>
                                            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-100">
                                                1
                                            </a>
                                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                                <span class="sr-only">下一页</span>
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Content from hazard_check_list.html END -->
                    </section>

                    <!-- 整改任务列表内容 -->
                    <section id="rectification-list-content" class="tab-content p-6">
                        <!-- Content from rectification_task_list.html START -->
                        <div class="flex justify-between items-center mb-6">
                            <div>
                                <h3 class="text-xl font-semibold text-gray-700">整改任务列表</h3>
                                <p class="text-sm text-gray-500 mt-1">跟踪和管理隐患整改任务的进度</p>
                            </div>
                            <button id="btnAddRectificationTask" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                <i class="fas fa-plus mr-2"></i> 添加任务
                            </button>
                        </div>

                        <!-- 过滤栏 -->
                        <div class="bg-white p-4 rounded-lg shadow-sm mb-6 border border-gray-200">
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div>
                                    <label for="filterTaskStatus" class="block text-sm font-medium text-gray-700 mb-1">任务状态</label>
                                    <select id="filterTaskStatus" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">全部</option>
                                        <option value="pending">待处理</option>
                                        <option value="progress">整改中</option>
                                        <option value="completed">已完成</option>
                                        <option value="overdue">已逾期</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="filterRectificationRespUnit" class="block text-sm font-medium text-gray-700 mb-1">责任单位</label>
                                    <div id="filterRectificationOrgApp">
                                         <el-tree-select
                                            v-model="selectedUnit"
                                            :data="unitOptions"
                                            :multiple="false"
                                            :check-strictly="true"
                                            :props="{ value: 'value', label: 'label', children: 'children' }"
                                            placeholder="请选择责任单位"
                                            class="block w-full"
                                            clearable
                                            @change="handleFilterOrgChange"
                                        />
                                    </div>
                                </div>
                                <div>
                                    <label for="filterRectificationDeadline" class="block text-sm font-medium text-gray-700 mb-1">整改期限</label>
                                    <div id="filterRectificationDateApp">
                                        <el-date-picker
                                            v-model="deadlineRange"
                                            type="daterange"
                                            range-separator="至"
                                            start-placeholder="开始日期"
                                            end-placeholder="结束日期"
                                            class="block w-full"
                                            value-format="YYYY-MM-DD"
                                            @change="handleDateChange"
                                        />
                                    </div>
                                </div>
                                <div class="flex items-end space-x-2 justify-end">
                                    <button id="btnRectificationFilter" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                      <i class="fas fa-search mr-1"></i> 查询
                                    </button>
                                    <button id="btnRectificationResetFilter" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                      <i class="fas fa-undo mr-1"></i> 重置
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 数据表格 -->
                        <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务ID</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联隐患</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">责任单位</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">责任人</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建日期</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">整改期限</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="rectificationListTableBody" class="bg-white divide-y divide-gray-200">
                                        <!-- 示例数据行 -->
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TASK001</td>
                                            <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs"><a href="#" class="text-blue-600 hover:underline btn-view-associated-hazard" data-hazard-id="1" title="ID:1, K1500+200处边坡有落石风险">隐患ID:1</a></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">南宁市交通运输局</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李工</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-28</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-08-10</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-pending">待处理</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-rectification" data-id="TASK001" title="查看"><i class="fas fa-eye"></i></button>
                                                <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-rectification" data-id="TASK001" title="编辑/更新状态"><i class="fas fa-edit"></i></button>
                                                <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-complete-rectification" data-id="TASK001" title="标记完成"><i class="fas fa-check-circle"></i></button>
                                                <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-rectification" data-id="TASK001" title="删除"><i class="fas fa-trash-alt"></i></button>
                                            </td>
                                        </tr>
                                         <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TASK002</td>
                                            <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs"><a href="#" class="text-blue-600 hover:underline btn-view-associated-hazard" data-hazard-id="2" title="ID:2, 桥梁限高标识不清">隐患ID:2</a></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钦州市交通运输局</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王工</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-29</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-08-05</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-progress">整改中</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                 <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-rectification" data-id="TASK002" title="查看"><i class="fas fa-eye"></i></button>
                                                <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-rectification" data-id="TASK002" title="编辑/更新状态"><i class="fas fa-edit"></i></button>
                                                <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-complete-rectification" data-id="TASK002" title="标记完成"><i class="fas fa-check-circle"></i></button>
                                                <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-rectification" data-id="TASK002" title="删除"><i class="fas fa-trash-alt"></i></button>
                                            </td>
                                        </tr>
                                          <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TASK003</td>
                                            <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs"><a href="#" class="text-blue-600 hover:underline btn-view-associated-hazard" data-hazard-id="3" title="ID:3, XX桥梁伸缩缝堵塞">隐患ID:3</a></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">玉林市交通运输局</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张工</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-20</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-25</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-overdue">已逾期</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-rectification" data-id="TASK003" title="查看"><i class="fas fa-eye"></i></button>
                                                <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-rectification" data-id="TASK003" title="编辑/更新状态"><i class="fas fa-edit"></i></button>
                                                <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-complete-rectification" data-id="TASK003" title="标记完成"><i class="fas fa-check-circle"></i></button>
                                                <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-rectification" data-id="TASK003" title="删除"><i class="fas fa-trash-alt"></i></button>
                                            </td>
                                        </tr>
                                        <!-- 更多数据行 -->
                                    </tbody>
                                </table>
                            </div>
                            <!-- 分页 -->
                            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                                <div class="flex justify-between items-center">
                                    <div class="text-sm text-gray-700">
                                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">3</span> 条记录
                                    </div>
                                    <div>
                                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                                <span class="sr-only">上一页</span>
                                                <i class="fas fa-chevron-left"></i>
                                            </a>
                                            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-100">
                                                1
                                            </a>
                                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                                <span class="sr-only">下一页</span>
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Content from rectification_task_list.html END -->
                    </section>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals related to Rectification Tasks START -->
    <!-- 查看/编辑任务模态框 (from rectification_task_list.html) -->
    <div id="rectificationTaskModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800" id="rectificationModalTitle">查看/编辑整改任务</h3>
                <button class="text-gray-400 hover:text-gray-500 focus:outline-none btn-close-rectification-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="px-6 py-4 max-h-[70vh] overflow-y-auto">
                <form id="rectificationTaskForm">
                    <input type="hidden" id="rectificationTaskId" name="rectificationTaskId">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 pb-4 border-b">
                         <div><strong class="text-gray-600 block mb-1">任务ID:</strong> <span id="view-rectificationTaskId"></span></div>
                         <div><strong class="text-gray-600 block mb-1">关联隐患ID:</strong> <a id="view-rectificationHazardLink" href="#" class="text-blue-600 hover:underline"></a></div>
                         <div>
                            <label for="modalRectificationRespUnit" class="block text-sm font-medium text-gray-700 mb-1">责任单位 <span class="text-red-500">*</span></label>
                            <div id="modalRectificationOrgApp">
                                <el-tree-select
                                    v-model="selectedUnit"
                                    :data="unitOptions"
                                    :multiple="false"
                                    :check-strictly="true"
                                    :props="{ value: 'value', label: 'label', children: 'children' }"
                                    placeholder="请选择单位"
                                    class="block w-full"
                                    clearable
                                    @change="handleModalOrgChange"
                                />
                            </div>
                        </div>
                         <div>
                             <label for="modalRectificationRespPerson" class="block text-sm font-medium text-gray-700 mb-1">责任人</label>
                             <input type="text" id="modalRectificationRespPerson" name="resp_person" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                         </div>
                         <div>
                             <label for="modalRectificationDeadline" class="block text-sm font-medium text-gray-700 mb-1">整改期限 <span class="text-red-500">*</span></label>
                             <div id="modalRectificationDateApp">
                                 <el-date-picker
                                    v-model="deadline"
                                    type="date"
                                    placeholder="选择日期"
                                    class="block w-full"
                                    value-format="YYYY-MM-DD"
                                />
                             </div>
                         </div>
                          <div>
                             <label for="modalRectificationTaskStatus" class="block text-sm font-medium text-gray-700 mb-1">任务状态 <span class="text-red-500">*</span></label>
                             <select id="modalRectificationTaskStatus" name="task_status" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="pending">待处理</option>
                                <option value="progress">整改中</option>
                                <option value="completed">已完成</option>
                                <option value="overdue">已逾期</option>
                             </select>
                         </div>
                    </div>
                    <div>
                        <label for="modalRectificationRemarks" class="block text-sm font-medium text-gray-700 mb-1">整改进度/备注</label>
                        <textarea id="modalRectificationRemarks" name="remarks" rows="4" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
                    </div>
                     <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">上传整改附件</label>
                        <input type="file" id="modalRectificationTaskFiles" name="task_files" multiple class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                        <div id="uploadedRectificationTaskFilesList" class="mt-2 text-sm text-gray-600"></div>
                    </div>
                </form>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-rectification-modal">
                  取消
                </button>
                <button id="btnSaveRectificationTask" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  保存更新
                </button>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 (Rectification Task Specific - Renamed ID if different from hazard list's delete modal) -->
    <div id="deleteRectificationConfirmModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center hidden">
         <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">确认删除整改任务</h3>
            </div>
            <div class="px-6 py-4">
                <p class="text-sm text-gray-700">您确定要删除这条整改任务吗？此操作无法撤销。</p>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-rectification-modal">
                  取消
                </button>
                <button id="btnConfirmRectificationDelete" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                  删除
                </button>
            </div>
        </div>
    </div>
    <!-- Modals related to Rectification Tasks END -->

    <!-- Modals from hazard_check_list.html START -->
    <!-- 添加/编辑隐患模态框 -->
    <div id="hazardModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800" id="modalTitle">添加检查记录</h3>
                <button class="text-gray-400 hover:text-gray-500 focus:outline-none btn-close-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="px-6 py-4 max-h-[70vh] overflow-y-auto">
                <form id="hazardForm">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="modalCategory" class="block text-sm font-medium text-gray-700 mb-1">检查类别 <span class="text-red-500">*</span></label>
                            <select id="modalCategory" name="category" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="">请选择</option>
                                <optgroup label="风险路段">
                                    <option value="risk_section_flood">山洪淹没区风险路段</option>
                                    <option value="risk_section_geology">地质灾害风险路段</option>
                                </optgroup>
                                <option value="management_mechanism">工作管理机制隐患</option>
                                <optgroup label="基础保障设施隐患">
                                     <option value="basic_facilities_sign">防洪标识</option>
                                     <option value="basic_facilities_trail">检查步道</option>
                                     <option value="basic_facilities_hazard">涉灾隐患点</option>
                                </optgroup>
                                 <optgroup label="涉灾隐患点">
                                    <option value="hazard_points_slope">边坡</option>
                                    <option value="hazard_points_drainage">防洪排水设施</option>
                                    <option value="hazard_points_bridge">桥梁</option>
                                    <option value="hazard_points_tunnel">隧道</option>
                                </optgroup>
                            </select>
                        </div>
                         <div>
                            <label for="modalCity" class="block text-sm font-medium text-gray-700 mb-1">市、区/县名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="modalCity" name="city_county" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="系统自动关联/手动选择" required>
                        </div>
                        <div>
                            <label for="modalOrgUnit" class="block text-sm font-medium text-gray-700 mb-1">所属单位 <span class="text-red-500">*</span></label>
                            <div id="modalOrgApp">
                                <el-tree-select
                                    v-model="selectedUnit"
                                    :data="unitOptions"
                                    :props="{ value: 'value', label: 'label', children: 'children' }"
                                    placeholder="请选择单位"
                                    class="block w-full"
                                    clearable
                                    @change="handleModalOrgChange"
                                />
                            </div>
                        </div>
                        <div>
                            <label for="modalRoadNumber" class="block text-sm font-medium text-gray-700 mb-1">公路编号 <span class="text-red-500">*</span></label>
                            <select id="modalRoadNumber" name="road_number" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="">请先选择市/区县</option>
                                <option value="G324">G324</option>
                                <option value="S211">S211</option>
                                <option value="X456">X456</option>
                            </select>
                        </div>
                         <div>
                            <label for="modalStartStake" class="block text-sm font-medium text-gray-700 mb-1">起点桩号</label>
                            <input type="text" id="modalStartStake" name="start_stake" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如: K1500+200">
                        </div>
                         <div>
                            <label for="modalEndStake" class="block text-sm font-medium text-gray-700 mb-1">止点桩号</label>
                            <input type="text" id="modalEndStake" name="end_stake" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如: K1500+500">
                        </div>
                    </div>

                    <div class="mt-4">
                      <label for="modalRiskDescription" class="block text-sm font-medium text-gray-700 mb-1">风险点描述 <span class="text-red-500">*</span></label>
                      <textarea id="modalRiskDescription" name="risk_description" rows="3" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required></textarea>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">现场照片/附件</label>
                            <input type="file" id="modalPhotos" name="photos" multiple class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                            <div id="uploadedFilesList" class="mt-2 text-sm text-gray-600"></div>
                        </div>
                        <div>
                            <label for="modalRiskLevel" class="block text-sm font-medium text-gray-700 mb-1">风险等级 <span class="text-red-500">*</span></label>
                            <select id="modalRiskLevel" name="risk_level" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="high">高</option>
                                <option value="medium">中</option>
                                <option value="low">低</option>
                                <option value="none">无风险</option>
                            </select>
                        </div>
                         <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">是否已采取措施 <span class="text-red-500">*</span></label>
                            <div class="flex items-center space-x-4 mt-1">
                                <label><input type="radio" name="measures_taken" value="yes" class="mr-1"> 是</label>
                                <label><input type="radio" name="measures_taken" value="no" class="mr-1" checked> 否</label>
                            </div>
                        </div>
                         <div>
                            <label for="modalMeasures" class="block text-sm font-medium text-gray-700 mb-1">已（拟）采取的措施</label>
                            <textarea id="modalMeasures" name="measures" rows="2" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="可多选预设措施，或手动填写"></textarea>
                            <input type="file" id="modalMeasureFiles" name="measure_files" multiple class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-1 file:px-2 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-gray-50 file:text-gray-700 hover:file:bg-gray-100" placeholder="上传方案/报告">
                            <div id="uploadedMeasureFilesList" class="mt-1 text-sm text-gray-600"></div>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 border-t pt-4">
                        <div>
                           <label class="block text-sm font-medium text-gray-700 mb-1">省级责任单位及人员</label>
                           <p class="text-sm text-gray-600">系统自动关联</p>
                        </div>
                         <div>
                           <label class="block text-sm font-medium text-gray-700 mb-1">复核责任单位及人员</label>
                           <p class="text-sm text-gray-600">系统自动关联</p>
                        </div>
                         <div>
                           <label class="block text-sm font-medium text-gray-700 mb-1">排查责任单位及人员</label>
                           <p class="text-sm text-gray-600">系统自动关联</p>
                        </div>
                    </div>
                </form>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-modal">
                  取消
                </button>
                <button id="btnSave" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  保存
                </button>
            </div>
        </div>
    </div>

    <!-- 查看详情模态框 -->
    <div id="viewModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">查看检查记录详情</h3>
                <button class="text-gray-400 hover:text-gray-500 focus:outline-none btn-close-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="px-6 py-4 max-h-[70vh] overflow-y-auto">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 pb-4 border-b">
                    <div><strong class="text-gray-600">检查类别:</strong> <span id="view-category"></span></div>
                    <div><strong class="text-gray-600">市/区县:</strong> <span id="view-city_county"></span></div>
                    <div><strong class="text-gray-600">所属单位:</strong> <span id="view-orgUnit"></span></div>
                    <div><strong class="text-gray-600">公路编号:</strong> <span id="view-roadNumber"></span></div>
                    <div><strong class="text-gray-600">起点桩号:</strong> <span id="view-startStake"></span></div>
                    <div><strong class="text-gray-600">止点桩号:</strong> <span id="view-endStake"></span></div>
                    <div><strong class="text-gray-600">风险等级:</strong> <span id="view-riskLevel"></span></div>
                    <div><strong class="text-gray-600">是否隐患点:</strong> <span id="view-isHazard" class="font-semibold"></span></div>
                    <div><strong class="text-gray-600">是否已采取措施:</strong> <span id="view-measuresTaken"></span></div>
                </div>
                 <div class="mb-4">
                    <strong class="text-gray-600 block mb-1">风险点描述:</strong>
                    <p id="view-riskDescription" class="text-gray-800 bg-gray-50 p-2 rounded"></p>
                 </div>
                  <div class="mb-4">
                    <strong class="text-gray-600 block mb-1">已（拟）采取的措施:</strong>
                    <p id="view-measures" class="text-gray-800 bg-gray-50 p-2 rounded"></p>
                 </div>
                 <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                     <div>
                         <strong class="text-gray-600 block mb-1">现场照片/附件:</strong>
                         <div id="view-photos" class="text-blue-600"></div>
                     </div>
                      <div>
                         <strong class="text-gray-600 block mb-1">措施附件:</strong>
                         <div id="view-measureFiles" class="text-blue-600"></div>
                     </div>
                 </div>
                 <div class="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
                     <div><strong class="text-gray-600">省级责任单位:</strong> <span id="view-provincialResp"></span></div>
                     <div><strong class="text-gray-600">复核责任单位:</strong> <span id="view-reviewResp"></span></div>
                     <div><strong class="text-gray-600">排查责任单位:</strong> <span id="view-inspectResp"></span></div>
                 </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 btn-close-modal">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div id="deleteConfirmModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">确认删除</h3>
            </div>
            <div class="px-6 py-4">
                <p class="text-sm text-gray-700">您确定要删除这条检查记录吗？相关整改任务（如有）将可能受到影响。此操作无法撤销。</p>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-modal">
                  取消
                </button>
                <button id="btnConfirmDelete" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                  删除
                </button>
            </div>
        </div>
    </div>
    <!-- Modals from hazard_check_list.html END -->

    <!-- Load Libraries (Vue & Element Plus might be needed later) -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>

    <!-- Load component HTML first -->
    <script src="js/navbarComponent_risk.js"></script>
    <script src="js/sidebarComponent_risk.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>

    <!-- Tab Switching Logic -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 选项卡切换逻辑
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabId = button.getAttribute('data-tab');

                    // Deactivate all buttons and content
                    tabButtons.forEach(btn => {
                        btn.classList.remove('text-blue-600', 'border-blue-600', 'active');
                        btn.classList.add('text-gray-500', 'border-transparent');
                    });
                    tabContents.forEach(content => {
                        content.classList.remove('active');
                    });

                    // Activate the clicked button and corresponding content
                    button.classList.add('text-blue-600', 'border-blue-600', 'active');
                    button.classList.remove('text-gray-500', 'border-transparent');
                    const activeTab = document.getElementById(tabId);
                    if (activeTab) {
                        activeTab.classList.add('active');
                    }
                });
            });

            // Optional: Ensure the first tab content is shown on load if needed
            // This might already be handled by the `active` class in HTML
            const initialActiveTabButton = document.querySelector('.tab-btn.active');
            const initialActiveTabContentId = initialActiveTabButton?.getAttribute('data-tab');
            if (initialActiveTabContentId) {
                const initialActiveTabContent = document.getElementById(initialActiveTabContentId);
                if (initialActiveTabContent) {
                     // Ensure only the correct initial tab is active
                    tabContents.forEach(content => {
                         if(content.id === initialActiveTabContentId) {
                             content.classList.add('active');
                         } else {
                             content.classList.remove('active');
                         }
                     });
                }
            }

            // --- Content from hazard_check_list.html JavaScript START ---
            // 模拟单位数据 (应从后端获取)
            const unitData = [{
                value: '1', label: '广西壮族自治区交通运输厅', children: [
                    { value: '1.1', label: '直属事业单位及专项机构', children: [
                            { value: '1.1.1', label: '自治区公路发展中心' },
                            { value: '1.1.2', label: '自治区高速公路发展中心' },
                            { value: '1.1.3', label: '自治区道路运输发展中心' }
                    ]},
                    { value: '1.2', label: '市级交通运输局', children: [
                            { value: '1.2.1', label: '钦州市交通运输局' },
                            { value: '1.2.2', label: '南宁市交通运输局' },
                            { value: '1.2.3', label: '玉林市交通运输局' }
                    ]}
                ]
            }];

            // Vue 应用 - 用于筛选栏所属单位多选树形选择器
            if (document.getElementById('filterOrgApp')) {
                const filterOrgApp = Vue.createApp({
                    data() { return { selectedUnits: [], unitOptions: unitData } },
                    methods: { handleFilterOrgChange(value) { console.log('筛选选中的单位:', value); } }
                }).use(ElementPlus);
                filterOrgApp.mount('#filterOrgApp');
            }

            // Vue 应用 - 用于模态框内所属单位单选树形选择器
            if (document.getElementById('modalOrgApp')) {
                const modalOrgApp = Vue.createApp({
                    data() { return { selectedUnit: null, unitOptions: unitData } },
                    methods: { handleModalOrgChange(value) { console.log('模态框选中的单位:', value); /* TODO: Link to hazardForm data */ } }
                }).use(ElementPlus);
                modalOrgApp.mount('#modalOrgApp');
            }


            // 模拟检查类别数据 (应从后端获取或配置)
            const categoryData = [
                { value: 'risk_section', label: '风险路段', children: [
                        { value: 'risk_section_flood', label: '山洪淹没区风险路段' },
                        { value: 'risk_section_geology', label: '地质灾害风险路段' }
                ]},
                { value: 'management_mechanism', label: '工作管理机制隐患' },
                { value: 'basic_facilities', label: '基础保障设施隐患', children: [
                        { value: 'basic_facilities_sign', label: '防洪标识' },
                        { value: 'basic_facilities_trail', label: '检查步道' },
                        { value: 'basic_facilities_hazard', label: '涉灾隐患点' }
                ]},
                { value: 'hazard_points', label: '涉灾隐患点', children: [
                        { value: 'hazard_points_slope', label: '边坡' },
                        { value: 'hazard_points_drainage', label: '防洪排水设施' },
                        { value: 'hazard_points_bridge', label: '桥梁' },
                        { value: 'hazard_points_tunnel', label: '隧道' }
                ]}
            ];

            // Vue 应用 - 用于筛选栏检查类别多选树形选择器
            if (document.getElementById('filterCategoryApp')) {
                const filterCategoryApp = Vue.createApp({
                    data() { return { selectedCategories: [], categoryOptions: categoryData } },
                    methods: { handleFilterCategoryChange(value) { console.log('筛选选中的类别:', value); } }
                }).use(ElementPlus);
                filterCategoryApp.mount('#filterCategoryApp');
            }

            const hazardModal = document.getElementById('hazardModal');
            const viewModal = document.getElementById('viewModal');
            const deleteConfirmModal = document.getElementById('deleteConfirmModal');
            const btnAddHazard = document.getElementById('btnAddHazard');
            const hazardForm = document.getElementById('hazardForm');
            const modalTitle = document.getElementById('modalTitle');
            let currentEditId = null; // 用于存储当前编辑的记录ID
            let hazardIdToDelete = null; // 用于存储待删除的ID

            // --- 模态框控制 ---
            const openModal = (modal) => { if(modal) modal.classList.remove('hidden');}
            const closeModal = (modal) => { if(modal) modal.classList.add('hidden');}

            // 打开新增模态框
            if (btnAddHazard) {
                btnAddHazard.addEventListener('click', () => {
                    currentEditId = null; // 清除编辑ID
                    if(modalTitle) modalTitle.textContent = '添加检查记录';
                    if(hazardForm) hazardForm.reset();
                    const modalOrgAppInstance = document.getElementById('modalOrgApp')?.__vue_app__?._instance;
                    if (modalOrgAppInstance) {
                        modalOrgAppInstance.data.selectedUnit = null;
                    }
                    clearUploadedFiles('uploadedFilesList');
                    clearUploadedFiles('uploadedMeasureFilesList');
                    openModal(hazardModal);
                });
            }

            // 关闭模态框按钮
            document.querySelectorAll('.btn-close-modal').forEach(button => {
                button.addEventListener('click', (e) => {
                    // Find the closest modal and close it
                    const modalToClose = e.target.closest('.fixed.inset-0.z-50');
                    if (modalToClose) {
                        closeModal(modalToClose);
                    } else { // Fallback for modals not directly parent
                         closeModal(hazardModal);
                         closeModal(viewModal);
                         closeModal(deleteConfirmModal);
                    }
                });
            });

            // --- 列表操作按钮 (Hazard List Specific) ---
            // Ensure table body exists before adding listener
            const hazardListTableBody = document.getElementById('hazardListTableBody');
            if (hazardListTableBody) {
                hazardListTableBody.addEventListener('click', (event) => {
                    const target = event.target.closest('button');
                    if (!target) return;

                    const id = target.getAttribute('data-id');

                    if (target.classList.contains('btn-view')) {
                        console.log('查看 ID:', id);
                        populateViewModal(id);
                        openModal(viewModal);
                    } else if (target.classList.contains('btn-edit')) {
                        console.log('编辑 ID:', id);
                        currentEditId = id;
                        if(modalTitle) modalTitle.textContent = '编辑检查记录';
                        populateEditModal(id);
                        openModal(hazardModal);
                    } else if (target.classList.contains('btn-delete')) {
                        console.log('删除 ID:', id);
                        hazardIdToDelete = id;
                        openModal(deleteConfirmModal);
                    } else if (target.classList.contains('btn-rectify')) {
                        console.log('生成整改任务 ID:', id);
                        alert(`模拟为记录 ${id} 生成整改任务`);
                        // TODO: Navigate or open modal for rectification task creation
                        // This might involve switching to the '整改任务列表' tab and pre-filling data
                        // For now, just an alert.
                    }
                });
            }


            // --- 模态框表单处理 (Hazard Modal Specific) ---
            const modalPhotosInput = document.getElementById('modalPhotos');
            if (modalPhotosInput) {
                modalPhotosInput.addEventListener('change', function(e) {
                    displayUploadedFiles(e.target.files, 'uploadedFilesList');
                });
            }
            const modalMeasureFilesInput = document.getElementById('modalMeasureFiles');
            if (modalMeasureFilesInput) {
                modalMeasureFilesInput.addEventListener('change', function(e) {
                    displayUploadedFiles(e.target.files, 'uploadedMeasureFilesList');
                });
            }

             // 保存按钮 (Hazard Modal Specific)
            const btnSave = document.getElementById('btnSave');
            if (btnSave) {
                btnSave.addEventListener('click', () => {
                    if (!hazardForm) return;
                    // TODO: 表单验证
                    const formData = new FormData(hazardForm);
                    const modalOrgAppInstance = document.getElementById('modalOrgApp')?.__vue_app__?._instance;
                    if (modalOrgAppInstance) {
                         formData.append('organization_unit_id', modalOrgAppInstance.data.selectedUnit);
                    }

                    const data = Object.fromEntries(formData.entries());

                    if (currentEditId) {
                        console.log('更新记录 ID:', currentEditId, '数据:', data);
                        alert(`更新记录 ${currentEditId} 成功 (模拟)`);
                    } else {
                        console.log('新增记录 数据:', data);
                         alert('新增记录成功 (模拟)');
                    }
                    closeModal(hazardModal);
                    // TODO: 刷新列表
                });
            }


            // --- 删除确认 (Hazard Delete Modal Specific) ---
            const btnConfirmDelete = document.getElementById('btnConfirmDelete');
            if (btnConfirmDelete) {
                btnConfirmDelete.addEventListener('click', () => {
                     if (hazardIdToDelete) {
                        console.log('确认删除 ID:', hazardIdToDelete);
                        alert(`删除记录 ${hazardIdToDelete} 成功 (模拟)`);
                        hazardIdToDelete = null;
                        closeModal(deleteConfirmModal);
                        // TODO: 刷新列表
                     }
                });
            }

            // --- 筛选栏处理 (Hazard List Specific) ---
            const btnFilter = document.getElementById('btnFilter');
            if (btnFilter) {
                 btnFilter.addEventListener('click', () => {
                    const filterOrgAppInstance = document.getElementById('filterOrgApp')?.__vue_app__?._instance;
                    const filterCategoryAppInstance = document.getElementById('filterCategoryApp')?.__vue_app__?._instance;

                    const filterData = {
                        city: document.getElementById('filterCity')?.value,
                        county: document.getElementById('filterCounty')?.value,
                        units: filterOrgAppInstance ? filterOrgAppInstance.data.selectedUnits : [],
                        categories: filterCategoryAppInstance ? filterCategoryAppInstance.data.selectedCategories : [],
                        riskLevel: document.getElementById('filterRiskLevel')?.value,
                        isHazard: document.getElementById('filterIsHazard')?.value,
                        roadNumber: document.getElementById('filterRoadNumber')?.value,
                    };
                    console.log('应用筛选:', filterData);
                    alert('应用筛选条件 (模拟)');
                });
            }


            const btnResetFilter = document.getElementById('btnResetFilter');
            if (btnResetFilter) {
                btnResetFilter.addEventListener('click', () => {
                    const filterCity = document.getElementById('filterCity');
                    if(filterCity) filterCity.value = '';
                    const filterCounty = document.getElementById('filterCounty');
                    if(filterCounty) filterCounty.value = '';

                    const filterOrgAppInstance = document.getElementById('filterOrgApp')?.__vue_app__?._instance;
                    if (filterOrgAppInstance) filterOrgAppInstance.data.selectedUnits = [];

                    const filterCategoryAppInstance = document.getElementById('filterCategoryApp')?.__vue_app__?._instance;
                    if (filterCategoryAppInstance) filterCategoryAppInstance.data.selectedCategories = [];

                    const filterRiskLevel = document.getElementById('filterRiskLevel');
                    if(filterRiskLevel) filterRiskLevel.value = '';
                    const filterIsHazard = document.getElementById('filterIsHazard');
                    if(filterIsHazard) filterIsHazard.value = '';
                    const filterRoadNumber = document.getElementById('filterRoadNumber');
                    if(filterRoadNumber) filterRoadNumber.value = '';

                    console.log('重置筛选条件');
                    alert('重置筛选条件 (模拟)');
                });
            }


            // --- 辅助函数 (Copied from hazard_check_list.html) ---
            function populateViewModal(id) {
              console.log(`模拟获取 ID=${id} 的数据填充查看模态框`);
              const modal = document.getElementById('viewModal');
              if (!modal) {
                  console.error("#viewModal not found!");
                  return;
              }

              const categoryEl = modal.querySelector('#view-category');
              if (categoryEl) categoryEl.textContent = '风险路段-地质灾害风险'; else console.error("#view-category not found in #viewModal");

              const cityCountyEl = modal.querySelector('#view-city_county');
              if (cityCountyEl) cityCountyEl.textContent = '南宁市 / 青秀区'; else console.error("#view-city_county not found in #viewModal");

              const orgUnitEl = modal.querySelector('#view-orgUnit');
              if (orgUnitEl) orgUnitEl.textContent = '南宁市交通运输局'; else console.error("#view-orgUnit not found in #viewModal");

              const roadNumberEl = modal.querySelector('#view-roadNumber');
              if (roadNumberEl) roadNumberEl.textContent = 'G324'; else console.error("#view-roadNumber not found in #viewModal");

              const startStakeEl = modal.querySelector('#view-startStake');
              if (startStakeEl) startStakeEl.textContent = 'K1500+200'; else console.error("#view-startStake not found in #viewModal");

              const endStakeEl = modal.querySelector('#view-endStake');
              if (endStakeEl) endStakeEl.textContent = 'K1500+500'; else console.error("#view-endStake not found in #viewModal");

              const riskLevelEl = modal.querySelector('#view-riskLevel');
              if (riskLevelEl) riskLevelEl.innerHTML = '<span class="status-badge status-high">高</span>'; else console.error("#view-riskLevel not found in #viewModal");

              const isHazardEl = modal.querySelector('#view-isHazard');
              if (isHazardEl) {
                  isHazardEl.textContent = '是';
                  isHazardEl.classList.add('text-red-600');
                  isHazardEl.classList.remove('text-gray-500');
              } else { console.error("#view-isHazard not found in #viewModal"); }

              const measuresTakenEl = modal.querySelector('#view-measuresTaken');
              if (measuresTakenEl) measuresTakenEl.textContent = '否'; else console.error("#view-measuresTaken not found in #viewModal");

              const riskDescriptionEl = modal.querySelector('#view-riskDescription');
              if (riskDescriptionEl) riskDescriptionEl.textContent = `记录 ${id}: K1500+200处边坡有落石风险，需尽快处理。`; else console.error("#view-riskDescription not found in #viewModal");

              const measuresEl = modal.querySelector('#view-measures');
              if (measuresEl) measuresEl.textContent = '尚未采取措施'; else console.error("#view-measures not found in #viewModal");

              const photosEl = modal.querySelector('#view-photos');
              if (photosEl) photosEl.innerHTML = '<a href="#" class="hover:underline">photo1.jpg</a>, <a href="#" class="hover:underline">photo2.jpg</a>'; else console.error("#view-photos not found in #viewModal");

              const measureFilesEl = modal.querySelector('#view-measureFiles');
              if (measureFilesEl) measureFilesEl.innerHTML = '无'; else console.error("#view-measureFiles not found in #viewModal");

              const provincialRespEl = modal.querySelector('#view-provincialResp');
              if (provincialRespEl) provincialRespEl.textContent = '省交通厅 - 李四'; else console.error("#view-provincialResp not found in #viewModal");

              const reviewRespEl = modal.querySelector('#view-reviewResp');
              if (reviewRespEl) reviewRespEl.textContent = '市交通局 - 王五'; else console.error("#view-reviewResp not found in #viewModal");

              const inspectRespEl = modal.querySelector('#view-inspectResp');
              if (inspectRespEl) inspectRespEl.textContent = '南宁市交通运输局 - 张三'; else console.error("#view-inspectResp not found in #viewModal");
            }

            function populateEditModal(id) {
               console.log(`模拟获取 ID=${id} 的数据填充编辑模态框`);
               const currentHazardForm = document.getElementById('hazardForm');
               if (!currentHazardForm) {
                   console.error("#hazardForm not found!");
                   return;
               }
               currentHazardForm.reset();

               const modalCategoryEl = currentHazardForm.querySelector('#modalCategory');
               if (modalCategoryEl) modalCategoryEl.value = 'risk_section_geology'; else console.error("#modalCategory not found in #hazardForm");

               const modalCityEl = currentHazardForm.querySelector('#modalCity');
               if (modalCityEl) modalCityEl.value = '南宁市 / 青秀区'; else console.error("#modalCity not found in #hazardForm");

               const modalOrgAppInstance = document.getElementById('modalOrgApp')?.__vue_app__?._instance;
               if (modalOrgAppInstance) {
                   modalOrgAppInstance.data.selectedUnit = '1.2.2';
               } else {
                   console.error("Vue instance for #modalOrgApp not found or #modalOrgApp itself not found.");
               }

               const modalRoadNumberEl = currentHazardForm.querySelector('#modalRoadNumber');
               if (modalRoadNumberEl) modalRoadNumberEl.value = 'G324'; else console.error("#modalRoadNumber not found in #hazardForm");

               const modalStartStakeEl = currentHazardForm.querySelector('#modalStartStake');
               if (modalStartStakeEl) modalStartStakeEl.value = 'K1500+200'; else console.error("#modalStartStake not found in #hazardForm");

               const modalEndStakeEl = currentHazardForm.querySelector('#modalEndStake');
               if (modalEndStakeEl) modalEndStakeEl.value = 'K1500+500'; else console.error("#modalEndStake not found in #hazardForm");

               const modalRiskDescriptionEl = currentHazardForm.querySelector('#modalRiskDescription');
               if (modalRiskDescriptionEl) modalRiskDescriptionEl.value = `记录 ${id}: K1500+200处边坡有落石风险，需尽快处理。（编辑中）`; else console.error("#modalRiskDescription not found in #hazardForm");

               const modalRiskLevelEl = currentHazardForm.querySelector('#modalRiskLevel');
               if (modalRiskLevelEl) modalRiskLevelEl.value = 'high'; else console.error("#modalRiskLevel not found in #hazardForm");

               const measuresTakenNo = currentHazardForm.querySelector('input[name="measures_taken"][value="no"]');
               if(measuresTakenNo) measuresTakenNo.checked = true; else console.error("Radio button measures_taken[no] not found in #hazardForm");

               const modalMeasuresEl = currentHazardForm.querySelector('#modalMeasures');
               if (modalMeasuresEl) modalMeasuresEl.value = ''; else console.error("#modalMeasures not found in #hazardForm");

               clearUploadedFiles('uploadedFilesList');
               const filesList = document.getElementById('uploadedFilesList');
               if (filesList) {
                    filesList.innerHTML += `<div class="upload-list-item"><span>existing_photo1.jpg</span> <button type="button" class="text-red-500 text-xs" onclick="this.parentNode.remove()">删除</button></div>`;
                    filesList.innerHTML += `<div class="upload-list-item"><span>existing_photo2.jpg</span> <button type="button" class="text-red-500 text-xs" onclick="this.parentNode.remove()">删除</button></div>`;
               } else {
                   console.error("#uploadedFilesList not found");
               }
               clearUploadedFiles('uploadedMeasureFilesList');
            }

            function displayUploadedFiles(files, listElementId) {
                const listElement = document.getElementById(listElementId);
                if (listElement) {
                    for (let i = 0; i < files.length; i++) {
                        const file = files[i];
                        const listItem = document.createElement('div');
                        listItem.className = 'upload-list-item';
                        listItem.innerHTML = `<span>${file.name}</span> <button type="button" class="text-red-500 text-xs" onclick="this.parentNode.remove()">删除</button>`;
                        listElement.appendChild(listItem);
                    }
                } else {
                    console.error(`${listElementId} not found for displayUploadedFiles`);
                }
            }

            function clearUploadedFiles(listElementId) {
                 const listElement = document.getElementById(listElementId);
                 if (listElement) {
                    listElement.innerHTML = '';
                 } else {
                    console.error(`${listElementId} not found for clearUploadedFiles`);
                 }
            }
            // --- Content from hazard_check_list.html JavaScript END ---

            // --- Content for rectification_task_list.html JavaScript START ---
            // Define unit options (ensure this is available or adapt as needed)
            // If standardUnitOptions is the same as unitData, we can reuse unitData.
            // For now, let's assume they might be distinct for clarity during integration.
            const rectificationUnitOptions = [
                { value: '1', label: '广西壮族自治区交通运输厅', children: [
                    { value: '1.1', label: '直属事业单位及专项机构', children: [
                        { value: '1.1.1', label: '自治区公路发展中心' },
                        { value: '1.1.2', label: '自治区高速公路发展中心' },
                        { value: '1.1.3', label: '自治区道路运输发展中心' }
                    ]},
                    { value: '1.2', label: '市级交通运输局', children: [
                        { value: '1.2.1', label: '钦州市交通运输局' },
                        { value: '1.2.2', label: '南宁市交通运输局' },
                        { value: '1.2.3', label: '玉林市交通运输局' }
                    ]}
                ]}
            ];

            // Vue for Filter Org Unit (Rectification List)
            if (document.getElementById('filterRectificationOrgApp')) {
                const filterRectificationOrgApp = Vue.createApp({
                    data() {
                        return {
                            selectedUnit: null,
                            unitOptions: rectificationUnitOptions
                        }
                     },
                    methods: {
                        handleFilterOrgChange(value) {
                            console.log('Rectification Filter Org Unit:', value);
                        }
                     }
                }).use(ElementPlus);
                filterRectificationOrgApp.mount('#filterRectificationOrgApp');
            }

            // Vue for Filter Date Range (Rectification List)
            if (document.getElementById('filterRectificationDateApp')) {
                const filterRectificationDateApp = Vue.createApp({
                    data() { return { deadlineRange: [] } },
                    methods: { handleDateChange(value) { console.log('Rectification Filter Deadline Range:', value); } }
                }).use(ElementPlus);
                filterRectificationDateApp.mount('#filterRectificationDateApp');
            }

            // Vue for Modal Org Unit (Rectification Task Modal)
            if (document.getElementById('modalRectificationOrgApp')) {
                const modalRectificationOrgApp = Vue.createApp({
                    data() {
                        return {
                            selectedUnit: null,
                            unitOptions: rectificationUnitOptions
                        }
                     },
                    methods: {
                        handleModalOrgChange(value) { // Renamed from original to avoid conflict if any global scope issue
                            console.log('Rectification Modal Org Unit:', value);
                        }
                    }
                }).use(ElementPlus);
                modalRectificationOrgApp.mount('#modalRectificationOrgApp');
            }

            // Vue for Modal Deadline (Rectification Task Modal)
            if (document.getElementById('modalRectificationDateApp')) {
                const modalRectificationDateApp = Vue.createApp({
                    data() { return { deadline: '' } }
                }).use(ElementPlus);
                modalRectificationDateApp.mount('#modalRectificationDateApp');
            }

            const rectificationTaskModal = document.getElementById('rectificationTaskModal');
            const deleteRectificationConfirmModal = document.getElementById('deleteRectificationConfirmModal');
            const rectificationTaskForm = document.getElementById('rectificationTaskForm');
            const rectificationModalTitle = document.getElementById('rectificationModalTitle');
            let currentRectificationTaskId = null;
            let rectificationTaskIdToDelete = null;

            // Open/Close for Rectification Modals (can reuse generic openModal/closeModal if IDs are passed)
            // For clarity, specific close buttons might exist: btn-close-rectification-modal
            document.querySelectorAll('.btn-close-rectification-modal').forEach(button => {
                button.addEventListener('click', () => {
                    // Determine which modal to close based on parent or specific ID
                    if (button.closest('#rectificationTaskModal')) {
                        closeModal(rectificationTaskModal); // Assumes closeModal can take a DOM element
                    }
                    if (button.closest('#deleteRectificationConfirmModal')) {
                        closeModal(deleteRectificationConfirmModal);
                    }
                });
            });

            // Event listener for rectification list table buttons
            const rectificationListTableBody = document.getElementById('rectificationListTableBody');
            if (rectificationListTableBody) {
                rectificationListTableBody.addEventListener('click', (event) => {
                    const target = event.target.closest('button, a');
                    if (!target) return;
                    const id = target.getAttribute('data-id');

                    if (target.classList.contains('btn-view-rectification') || target.classList.contains('btn-edit-rectification')) {
                        console.log('View/Edit Rectification Task ID:', id);
                        currentRectificationTaskId = id;
                        populateRectificationTaskModal(id);
                        openModal(rectificationTaskModal); // Assumes openModal can take a DOM element
                    } else if (target.classList.contains('btn-complete-rectification')) {
                        console.log('Complete Rectification Task ID:', id);
                        if (confirm(`确认将整改任务 ${id} 标记为完成吗?`)) {
                            alert(`任务 ${id} 已标记为完成 (模拟)`);
                            // TODO: Send request to mark task as complete & Refresh list or update row status
                        }
                    } else if (target.classList.contains('btn-delete-rectification')) {
                        console.log('Delete Rectification Task ID:', id);
                        rectificationTaskIdToDelete = id;
                        openModal(deleteRectificationConfirmModal);
                    } else if (target.classList.contains('btn-view-associated-hazard')) {
                        event.preventDefault();
                        const hazardId = target.getAttribute('data-hazard-id');
                        console.log('View Associated Hazard ID:', hazardId);
                        // TODO: Implement logic to show hazard details (e.g., switch tab and open viewModal, or a new dedicated modal)
                        alert(`查看关联的隐患详情 ID: ${hazardId} (功能待实现)`);
                        // Example: Switch to the first tab and open the hazard view modal
                        // document.querySelector('.tab-btn[data-tab="hazard-list-content"]').click();
                        // setTimeout(() => { // Ensure tab switch is complete
                        //     populateViewModal(hazardId); // Assumes populateViewModal is globally accessible
                        //     const hazardViewModal = document.getElementById('viewModal');
                        //     if(hazardViewModal) openModal(hazardViewModal);
                        // }, 100);
                    }
                });
            }

            // Add New Rectification Task Button
            const btnAddRectificationTask = document.getElementById('btnAddRectificationTask');
            if (btnAddRectificationTask) {
                btnAddRectificationTask.addEventListener('click', () => {
                    currentRectificationTaskId = null;
                    if(rectificationModalTitle) rectificationModalTitle.textContent = '添加整改任务';
                    if(rectificationTaskForm) rectificationTaskForm.reset();
                    clearUploadedFiles('uploadedRectificationTaskFilesList');

                    const modalOrgAppInstance = document.getElementById('modalRectificationOrgApp')?.__vue_app__?._instance;
                    if (modalOrgAppInstance) modalOrgAppInstance.data.selectedUnit = null;

                    const modalDateAppInstance = document.getElementById('modalRectificationDateApp')?.__vue_app__?._instance;
                    if (modalDateAppInstance) modalDateAppInstance.data.deadline = '';

                    const taskIdSpan = document.getElementById('view-rectificationTaskId');
                    if(taskIdSpan) taskIdSpan.textContent = '系统自动生成';
                    const hazardLink = document.getElementById('view-rectificationHazardLink');
                    if(hazardLink) {
                        hazardLink.textContent = '请在下方选择或关联';
                        hazardLink.removeAttribute('href');
                    }
                    openModal(rectificationTaskModal);
                });
            }

            // Save Rectification Task button
            const btnSaveRectificationTask = document.getElementById('btnSaveRectificationTask');
            if (btnSaveRectificationTask) {
                btnSaveRectificationTask.addEventListener('click', () => {
                    if (!rectificationTaskForm) return;
                    // TODO: Form validation
                    const formData = new FormData(rectificationTaskForm);
                    const modalOrgAppInstance = document.getElementById('modalRectificationOrgApp')?.__vue_app__?._instance;
                    if (modalOrgAppInstance) formData.append('organization_unit_id', modalOrgAppInstance.data.selectedUnit);

                    const modalDateAppInstance = document.getElementById('modalRectificationDateApp')?.__vue_app__?._instance;
                    if (modalDateAppInstance) formData.append('deadline', modalDateAppInstance.data.deadline);

                    // Handle files (example: send names)
                    const taskFilesInput = document.getElementById('modalRectificationTaskFiles');
                    if (taskFilesInput) {
                        const taskFiles = taskFilesInput.files;
                        for(let i=0; i<taskFiles.length; i++){ formData.append('task_files[]', taskFiles[i].name); }
                    }

                    const data = Object.fromEntries(formData.entries());
                    console.log('Saving Rectification Task ID:', currentRectificationTaskId, 'Data:', data);
                    alert(`任务 ${currentRectificationTaskId || 'NEW'} 更新/保存成功 (模拟)`);
                    closeModal(rectificationTaskModal);
                    // TODO: Refresh list
                });
            }

            // Confirm Delete (Rectification Task) button
            const btnConfirmRectificationDelete = document.getElementById('btnConfirmRectificationDelete');
            if (btnConfirmRectificationDelete) {
                btnConfirmRectificationDelete.addEventListener('click', () => {
                    if (rectificationTaskIdToDelete) {
                        console.log('Confirm Delete Rectification Task ID:', rectificationTaskIdToDelete);
                        alert(`整改任务 ${rectificationTaskIdToDelete} 删除成功 (模拟)`);
                        rectificationTaskIdToDelete = null;
                        closeModal(deleteRectificationConfirmModal);
                        // TODO: Refresh list
                    }
                });
            }

            // Filter buttons (Rectification List)
            const btnRectificationFilter = document.getElementById('btnRectificationFilter');
            if (btnRectificationFilter) {
                btnRectificationFilter.addEventListener('click', () => {
                    const filterOrgAppInstance = document.getElementById('filterRectificationOrgApp')?.__vue_app__?._instance;
                    const filterDateAppInstance = document.getElementById('filterRectificationDateApp')?.__vue_app__?._instance;
                    const filterData = {
                        status: document.getElementById('filterTaskStatus')?.value,
                        unit: filterOrgAppInstance ? filterOrgAppInstance.data.selectedUnit : null,
                        deadlineStart: filterDateAppInstance?.deadlineRange ? filterDateAppInstance.deadlineRange[0] : '',
                        deadlineEnd: filterDateAppInstance?.deadlineRange ? filterDateAppInstance.deadlineRange[1] : ''
                    };
                    console.log('Apply Rectification Filter:', filterData);
                    alert('应用整改任务筛选条件 (模拟)');
                    // TODO: Apply filter and refresh list
                });
            }

            const btnRectificationResetFilter = document.getElementById('btnRectificationResetFilter');
            if (btnRectificationResetFilter) {
                btnRectificationResetFilter.addEventListener('click', () => {
                    const filterTaskStatusEl = document.getElementById('filterTaskStatus');
                    if(filterTaskStatusEl) filterTaskStatusEl.value = '';

                    const filterOrgAppInstance = document.getElementById('filterRectificationOrgApp')?.__vue_app__?._instance;
                    if(filterOrgAppInstance) filterOrgAppInstance.data.selectedUnit = null;

                    const filterDateAppInstance = document.getElementById('filterRectificationDateApp')?.__vue_app__?._instance;
                    if(filterDateAppInstance) filterDateAppInstance.deadlineRange = [];

                    console.log('Reset Rectification Filter');
                    alert('重置整改任务筛选条件 (模拟)');
                    // TODO: Reset filter and refresh list
                });
            }

            function populateRectificationTaskModal(taskId) {
                console.log(`Populating modal for Rectification Task ID: ${taskId}`);
                if(rectificationModalTitle) rectificationModalTitle.textContent = '编辑整改任务';
                if(rectificationTaskForm) rectificationTaskForm.reset();
                clearUploadedFiles('uploadedRectificationTaskFilesList');

                const taskIdInput = document.getElementById('rectificationTaskId');
                if(taskIdInput) taskIdInput.value = taskId;
                const viewTaskIdSpan = document.getElementById('view-rectificationTaskId');
                if(viewTaskIdSpan) viewTaskIdSpan.textContent = taskId;

                // Simulate fetching data - replace with actual data retrieval
                let hazardId, respUnitId, respPerson, deadline, status, remarks;
                if (taskId === 'TASK001') {
                    hazardId = '1'; respUnitId = '1.2.2'; respPerson = '李工'; deadline = '2024-08-10'; status = 'pending'; remarks = '尚未开始处理。';
                } else if (taskId === 'TASK002') {
                    hazardId = '2'; respUnitId = '1.2.1'; respPerson = '王工'; deadline = '2024-08-05'; status = 'progress'; remarks = '已联系施工队，准备更换标识。';
                } else { // TASK003 or default
                     hazardId = '3'; respUnitId = '1.2.3'; respPerson = '张工'; deadline = '2024-07-25'; status = 'overdue'; remarks = '已过整改期，未完成。';
                }

                const hazardLink = document.getElementById('view-rectificationHazardLink');
                if(hazardLink) {
                    hazardLink.href = `#`; // In a real scenario, this might link to the hazard view
                    hazardLink.textContent = `隐患ID:${hazardId}`;
                    hazardLink.setAttribute('data-hazard-id', hazardId);
                }

                const modalOrgAppInstance = document.getElementById('modalRectificationOrgApp')?.__vue_app__?._instance;
                if (modalOrgAppInstance) modalOrgAppInstance.data.selectedUnit = respUnitId;

                const respPersonInput = document.getElementById('modalRectificationRespPerson');
                if(respPersonInput) respPersonInput.value = respPerson;

                const modalDateAppInstance = document.getElementById('modalRectificationDateApp')?.__vue_app__?._instance;
                if(modalDateAppInstance) modalDateAppInstance.data.deadline = deadline;

                const taskStatusSelect = document.getElementById('modalRectificationTaskStatus');
                if(taskStatusSelect) taskStatusSelect.value = status;

                const remarksTextarea = document.getElementById('modalRectificationRemarks');
                if(remarksTextarea) remarksTextarea.value = remarks;

                // Simulate existing files - adapt displayUploadedFiles if its signature/behavior for rectification is different
                // For now, assuming clearUploadedFiles and displayUploadedFiles are generic enough
                const filesList = document.getElementById('uploadedRectificationTaskFilesList');
                if (filesList) {
                    // Example: filesList.innerHTML += `<div class="upload-list-item"><span>previous_report.pdf</span> ...</div>`;
                } else {
                    console.error("#uploadedRectificationTaskFilesList not found for populate");
                }
            }
            // --- Content for rectification_task_list.html JavaScript END ---
        });
    </script>
</body>
</html>