<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预案库 - 应急管理系统</title>
    <!-- Common CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/unified_header.css">
    <!-- Add Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        /* Keep page-specific styles, removed old layout styles */
         body {
            font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
            margin: 0;
        }
        .sidebar-menu-item { /* Kept for potential direct use if needed, though loaded component uses it */
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            color: #4b5563;
            transition: all 0.2s;
            text-decoration: none;
        }

        .sidebar-menu-item:hover {
            background-color: #f3f4f6;
            color: #1f2937;
        }
         /* Tab styles remain */
         .tab-btn.active {
             color: #2563eb;
             border-bottom: 2px solid #2563eb;
         }
         .tab-content:not(.active) {
            display: none;
         }

    </style>
</head>
<body class="bg-gray-100 flex flex-col min-h-screen"> <!-- Removed sidebar-expanded class -->
    <!-- Navbar Placeholder -->
    <header class="top-nav">
        <div class="system-title">
            <strong>广西公路水路安全畅通与应急处置系统</strong>
        </div>
        <nav class="tab-navigation">
            <a href="new_index.html" class="tab-button">风险一张图</a>
            <a href="my_check_tasks.html" class="tab-button">风险隐患管理</a>
            <a href="plan_list.html" class="tab-button active">应急处置</a>
            <button class="tab-button" onclick="alert('应急演练功能暂未开放');">应急演练</button>
        </nav>
    </header>

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area - Added id="main-content", removed pt-16 which is now handled by loadComponents.js -->
        <main id="main-content" class="flex-grow pb-8 px-6 bg-gray-100 min-h-screen">
             <!-- ... existing main content from original file ... -->
        <div class="py-6">
            <!-- 页面标题部分 -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-bold text-gray-800">预案库</h2>
                    <p class="text-gray-600 mt-1">管理、检索和查看应急预案</p>
                </div>
                <!-- <div>
                    <a href="edit_plan.html" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <i class="fas fa-plus mr-2"></i> 新增预案
                    </a>
                </div> -->
            </div>

            <!-- 选项卡和搜索部分 -->
            <div class="bg-white rounded-t-lg shadow-sm mb-0">
                <div class="p-4 sm:px-6">
                    <nav class="flex space-x-4 overflow-x-auto pb-1">
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600 focus:outline-none active" data-tab="my-plans">
                            我的预案
                        </button>
                        <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="all-plans">
                            厅本级预案
                        </button>
                        <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="city-plans">
                            市级预案
                        </button>
                        <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="direct-unit-plans">
                            直属单位预案
                        </button>
                        <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="drafts">
                            草稿箱
                        </button>
                    </nav>
                </div>

                <!-- 搜索条件区域 -->
                <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                        <!-- Add id="filter-app" to the container -->
                        <div id="filter-app" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                                <label for="plan_type" class="block text-sm font-medium text-gray-700 mb-1\">预案类型</label>
                                <!-- Replace select with el-select -->
                                <el-select v-model="selectedPlanType" placeholder="请选择预案类型" clearable class="block w-full">
                                    <el-option v-for="item in planTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                        </div>

                        <div>
                                <label for="scope" class="block text-sm font-medium text-gray-700 mb-1\">适用范围</label>
                                <!-- Replace select with el-select -->
                                <el-select v-model="selectedScope" placeholder="请选择适用范围" clearable class="block w-full">
                                     <el-option v-for="item in scopeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                        </div>

                        <div>
                                <label for="compile_unit" class="block text-sm font-medium text-gray-700 mb-1\">编制单位</label>
                                <!-- Remove wrapper div, update v-model -->
                                <el-tree-select
                                    v-model="selectedCompileUnit"
                                    :data="unitOptions"
                                    :multiple="false"
                                    :check-strictly="true"
                                    placeholder="请选择编制单位"
                                    class="block w-full"
                                    clearable
                                    @change="handleUnitChange('compile', $event)"
                                />
                        </div>

                        <div>
                                <label for="apply_unit" class="block text-sm font-medium text-gray-700 mb-1\">适用单位</label>
                                <!-- Remove wrapper div, update v-model -->
                                 <el-tree-select
                                    v-model="selectedApplyUnit"
                                    :data="unitOptions"
                                    :multiple="false"
                                    :check-strictly="true"
                                    placeholder="请选择适用单位"
                                    class="block w-full"
                                    clearable
                                     @change="handleUnitChange('apply', $event)"
                                />
                        </div>

                        <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-1\">启用状态</label>
                                <!-- Replace select with el-select -->
                                <el-select v-model="selectedStatus" placeholder="请选择启用状态" clearable class="block w-full">
                                    <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                        </div>

                        <div>
                                <label for="check_status" class="block text-sm font-medium text-gray-700 mb-1\">本周检查状态</label>
                                <!-- Replace select with el-select -->
                                <el-select v-model="selectedCheckStatus" placeholder="请选择检查状态" clearable class="block w-full">
                                     <el-option v-for="item in checkStatusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                        </div>

                        <div>
                            <label for="keyword" class="block text-sm font-medium text-gray-700 mb-1">关键字</label>
                            <input type="text" id="keyword" placeholder="预案名称、正文关键字" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>

                    </div>

                    <div class="mt-4 flex justify-end">
                        <button class="bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mr-3">
                            <i class="fas fa-undo mr-1"></i> 重置
                        </button>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-search mr-1"></i> 搜索
                        </button>
                        <a href="edit_plan.html" class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 ml-3">
                            <i class="fas fa-plus mr-2"></i> 新增预案
                        </a>
                    </div>
                </div>
            </div>

            <!-- 预案列表部分 -->
            <div class="bg-white rounded-b-lg shadow-md overflow-hidden">
                <!-- 我的预案列表 -->
                <div id="my-plans" class="tab-content active">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预案类型</th>
                                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预案名称</th>
                                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">适用范围</th>
                                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">启用状态</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">本周检查状态</th>
                                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">编制单位</th>
                                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最新检查时间</th>
                                    <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">自然灾害类</td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900">某区低温雨雪冰冻灾害应急预案</td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">低温冰冻</td>
                                    <td class="px-3 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已启用</span>
                                    </td>
                                        <td class="px-3 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已检查</span>
                                        </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">应急管理局</td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2023-11-15</td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                            <a href="plan_detail.html" class="text-blue-600 hover:text-blue-900 mr-3">检查</a>
                                            <!-- <a href="edit_plan.html" class="text-blue-600 hover:text-blue-900 mr-3">检查</a> -->
                                        <button class="text-red-600 hover:text-red-900 mr-3">停用</button>
                                            <a href="plan_version_history.html" class="text-gray-600 hover:text-gray-900 mr-3">检查历史</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">自然灾害类</td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900">某区洪涝灾害应急预案</td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">江河洪水、渍涝灾害</td>
                                    <td class="px-3 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已启用</span>
                                    </td>
                                        <td class="px-3 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">未检查</span>
                                        </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">应急管理局</td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-20</td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                            <a href="plan_detail.html" class="text-blue-600 hover:text-blue-900 mr-3">检查</a>
                                            <!-- <a href="edit_plan.html" class="text-blue-600 hover:text-blue-900 mr-3">修改</a> -->
                                        <button class="text-red-600 hover:text-red-900 mr-3">停用</button>
                                            <a href="plan_version_history.html" class="text-gray-600 hover:text-gray-900 mr-3">检查历史</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">事故灾难类</td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900">某区危化品泄漏事故应急预案</td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">危险化学品泄漏</td>
                                    <td class="px-3 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">已停用</span>
                                    </td>
                                        <td class="px-3 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">未检查</span>
                                        </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">应急管理局</td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2023-09-05</td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                            <a href="plan_detail.html" class="text-blue-600 hover:text-blue-900 mr-3">检查</a>
                                            <!-- <a href="edit_plan.html" class="text-blue-600 hover:text-blue-900 mr-3">修改</a> -->
                                        <button class="text-green-600 hover:text-green-900 mr-3">启用</button>
                                            <a href="plan_version_history.html" class="text-gray-600 hover:text-gray-900 mr-3">检查历史</a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    显示第 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">10</span> 条
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">上一页</span>
                                        <i class="fas fa-chevron-left text-xs"></i>
                                    </a>
                                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-50">1</a>
                                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">2</a>
                                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">3</a>
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">下一页</span>
                                        <i class="fas fa-chevron-right text-xs"></i>
                                    </a>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 全区预案列表 -->
                <div id="all-plans" class="tab-content">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预案类型</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预案名称</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">适用范围</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">启用状态</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">本周检查状态</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">编制单位</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最新修订时间</th>
                                        <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">公路交通类</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900">广西壮族自治区公路交通突发事件应急预案</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">全区范围</td>
                                        <td class="px-3 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已启用</span>
                                        </td>
                                        <td class="px-3 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已检查</span>
                                        </td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">广西交通运输厅</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-10</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                            <a href="plan_detail.html" class="text-blue-600 hover:text-blue-900 mr-3">检查</a>
                                            <a href="plan_version_history.html" class="text-gray-600 hover:text-gray-900 mr-3">检查历史</a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">公共卫生类</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900">重大传染病疫情应急预案</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">全区范围</td>
                                        <td class="px-3 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已启用</span>
                                        </td>
                                        <td class="px-3 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">未检查</span>
                                        </td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">卫生健康委员会</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                            <a href="plan_detail.html" class="text-blue-600 hover:text-blue-900 mr-3">检查</a>
                                            <a href="plan_version_history.html" class="text-gray-600 hover:text-gray-900 mr-3">检查历史</a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-700">
                                        显示第 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">50</span> 条
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">上一页</span>
                                            <i class="fas fa-chevron-left text-xs"></i>
                                        </a>
                                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-50">1</a>
                                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">2</a>
                                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">3</a>
                                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">下一页</span>
                                            <i class="fas fa-chevron-right text-xs"></i>
                                        </a>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 市级预案列表 (新增) -->
                    <div id="city-plans" class="tab-content">
                        <!-- 市级预案内容将在这里 -->
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预案类型</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预案名称</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">适用范围</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">启用状态</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">本周检查状态</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">编制单位</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最新修订时间</th>
                                        <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">自然灾害类</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900">南宁市城市内涝应急预案</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">城市内涝</td>
                                        <td class="px-3 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已启用</span>
                                        </td>
                                        <td class="px-3 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已检查</span>
                                        </td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">南宁市应急管理局</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2023-08-10</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                            <a href="plan_detail.html" class="text-blue-600 hover:text-blue-900 mr-3">检查</a>
                                            <a href="plan_version_history.html" class="text-gray-600 hover:text-gray-900 mr-3">检查历史</a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">事故灾难类</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900">桂林市旅游交通安全事故应急预案</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">旅游交通安全</td>
                                        <td class="px-3 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已启用</span>
                                        </td>
                                        <td class="px-3 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">未检查</span>
                                        </td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">桂林市文化广电和旅游局</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-22</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                            <a href="plan_detail.html" class="text-blue-600 hover:text-blue-900 mr-3">检查</a>
                                            <a href="plan_version_history.html" class="text-gray-600 hover:text-gray-900 mr-3">检查历史</a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- 分页 (市级预案) -->
                        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-700">
                                        显示第 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">上一页</span>
                                            <i class="fas fa-chevron-left text-xs"></i>
                                        </a>
                                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-50">1</a>
                                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">下一页</span>
                                            <i class="fas fa-chevron-right text-xs"></i>
                                        </a>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 直属单位预案列表 (新增) -->
                    <div id="direct-unit-plans" class="tab-content">
                        <!-- 直属单位预案内容将在这里 -->
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预案类型</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预案名称</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">适用范围</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">启用状态</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">本周检查状态</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">编制单位</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最新修订时间</th>
                                        <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">事故灾难类</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900">公路发展中心网络安全应急预案</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">网络安全事件</td>
                                        <td class="px-3 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已启用</span>
                                        </td>
                                        <td class="px-3 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">未检查</span>
                                        </td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">自治区公路发展中心</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2024-02-15</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                            <a href="plan_detail.html" class="text-blue-600 hover:text-blue-900 mr-3">检查</a>
                                            <a href="plan_version_history.html" class="text-gray-600 hover:text-gray-900 mr-3">检查历史</a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">自然灾害类</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900">高速公路发展中心防汛应急预案</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">汛期公路保障</td>
                                        <td class="px-3 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已启用</span>
                                        </td>
                                        <td class="px-3 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已检查</span>
                                        </td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">自治区高速公路发展中心</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2024-04-01</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                            <a href="plan_detail.html" class="text-blue-600 hover:text-blue-900 mr-3">检查</a>
                                            <a href="plan_version_history.html" class="text-gray-600 hover:text-gray-900 mr-3">检查历史</a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- 分页 (直属单位预案) -->
                        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-700">
                                        显示第 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">上一页</span>
                                            <i class="fas fa-chevron-left text-xs"></i>
                                        </a>
                                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-50">1</a>
                                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">下一页</span>
                                            <i class="fas fa-chevron-right text-xs"></i>
                                        </a>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 草稿箱列表 -->
                    <div id="drafts" class="tab-content">
                         <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预案类型</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预案名称</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">适用范围</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">编制单位</th>
                                        <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">修改时间</th>
                                        <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- 示例草稿 -->
                                    <tr>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">事故灾难类</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900">（草稿）某区高温天气应急预案</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">高温天气</td>
                                         <td class="px-3 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">草稿</span>
                                        </td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">应急管理局</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2024-05-20 10:30</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                            <a href="edit_plan.html" class="text-blue-600 hover:text-blue-900 mr-3">编辑</a>
                                            <button class="text-red-600 hover:text-red-900 mr-3">删除</button>
                                        </td>
                                    </tr>
                                     <tr>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">自然灾害类</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900">（草稿）森林防火应急预案</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">森林火灾</td>
                                         <td class="px-3 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">草稿</span>
                                        </td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">林业局</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2024-05-18 15:00</td>
                                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                            <a href="edit_plan.html" class="text-blue-600 hover:text-blue-900 mr-3">编辑</a>
                                            <button class="text-red-600 hover:text-red-900 mr-3">删除</button>
                                        </td>
                                    </tr>
                                    <!-- 更多草稿... -->
                                </tbody>
                            </table>
                        </div>
                        <!-- 分页 -->
                        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                           <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-700">
                                        显示第 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">5</span> 条
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">上一页</span>
                                            <i class="fas fa-chevron-left text-xs"></i>
                                        </a>
                                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-50">1</a>
                                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">下一页</span>
                                            <i class="fas fa-chevron-right text-xs"></i>
                                        </a>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        </div>

    <script>
        // Keep page-specific Tab switching logic
        document.addEventListener('DOMContentLoaded', function() {
            // 选项卡切换
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabId = button.getAttribute('data-tab');

                    // 移除所有活动状态
                    tabButtons.forEach(btn => {
                        btn.classList.remove('text-blue-600', 'border-blue-600', 'active');
                        btn.classList.add('text-gray-500', 'border-transparent');
                    });

                    tabContents.forEach(content => {
                        content.classList.remove('active');
                    });

                    // 添加活动状态
                    button.classList.remove('text-gray-500', 'border-transparent');
                    button.classList.add('text-blue-600', 'border-blue-600', 'active');

                    const activeTab = document.getElementById(tabId);
                    if (activeTab) {
                        activeTab.classList.add('active');
                    }
                });
            });
            // Trigger click on the initially active tab to ensure content is shown
             const initialActiveTab = document.querySelector('.tab-btn.active');
             if (initialActiveTab) {
                 initialActiveTab.click(); // Simulate click to show content
             } else if (tabButtons.length > 0) {
                 tabButtons[0].click(); // Default to first tab if none are active
             }
        });
    </script>
     <!-- Load component HTML first -->
    <script src="js/navbarComponent.js"></script>
    <script src="js/sidebarComponent_emergency.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>

    <!-- Add Vue and Element Plus JS -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <script src="https://unpkg.com/element-plus"></script>

    <!-- Add Initialization Script for Tree Selects -->
    <script>
        // Remove old script content and replace with consolidated script
    </script>
    <!-- Add Consolidated Initialization Script for Filters -->
     <script>
        const standardUnitOptions = [
            { value: '1', label: '广西壮族自治区交通运输厅', children: [
                { value: '1.1', label: '直属事业单位及专项机构', children: [
                    { value: '1.1.1', label: '自治区公路发展中心' },
                    { value: '1.1.2', label: '自治区高速公路发展中心' },
                    { value: '1.1.3', label: '自治区道路运输发展中心' }
                ]},
                { value: '1.2', label: '市级交通运输局', children: [
                    { value: '1.2.1', label: '钦州市交通运输局' },
                    { value: '1.2.2', label: '南宁市交通运输局' },
                    { value: '1.2.3', label: '玉林市交通运输局' }
                ]}
            ]}
            // Add other top-level orgs if needed
        ];

         const FilterApp = {
            data() {
                return {
                    selectedPlanType: null,
                    selectedScope: null,
                    selectedCompileUnit: null,
                    selectedApplyUnit: null,
                    selectedStatus: null,
                    selectedCheckStatus: null,

                    unitOptions: standardUnitOptions, // For tree selects

                    planTypeOptions: [
                        { value: '', label: '全部' },
                        { value: '1', label: '自然灾害类' },
                        { value: '2', label: '事故灾难类' },
                        { value: '3', label: '公共卫生类' },
                        { value: '4', label: '社会安全类' }
                    ],
                    scopeOptions: [
                        { value: '', label: '全部' },
                        { value: '1', label: '江河洪水' },
                        { value: '2', label: '渍涝灾害' },
                        { value: '3', label: '山洪灾害' },
                        { value: '4', label: '台风风暴潮灾害' },
                        { value: '5', label: '水库垮坝' },
                        { value: '6', label: '低温冰冻' },
                        { value: '7', label: '危险化学品泄漏' }
                    ],
                    statusOptions: [
                         { value: '', label: '全部' },
                         { value: '1', label: '已启用' },
                         { value: '0', label: '已停用' }
                    ],
                    checkStatusOptions: [
                        { value: '', label: '全部' },
                        { value: 'checked', label: '已检查' },
                        { value: 'unchecked', label: '未检查' }
                    ]
                };
            },
            methods: {
                // Generic handler for simple selects (optional)
                 handleSelectChange(type, value) {
                    console.log(`Filter Changed - ${type}:`, value);
                },
                 // Specific handler for tree selects to know which one changed
                 handleUnitChange(type, value) {
                     console.log(`Unit Filter Changed - ${type}:`, value);
                 }
                 // You can add methods to trigger search or reset filters here
            }
        };

        const filterApp = Vue.createApp(FilterApp);
        filterApp.use(ElementPlus);
        filterApp.mount('#filter-app');

    </script>
</body>
</html>