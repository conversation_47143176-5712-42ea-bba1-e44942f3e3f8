/* 告警信息列表 */
.alert-list-container {
    margin-top: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.alert-list-container h4 {
    padding: 10px 15px;
    margin: 0;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
    font-size: 16px;
    color: #333;
}

.alert-tabs {
    display: flex;
    border-bottom: 1px solid #ddd;
    background-color: #f8f9fa;
}

.alert-tab-button {
    padding: 8px 15px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-size: 15px;
    color: #666;
    transition: all 0.2s ease;
}

.alert-tab-button:hover {
    color: #007bff;
}

.alert-tab-button.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background-color: #fff;
}

.alert-tab-content {
    display: none;
}

.alert-tab-content.active {
    display: block;
}

.alert-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.alert-item {
    padding: 8px 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    flex-direction: column;
    font-size: 15px;
}

.alert-item:last-child {
    border-bottom: none;
}

.alert-time {
    color: #888;
    font-size: 14px;
    margin-bottom: 2px;
}

.alert-content {
    display: flex;
    align-items: center;
}

.alert-level {
    display: inline-block;
    padding: 1px 5px;
    border-radius: 3px;
    font-size: 14px;
    margin-right: 8px;
    color: #fff;
}

.alert-item.high .alert-level {
    background-color: #dc3545;
}

.alert-item.medium .alert-level {
    background-color: #fd7e14;
}

.alert-item.low .alert-level {
    background-color: #28a745;
}

.alert-item.overdue-high .alert-level {
    background-color: #dc3545;
}

.alert-item.overdue-medium .alert-level {
    background-color: #fd7e14;
}

.alert-item.overdue-low .alert-level {
    background-color: #28a745;
}

.alert-item.overdue-high {
    /* 移除背景色 */
}

.alert-item.overdue-medium {
    /* 移除背景色 */
}

.alert-item.overdue-low {
    /* 移除背景色 */
}

.alert-text {
    flex: 1;
    color: #333;
}

/* 最新告警提示框 */
.latest-alert-container {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
    max-width: 400px;
    width: 100%;
}

.latest-alert {
    display: flex;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.latest-alert.high {
    border-left: 4px solid #dc3545;
}

.latest-alert.medium {
    border-left: 4px solid #fd7e14;
}

.latest-alert.low {
    border-left: 4px solid #28a745;
}

.alert-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    margin-right: 10px;
    color: #dc3545;
    font-size: 18px;
}

.latest-alert .alert-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.alert-title {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 5px;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alert-title .alert-time {
    font-weight: normal;
    font-size: 14px;
    color: #888;
}

.alert-message {
    font-size: 15px;
    color: #555;
}

.alert-actions {
    display: flex;
    flex-direction: column;
    margin-left: 10px;
}

.alert-more-btn {
    padding: 4px 8px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 3px;
    color: #007bff;
    font-size: 14px;
    cursor: pointer;
    margin-bottom: 5px;
}

.alert-more-btn:hover {
    background-color: #e9ecef;
}

.alert-close-btn {
    background: none;
    border: none;
    color: #aaa;
    cursor: pointer;
    font-size: 14px;
    padding: 4px;
    align-self: flex-end;
}

.alert-close-btn:hover {
    color: #666;
}
