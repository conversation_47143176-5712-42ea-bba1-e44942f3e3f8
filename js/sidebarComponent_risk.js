const sidebarHTML = `<!-- 左侧菜单-风险隐患 -->
<!-- Note: 'sidebar' class and fixed positioning might need adjustment in main CSS -->
<aside class="sidebar w-64 flex-shrink-0 bg-white shadow-md overflow-y-auto h-full">
    <nav class="py-4">
        
        <a href="my_check_tasks.html" class="sidebar-menu-item block">
            <i class="fas fa-shield-virus text-gray-600 w-6"></i>
            <span class="ml-3 text-gray-700">检查任务</span>
        </a>

        <a href="construction_projects.html" class="sidebar-menu-item block">
            <i class="fas fa-building text-gray-600 w-6"></i>
            <span class="ml-3 text-gray-700">在建项目</span>
        </a>
        
         <a href="hazard_rectification.html" class="sidebar-menu-item block">
            <i class="fas fa-shield-alt text-gray-600 w-6"></i>
            <span class="ml-3 text-gray-700">隐患与整改</span>
        </a>

         <a href="hazard_approval.html" class="sidebar-menu-item block">
            <i class="fas fa-clipboard-check text-gray-600 w-6"></i>
            <span class="ml-3 text-gray-700">检查审批</span>
        </a>
        
        <a href="check_template_list.html" class="sidebar-menu-item block">
            <i class="fas fa-list-alt text-gray-600 w-6"></i>
            <span class="ml-3 text-gray-700">检查下发与模板管理</span>
        </a>
        
        <!-- System settings removed for brevity, can be added back if needed -->
        <!-- <div class="px-4 mt-6 mb-2"> ... </div> -->
        
    </nav>
</aside>`;
