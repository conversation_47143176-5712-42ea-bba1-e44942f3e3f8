/**
 * 指挥调度系统主要功能
 */

// 模拟数据
const mockEvents = [
    {
        id: 'EVT001',
        title: 'G75高速多车相撞事故',
        reporter: '南宁市交通运输局',
        reportTime: '2024-12-19 14:30',
        eventTime: '2024-12-19 14:25',
        location: 'G75兰海高速南宁至柳州段K1205+300处',
        level: '重大事故',
        status: 'processing',
        responseStatus: 'activated',
        activatedPlan: '广西壮族自治区公路交通突发事件应急预案',
        activatedTime: '2024-12-19 14:35',
        activatedBy: '李明华厅长',
        responseLevel: 'Ⅱ级响应',
        casualties: '3人轻伤，无死亡',
        description: '雨天路滑，5辆车连环相撞'
    },
    {
        id: 'EVT002',
        title: '桂林隧道火灾事故',
        reporter: '桂林市交通运输局',
        reportTime: '2024-12-19 16:20',
        eventTime: '2024-12-19 16:00',
        location: '桂林市象山隧道',
        level: '较大事故',
        status: 'pending',
        responseStatus: 'recommended',
        recommendedPlan: '广西隧道火灾应急预案',
        recommendedLevel: 'Ⅲ级响应',
        recommendedReason: '事件类型匹配，影响范围评估',
        casualties: '暂无人员伤亡',
        description: '隧道内货车起火，烟雾较大'
    },
    {
        id: 'EVT003',
        title: '南宁绕城高速积水事件',
        reporter: '南宁市交通运输局',
        reportTime: '2024-12-18 09:15',
        eventTime: '2024-12-18 09:00',
        location: '南宁绕城高速东段',
        level: '一般事故',
        status: 'completed',
        responseStatus: 'completed',
        activatedPlan: '广西公路防汛应急预案',
        processingTime: '2小时30分钟',
        result: '积水排除，交通恢复',
        casualties: '无人员伤亡',
        description: '暴雨导致路面积水严重'
    }
];

// 指挥调度系统主对象
const CommandDispatch = {
    currentTab: 'event-report',
    currentSubTab: 'event-submit',

    // 初始化
    init() {
        this.initEventListeners();
        this.initDateTime();
        this.loadReceivedEvents();
    },

    // 初始化事件监听器
    initEventListeners() {
        // 主标签页切换
        document.querySelectorAll('.sidebar-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabId = e.currentTarget.dataset.tab;
                this.switchTab(tabId);
            });
        });

        // 子标签页切换
        document.querySelectorAll('.sub-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const subTabId = e.currentTarget.dataset.subtab;
                this.switchSubTab(subTabId);
            });
        });

        // 事件筛选
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const filter = e.currentTarget.dataset.filter;
                this.filterEvents(filter);
            });
        });

        // 表单提交
        document.getElementById('eventForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitEvent();
        });
    },

    // 初始化日期时间
    initDateTime() {
        const now = new Date();
        const formatDateTime = (date) => {
            return date.toISOString().slice(0, 16);
        };

        document.getElementById('reportTime').value = formatDateTime(now);
    },

    // 切换主标签页
    switchTab(tabId) {
        // 更新标签按钮状态
        document.querySelectorAll('.sidebar-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

        // 更新内容显示
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        document.getElementById(tabId).classList.add('active');

        this.currentTab = tabId;
    },

    // 切换子标签页
    switchSubTab(subTabId) {
        // 更新子标签按钮状态
        document.querySelectorAll('.sub-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-subtab="${subTabId}"]`).classList.add('active');

        // 更新子内容显示
        document.querySelectorAll('.sub-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        document.getElementById(subTabId).classList.add('active');

        this.currentSubTab = subTabId;
    },

    // 提交事件
    submitEvent() {
        const formData = new FormData(document.getElementById('eventForm'));
        const eventData = Object.fromEntries(formData);

        // 验证必填字段
        const requiredFields = ['eventTime', 'eventLocation', 'casualties', 'eventCause', 'eventType', 'impactScope', 'measures', 'forces'];
        const missingFields = requiredFields.filter(field => !eventData[field]);

        if (missingFields.length > 0) {
            this.showErrorMessage('请填写所有必填字段');
            // 高亮显示未填写的字段
            missingFields.forEach(field => {
                const element = document.getElementById(field);
                if (element) {
                    element.style.borderColor = '#dc3545';
                    element.addEventListener('input', function() {
                        this.style.borderColor = '#ddd';
                    }, { once: true });
                }
            });
            return;
        }

        // 显示上报状态
        document.getElementById('reportStatus').style.display = 'block';
        document.getElementById('reportSent').innerHTML = '✅ 已上报';
        document.getElementById('superiorConfirm').innerHTML = '⏰ 等待确认';

        // 模拟上级确认过程
        setTimeout(() => {
            document.getElementById('superiorConfirm').innerHTML = '✅ 已确认';
            document.getElementById('superiorComment').style.display = 'block';
            document.getElementById('commentText').innerHTML = '请立即启动应急响应，加强现场处置';
        }, 3000);

        this.showSuccessMessage('事件已成功上报！');
    },

    // 显示错误消息
    showErrorMessage(message) {
        // 创建错误提示
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `
            <i class="fas fa-exclamation-circle"></i>
            ${message}
        `;
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f8d7da;
            color: #721c24;
            padding: 15px 20px;
            border-radius: 6px;
            border: 1px solid #f5c6cb;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            z-index: 9999;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        `;

        document.body.appendChild(errorDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 3000);
    },

    // 重置表单
    resetForm() {
        document.getElementById('eventForm').reset();
        document.getElementById('reportStatus').style.display = 'none';
        this.initDateTime();
    },

    // 加载收到事件
    loadReceivedEvents() {
        const eventsList = document.getElementById('eventsList');
        eventsList.innerHTML = '';

        mockEvents.forEach(event => {
            const eventCard = this.createEventCard(event);
            eventsList.appendChild(eventCard);
        });
    },

    // 创建事件卡片
    createEventCard(event) {
        const card = document.createElement('div');
        card.className = 'event-card';
        card.dataset.status = event.status;

        const statusClass = `status-${event.status}`;
        const statusText = this.getStatusText(event.status);

        let responseStatusHTML = '';
        if (event.responseStatus === 'activated') {
            responseStatusHTML = `
                <div class="response-status">
                    <h5><i class="fas fa-check-circle"></i> 应急响应状态</h5>
                    <div class="response-info">
                        • 预案状态：✅ 已启动<br>
                        • 启动预案：${event.activatedPlan}<br>
                        • 启动时间：${event.activatedTime}<br>
                        • 启动人员：${event.activatedBy}<br>
                        • 响应等级：${event.responseLevel}
                    </div>
                </div>
            `;
        } else if (event.responseStatus === 'recommended') {
            responseStatusHTML = `
                <div class="response-status">
                    <h5><i class="fas fa-lightbulb"></i> 应急响应状态</h5>
                    <div class="response-info">
                        • 预案状态：💡 推荐预案<br>
                        • 推荐预案：${event.recommendedPlan}<br>
                        • 推荐等级：${event.recommendedLevel}<br>
                        • 推荐依据：${event.recommendedReason}
                    </div>
                </div>
            `;
        } else if (event.responseStatus === 'completed') {
            responseStatusHTML = `
                <div class="response-status">
                    <h5><i class="fas fa-check-circle"></i> 应急响应状态</h5>
                    <div class="response-info">
                        • 预案状态：✅ 已启动并完成<br>
                        • 启动预案：${event.activatedPlan}<br>
                        • 处置时长：${event.processingTime}<br>
                        • 处置结果：${event.result}
                    </div>
                </div>
            `;
        }

        let actionsHTML = '';
        if (event.status === 'pending') {
            actionsHTML = `
                <div class="event-actions">
                    <button class="btn btn-primary btn-sm" onclick="CommandDispatch.confirmEvent('${event.id}')">
                        <i class="fas fa-check"></i> 确认事件
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="CommandDispatch.startResponse('${event.id}')">
                        <i class="fas fa-rocket"></i> 启动响应
                    </button>
                </div>
            `;
        } else if (event.status === 'confirmed' || event.status === 'processing') {
            actionsHTML = `
                <div class="event-actions">
                    <button class="btn btn-primary btn-sm" onclick="CommandDispatch.viewDetails('${event.id}')">
                        <i class="fas fa-eye"></i> 查看详情
                    </button>
                    <button class="btn btn-success btn-sm" onclick="CommandDispatch.enterCommand('${event.id}')">
                        <i class="fas fa-headset"></i> 进入指挥
                    </button>
                </div>
            `;
        } else if (event.status === 'completed') {
            actionsHTML = `
                <div class="event-actions">
                    <button class="btn btn-primary btn-sm" onclick="CommandDispatch.viewDetails('${event.id}')">
                        <i class="fas fa-eye"></i> 查看详情
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="CommandDispatch.viewEvaluation('${event.id}')">
                        <i class="fas fa-chart-line"></i> 查看评估
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="CommandDispatch.viewSummary('${event.id}')">
                        <i class="fas fa-book"></i> 经验总结
                    </button>
                </div>
            `;
        }

        card.innerHTML = `
            <div class="event-header">
                <div>
                    <div class="event-title">${event.title}</div>
                    <div class="event-meta">
                        <span>上报：${event.reporter}</span>
                        <span>时间：${event.reportTime}</span>
                        <span>等级：${event.level}</span>
                    </div>
                </div>
                <div class="event-status ${statusClass}">${statusText}</div>
            </div>
            ${responseStatusHTML}
            ${actionsHTML}
        `;

        return card;
    },

    // 获取状态文本
    getStatusText(status) {
        const statusMap = {
            'pending': '待确认',
            'confirmed': '已确认',
            'processing': '处置中',
            'completed': '已完成'
        };
        return statusMap[status] || status;
    },

    // 筛选事件
    filterEvents(filter) {
        // 更新筛选按钮状态
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

        // 筛选事件卡片
        document.querySelectorAll('.event-card').forEach(card => {
            if (filter === 'all' || card.dataset.status === filter) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    },

    // 确认事件
    confirmEvent(eventId) {
        // 更新事件状态
        const eventIndex = mockEvents.findIndex(e => e.id === eventId);
        if (eventIndex !== -1) {
            mockEvents[eventIndex].status = 'confirmed';
        }

        // 重新加载事件列表
        this.loadReceivedEvents();

        // 显示成功消息
        this.showSuccessMessage(`事件已确认：${eventId}`);
    },

    // 启动响应
    startResponse(eventId) {
        const event = mockEvents.find(e => e.id === eventId);
        if (event) {
            this.showResponseModal(event);
        }
    },

    // 显示响应启动模态框
    showResponseModal(event) {
        const modal = document.getElementById('responseModal');
        const content = document.getElementById('responseModalContent');

        content.innerHTML = `
            <div class="response-form">
                <div class="event-info">
                    <h4>📊 事件信息</h4>
                    <p><strong>事件：</strong>${event.title}</p>
                    <p><strong>上报单位：</strong>${event.reporter}</p>
                    <p><strong>发生时间：</strong>${event.eventTime}</p>
                </div>

                <div class="plan-selection">
                    <div class="plan-header">
                        <h4>📋 预案选择</h4>
                        <button class="btn btn-info btn-sm" onclick="CommandDispatch.viewOtherPlans('${event.id}')">
                            <i class="fas fa-list"></i> 查看其他预案
                        </button>
                    </div>
                    <div class="plan-option">
                        <input type="radio" id="recommended" name="plan" value="recommended" checked>
                        <label for="recommended">
                            <strong>${event.recommendedPlan}</strong> (推荐)<br>
                            <small>适用范围：隧道火灾突发事件<br>
                            响应等级：${event.recommendedLevel}<br>
                            推荐依据：${event.recommendedReason}</small>
                        </label>
                    </div>
                </div>

                <div class="confirmation">
                    <h4>🎯 启动确认</h4>
                    <p><strong>选择预案：</strong>${event.recommendedPlan}</p>
                    <p><strong>响应等级：</strong>${event.recommendedLevel}</p>
                    <p><strong>启动人员：</strong>[当前用户] 李明华厅长</p>
                </div>

                <div class="modal-actions">
                    <button class="btn btn-primary" onclick="CommandDispatch.confirmStartResponse('${event.id}')">
                        <i class="fas fa-rocket"></i> 确认启动
                    </button>
                    <button class="btn btn-secondary" onclick="CommandDispatch.closeResponseModal()">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        `;

        modal.style.display = 'flex';
    },

    // 确认启动响应
    confirmStartResponse(eventId) {
        // 获取选中的预案
        const selectedPlan = document.querySelector('input[name="plan"]:checked').value;

        // 更新事件状态
        const eventIndex = mockEvents.findIndex(e => e.id === eventId);
        if (eventIndex !== -1) {
            mockEvents[eventIndex].status = 'processing';
            mockEvents[eventIndex].responseStatus = 'activated';
            mockEvents[eventIndex].activatedPlan = selectedPlan === 'recommended'
                ? mockEvents[eventIndex].recommendedPlan
                : '广西壮族自治区公路交通突发事件应急预案';
            mockEvents[eventIndex].activatedTime = new Date().toLocaleString('zh-CN');
            mockEvents[eventIndex].activatedBy = '李明华厅长';
            mockEvents[eventIndex].responseLevel = selectedPlan === 'recommended'
                ? mockEvents[eventIndex].recommendedLevel
                : 'Ⅱ级响应';
        }

        // 重新加载事件列表
        this.loadReceivedEvents();

        // 显示成功消息
        this.showSuccessMessage(`应急响应已启动！事件：${eventId}`);
        this.closeResponseModal();
    },

    // 显示成功消息
    showSuccessMessage(message) {
        // 创建成功提示
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.innerHTML = `
            <i class="fas fa-check-circle"></i>
            ${message}
        `;
        successDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #d4edda;
            color: #155724;
            padding: 15px 20px;
            border-radius: 6px;
            border: 1px solid #c3e6cb;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            z-index: 9999;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        `;

        document.body.appendChild(successDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 3000);
    },

    // 关闭响应模态框
    closeResponseModal() {
        document.getElementById('responseModal').style.display = 'none';
    },

    // 查看其他预案
    viewOtherPlans(eventId) {
        const modalHTML = `
            <div class="modal-overlay" onclick="CommandDispatch.closeModal()">
                <div class="modal-content" onclick="event.stopPropagation()">
                    <div class="modal-header">
                        <h3><i class="fas fa-list"></i> 应急预案库</h3>
                        <button class="modal-close" onclick="CommandDispatch.closeModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="plans-list">
                            <div class="plan-category">
                                <h4><i class="fas fa-fire"></i> 火灾事故预案</h4>
                                <div class="plan-items">
                                    <div class="plan-item">
                                        <div class="plan-name">广西隧道火灾应急预案</div>
                                        <div class="plan-info">
                                            <span class="plan-level">III级响应</span>
                                            <span class="plan-scope">隧道火灾突发事件</span>
                                        </div>
                                        <div class="plan-actions">
                                            <button class="btn btn-sm btn-primary" onclick="CommandDispatch.selectPlan('tunnel-fire', '${eventId}')">
                                                <i class="fas fa-check"></i> 选择
                                            </button>
                                            <button class="btn btn-sm btn-secondary" onclick="CommandDispatch.viewPlanDetails('tunnel-fire')">
                                                <i class="fas fa-eye"></i> 详情
                                            </button>
                                        </div>
                                    </div>
                                    <div class="plan-item">
                                        <div class="plan-name">建筑火灾应急预案</div>
                                        <div class="plan-info">
                                            <span class="plan-level">II级响应</span>
                                            <span class="plan-scope">建筑物火灾事故</span>
                                        </div>
                                        <div class="plan-actions">
                                            <button class="btn btn-sm btn-primary" onclick="CommandDispatch.selectPlan('building-fire', '${eventId}')">
                                                <i class="fas fa-check"></i> 选择
                                            </button>
                                            <button class="btn btn-sm btn-secondary" onclick="CommandDispatch.viewPlanDetails('building-fire')">
                                                <i class="fas fa-eye"></i> 详情
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="plan-category">
                                <h4><i class="fas fa-car-crash"></i> 交通事故预案</h4>
                                <div class="plan-items">
                                    <div class="plan-item">
                                        <div class="plan-name">广西壮族自治区公路交通突发事件应急预案</div>
                                        <div class="plan-info">
                                            <span class="plan-level">可调整</span>
                                            <span class="plan-scope">各类公路交通突发事件</span>
                                        </div>
                                        <div class="plan-actions">
                                            <button class="btn btn-sm btn-primary" onclick="CommandDispatch.selectPlan('traffic-general', '${eventId}')">
                                                <i class="fas fa-check"></i> 选择
                                            </button>
                                            <button class="btn btn-sm btn-secondary" onclick="CommandDispatch.viewPlanDetails('traffic-general')">
                                                <i class="fas fa-eye"></i> 详情
                                            </button>
                                        </div>
                                    </div>
                                    <div class="plan-item">
                                        <div class="plan-name">高速公路交通事故应急预案</div>
                                        <div class="plan-info">
                                            <span class="plan-level">III级响应</span>
                                            <span class="plan-scope">高速公路交通事故</span>
                                        </div>
                                        <div class="plan-actions">
                                            <button class="btn btn-sm btn-primary" onclick="CommandDispatch.selectPlan('highway-traffic', '${eventId}')">
                                                <i class="fas fa-check"></i> 选择
                                            </button>
                                            <button class="btn btn-sm btn-secondary" onclick="CommandDispatch.viewPlanDetails('highway-traffic')">
                                                <i class="fas fa-eye"></i> 详情
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="plan-category">
                                <h4><i class="fas fa-exclamation-triangle"></i> 综合应急预案</h4>
                                <div class="plan-items">
                                    <div class="plan-item">
                                        <div class="plan-name">广西壮族自治区突发事件总体应急预案</div>
                                        <div class="plan-info">
                                            <span class="plan-level">I-IV级</span>
                                            <span class="plan-scope">各类突发事件</span>
                                        </div>
                                        <div class="plan-actions">
                                            <button class="btn btn-sm btn-primary" onclick="CommandDispatch.selectPlan('general-emergency', '${eventId}')">
                                                <i class="fas fa-check"></i> 选择
                                            </button>
                                            <button class="btn btn-sm btn-secondary" onclick="CommandDispatch.viewPlanDetails('general-emergency')">
                                                <i class="fas fa-eye"></i> 详情
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="btn btn-secondary" onclick="CommandDispatch.closeModal()">
                            <i class="fas fa-times"></i> 关闭
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    },

    // 选择预案
    selectPlan(planId, eventId) {
        this.closeModal();
        this.showSuccessMessage(`已选择预案：${planId}`);
        // 这里可以添加更新预案选择的逻辑
    },

    // 查看预案详情
    viewPlanDetails(planId) {
        alert(`查看预案详情：${planId}`);
    },

    viewDetails(eventId) {
        const event = mockEvents.find(e => e.id === eventId);
        if (!event) return;

        const statusText = {
            'pending': '待确认',
            'confirmed': '已确认',
            'processing': '处理中',
            'completed': '已完成'
        };

        const levelText = {
            'I': 'I级响应',
            'II': 'II级响应',
            'III': 'III级响应',
            'IV': 'IV级响应'
        };

        const modalHTML = `
            <div class="modal-overlay" onclick="CommandDispatch.closeModal()">
                <div class="modal-content" onclick="event.stopPropagation()">
                    <div class="modal-header">
                        <h3><i class="fas fa-info-circle"></i> 事件详情</h3>
                        <button class="modal-close" onclick="CommandDispatch.closeModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="event-details">
                            <h4><i class="fas fa-exclamation-triangle"></i> ${event.title}</h4>

                            <div class="detail-section">
                                <h5><i class="fas fa-info"></i> 基本信息</h5>
                                <div class="detail-grid">
                                    <div class="detail-item">
                                        <div class="label">事件编号</div>
                                        <div class="value">${event.id}</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="label">上报单位</div>
                                        <div class="value">${event.reporter}</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="label">发生时间</div>
                                        <div class="value">${event.eventTime || event.time}</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="label">事件状态</div>
                                        <div class="value status ${event.status}">${statusText[event.status]}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="detail-section">
                                <h5><i class="fas fa-map-marker-alt"></i> 位置信息</h5>
                                <div class="detail-grid">
                                    <div class="detail-item">
                                        <div class="label">发生地点</div>
                                        <div class="value">${event.location || '桂林隧道火灾事故现场'}</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="label">影响范围</div>
                                        <div class="value">${event.scope || '隧道及周边道路'}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="detail-section">
                                <h5><i class="fas fa-users"></i> 伤亡情况</h5>
                                <div class="detail-grid">
                                    <div class="detail-item">
                                        <div class="label">人员伤亡</div>
                                        <div class="value">${event.casualties || '3人轻伤，无死亡'}</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="label">财产损失</div>
                                        <div class="value">${event.damage || '车辆损毁，隧道设施受损'}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="detail-section">
                                <h5><i class="fas fa-clipboard-list"></i> 响应情况</h5>
                                <div class="detail-grid">
                                    <div class="detail-item">
                                        <div class="label">响应等级</div>
                                        <div class="value">${levelText[event.level] || 'III级响应'}</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="label">启动预案</div>
                                        <div class="value">${event.plan || '广西隧道火灾应急预案'}</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="label">预计恢复时间</div>
                                        <div class="value">${event.recoveryTime || '6小时'}</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="label">已投入力量</div>
                                        <div class="value">${event.forces || '消防2队，交警1队，医疗1队'}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="detail-section">
                                <div class="detail-description">
                                    <div class="label">事件描述</div>
                                    <div class="value">${event.description || '桂林隧道内发生车辆火灾事故，现场浓烟较大，已启动应急预案。消防部门正在现场处置，交警部门实施交通管制，医疗部门待命救治伤员。目前火势已得到控制，正在进行现场清理和通风作业。'}</div>
                                </div>
                            </div>

                            <div class="detail-section">
                                <div class="detail-description">
                                    <div class="label">已采取措施</div>
                                    <div class="value">${event.measures || '1. 立即启动应急预案；2. 消防部门现场灭火；3. 交警实施交通管制；4. 医疗部门救治伤员；5. 现场指挥部已建立；6. 正在进行隧道通风和清理作业。'}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="btn btn-secondary" onclick="CommandDispatch.closeModal()">
                            <i class="fas fa-times"></i> 关闭
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    },

    enterCommand(eventId) {
        // 跳转到现场指挥标签页
        this.switchTab('field-command');
        this.showSuccessMessage(`已进入事件 ${eventId} 的现场指挥`);
    },

    viewEvaluation(eventId) {
        alert(`查看评估：${eventId}`);
    },

    viewSummary(eventId) {
        alert(`经验总结：${eventId}`);
    },

    consultExpert(eventId) {
        alert(`咨询专家：${eventId}`);
    },

    closeModal() {
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            modal.remove();
        }
    }
};

// 全局函数
function resetForm() {
    CommandDispatch.resetForm();
}

function closeResponseModal() {
    CommandDispatch.closeResponseModal();
}

// 现场指挥协调功能
const FieldCommand = {
    // 初始化
    init() {
        this.initSubTabs();
        this.initContactSections();
        this.initRecording();
    },

    // 初始化子标签页
    initSubTabs() {
        const subTabs = document.querySelectorAll('.sub-tab[data-subtab]');
        subTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const targetTab = e.currentTarget.dataset.subtab;
                this.switchSubTab(targetTab);
            });
        });
    },

    // 切换子标签页
    switchSubTab(tabName) {
        // 更新标签页状态
        document.querySelectorAll('.sub-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-subtab="${tabName}"]`).classList.add('active');

        // 更新内容区域
        document.querySelectorAll('.sub-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');
    },

    // 初始化联系人分组
    initContactSections() {
        // 默认展开第一个分组
        const firstSection = document.querySelector('.contact-list');
        if (firstSection) {
            firstSection.style.display = 'block';
        }
    },

    // 切换联系人分组
    toggleGroup(groupId) {
        const group = document.getElementById(groupId);
        const icon = event.currentTarget.querySelector('.toggle-icon');

        if (group.style.display === 'none' || !group.style.display) {
            group.style.display = 'block';
            icon.classList.add('rotated');
        } else {
            group.style.display = 'none';
            icon.classList.remove('rotated');
        }
    },

    // 视频会商功能
    startConference() {
        this.showSuccessMessage('正在启动视频会商...');
        // 模拟启动会商
        setTimeout(() => {
            this.updateConferenceStatus('active');
            this.showSuccessMessage('视频会商已启动！');
        }, 2000);
    },

    joinConference() {
        this.showSuccessMessage('正在加入会商...');
        // 模拟加入会商
        setTimeout(() => {
            this.updateParticipantStatus('online');
            this.showSuccessMessage('已成功加入会商！');
        }, 1500);
    },

    updateConferenceStatus(status) {
        const mainVideo = document.querySelector('.main-video .video-status');
        const videoPlaceholder = document.querySelector('.main-video .video-placeholder');

        if (status === 'active') {
            mainVideo.textContent = '在线';
            mainVideo.style.color = '#27ae60';
            videoPlaceholder.innerHTML = `
                <i class="fas fa-video"></i>
                <p>会商进行中...</p>
            `;
        }
    },

    updateParticipantStatus(status) {
        const participantCount = document.querySelector('.participants-list h4');
        if (status === 'online') {
            participantCount.textContent = '参会人员 (4/8)';

            // 更新参与者状态
            const participants = document.querySelectorAll('.participant-status');
            participants.forEach((status, index) => {
                if (index < 4) {
                    status.classList.remove('offline');
                    status.classList.add('online');
                }
            });

            // 更新视频网格中的状态
            const videoStatuses = document.querySelectorAll('.participant-info .status');
            videoStatuses.forEach((status, index) => {
                if (index < 4) {
                    status.textContent = '在线';
                    status.classList.remove('offline');
                    status.classList.add('online');
                }
            });
        }
    },

    // 会商工具功能
    toggleMute() {
        this.showSuccessMessage('麦克风状态已切换');
    },

    toggleVideo() {
        this.showSuccessMessage('视频状态已切换');
    },

    shareScreen() {
        this.showSuccessMessage('屏幕共享已启动');
    },

    // 通讯联系功能
    makeCall(phone, name) {
        this.showSuccessMessage(`正在呼叫 ${name} (${phone})...`);
        // 模拟呼叫过程
        setTimeout(() => {
            this.showSuccessMessage(`与 ${name} 通话已接通`);
        }, 2000);
    },

    // AI录音功能
    initRecording() {
        this.isRecording = false;
        this.recordingTime = 0;
        this.recordingTimer = null;
    },

    startRecording() {
        if (this.isRecording) return;

        this.isRecording = true;
        this.recordingTime = 0;

        // 更新按钮状态
        document.getElementById('recordBtn').disabled = true;
        document.getElementById('pauseBtn').disabled = false;
        document.getElementById('stopBtn').disabled = false;

        // 开始计时
        this.recordingTimer = setInterval(() => {
            this.recordingTime++;
            this.updateRecordingTime();
            this.simulateTranscription();
        }, 1000);

        this.showSuccessMessage('AI录音已开始');
    },

    pauseRecording() {
        if (!this.isRecording) return;

        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
            document.getElementById('pauseBtn').innerHTML = '<i class="fas fa-play"></i> 继续';
            this.showSuccessMessage('录音已暂停');
        } else {
            this.recordingTimer = setInterval(() => {
                this.recordingTime++;
                this.updateRecordingTime();
                this.simulateTranscription();
            }, 1000);
            document.getElementById('pauseBtn').innerHTML = '<i class="fas fa-pause"></i> 暂停';
            this.showSuccessMessage('录音已继续');
        }
    },

    stopRecording() {
        if (!this.isRecording) return;

        this.isRecording = false;

        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
        }

        // 更新按钮状态
        document.getElementById('recordBtn').disabled = false;
        document.getElementById('pauseBtn').disabled = true;
        document.getElementById('stopBtn').disabled = true;
        document.getElementById('pauseBtn').innerHTML = '<i class="fas fa-pause"></i> 暂停';

        this.showSuccessMessage('录音已停止，正在生成完整纪要...');

        // 模拟生成完整纪要
        setTimeout(() => {
            this.generateFinalSummary();
        }, 2000);
    },

    updateRecordingTime() {
        const minutes = Math.floor(this.recordingTime / 60);
        const seconds = this.recordingTime % 60;
        const timeStr = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        const timeDisplay = document.querySelector('.recording-time');
        if (timeDisplay) {
            timeDisplay.textContent = `00:${timeStr}`;
        }
    },

    simulateTranscription() {
        // 模拟实时转录
        if (this.recordingTime % 10 === 0) {
            this.addTranscriptionItem();
        }

        // 模拟音频电平
        this.updateAudioLevel();
    },

    addTranscriptionItem() {
        const speakers = ['张德国副厅长', '赵安全主任', '陈军组长', '王医生'];
        const contents = [
            '根据现场反馈，交通管制措施已经到位。',
            '医疗救护队伍准备就绪，随时待命。',
            '请各部门加强信息沟通，确保协调一致。',
            '伤员救治情况良好，无生命危险。'
        ];

        const speaker = speakers[Math.floor(Math.random() * speakers.length)];
        const content = contents[Math.floor(Math.random() * contents.length)];
        const time = new Date().toLocaleTimeString('zh-CN', { hour12: false, hour: '2-digit', minute: '2-digit' });

        const transcriptionDisplay = document.getElementById('transcriptionDisplay');
        const currentItem = transcriptionDisplay.querySelector('.transcription-item.current');

        if (currentItem) {
            currentItem.classList.remove('current');
        }

        const newItem = document.createElement('div');
        newItem.className = 'transcription-item';
        newItem.innerHTML = `
            <span class="speaker">${speaker}</span>
            <span class="time">${time}</span>
            <div class="content">${content}</div>
        `;

        transcriptionDisplay.appendChild(newItem);
        transcriptionDisplay.scrollTop = transcriptionDisplay.scrollHeight;

        // 更新AI纪要
        this.updateAISummary();
    },

    updateAudioLevel() {
        const levelBars = document.querySelectorAll('.level-bar');
        levelBars.forEach((bar, index) => {
            bar.classList.remove('active');
            if (Math.random() > 0.5 && index < 3) {
                bar.classList.add('active');
            }
        });
    },

    updateAISummary() {
        // 模拟AI实时更新纪要内容
        const summaryContent = document.getElementById('summaryContent');
        const participantCount = document.querySelectorAll('.transcription-item').length;

        // 更新参会人员信息
        const basicInfo = summaryContent.querySelector('.summary-section:first-child p:last-child');
        if (basicInfo) {
            basicInfo.innerHTML = `<strong>参会人员：</strong>张德国、王强、李队长、赵安全等 (${participantCount}人发言)`;
        }
    },

    generateFinalSummary() {
        const summaryContent = document.getElementById('summaryContent');
        summaryContent.innerHTML = `
            <div class="summary-section">
                <h5>📋 会议基本信息</h5>
                <p><strong>会议时间：</strong>2024-12-19 15:42-16:15 (33分钟)</p>
                <p><strong>主持人：</strong>李明华 (自治区交通运输厅厅长)</p>
                <p><strong>参会人员：</strong>张德国、王强、李队长、赵安全、陈军、王医生等</p>
            </div>

            <div class="summary-section">
                <h5>🚨 现场情况汇报</h5>
                <ul>
                    <li>事故涉及5辆车辆相撞，现场清障基本完成</li>
                    <li>3人轻伤，已送医治疗，生命体征稳定</li>
                    <li>交通管制措施到位，绕行方案执行顺利</li>
                    <li>消防救援力量协助清障，现场安全可控</li>
                </ul>
            </div>

            <div class="summary-section">
                <h5>🤝 部门协调分工</h5>
                <ul>
                    <li>应急指挥组：负责现场统一指挥，技术指导到位</li>
                    <li>消防救援：协助清障作业，确保现场安全</li>
                    <li>医疗救护：伤员救治及时，医疗保障充分</li>
                    <li>交警部门：交通管制有效，绕行组织有序</li>
                </ul>
            </div>

            <div class="summary-section">
                <h5>📝 主要决策事项</h5>
                <ul>
                    <li>启动Ⅱ级应急响应，成立现场指挥部</li>
                    <li>优先完成人员救援，确保生命安全</li>
                    <li>组织交通绕行，减少拥堵影响</li>
                    <li>加强信息发布，及时通报处置进展</li>
                </ul>
            </div>

            <div class="summary-section">
                <h5>⏰ 下次汇报安排</h5>
                <p><strong>汇报时间：</strong>16:30</p>
                <p><strong>汇报内容：</strong>各工作组汇报最新进展情况</p>
            </div>
        `;

        this.showSuccessMessage('AI智能纪要生成完成！');
    },

    // 纪要编辑功能
    editSummary() {
        this.showSuccessMessage('正在打开纪要编辑器...');
        // 这里可以打开一个富文本编辑器
    },

    exportSummary() {
        this.showSuccessMessage('会议纪要已导出到本地');
        // 这里可以实现导出功能
    },

    // 音频文件功能
    playAudio(fileId) {
        this.showSuccessMessage('正在播放录音文件...');
    },

    downloadAudio(fileId) {
        this.showSuccessMessage('录音文件下载已开始');
    },

    // 显示成功消息
    showSuccessMessage(message) {
        // 创建成功提示
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.innerHTML = `
            <i class="fas fa-check-circle"></i>
            ${message}
        `;
        successDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #d4edda;
            color: #155724;
            padding: 15px 20px;
            border-radius: 6px;
            border: 1px solid #c3e6cb;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            z-index: 9999;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        `;

        document.body.appendChild(successDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 3000);
    }
};

// ==================== 事件回溯分析模块 ====================
const EventReview = {
    // 查看事件详情
    viewEventDetail(eventId) {
        const eventData = this.getEventData(eventId);
        this.showEventDetailModal(eventData);
    },

    // 获取事件数据（模拟数据）
    getEventData(eventId) {
        // 模拟详细的事件数据
        const eventDataMap = {
            'event-001': {
                id: 'event-001',
                title: '泉南高速吴家屯隧道山体塌方事故',
                type: '交通事故',
                level: 'Ⅱ级',
                eventTime: '2023-04-20 10:03',
                reportTime: '2023-04-20 10:08',
                location: '泉南高速广西桂林至柳州段改扩建工程吴家屯隧道出口（柳州端）',
                reporter: '广西壮族自治区交通运输厅',
                casualties: {
                    total: 10,
                    dead: 0,
                    injured: 10,
                    missing: 0,
                    trapped: 10,
                    details: '10人受伤被困车内（轿车和客车各4人，厢式货车2人），均已救出送医治疗'
                },
                vehicleDamage: {
                    scrapped: 1,
                    major: 3,
                    minor: 0,
                    estimatedLoss: '约45万元',
                    details: '1辆槽罐车报废，1辆客车大修，1辆厢式货车大修，1辆小轿车大修'
                },
                roadImpact: {
                    closureTime: '6小时15分钟',
                    affectedVehicles: '约1200辆',
                    detourDistance: '25公里',
                    economicLoss: '约15万元',
                    details: '桂林至柳州方向高速公路交通中断，车辆需绕行省道，造成大面积拥堵'
                },
                totalLoss: '直接经济损失约68万元，间接损失约15万元，总计约83万元',
                description: '因多日连续强降雨导致山体塌方，槽罐车撞上塌方体后引发连环追尾，造成10人受伤被困，粗苯泄漏',
                cause: '多日连续强降雨导致隧道出口上方山体塌方，槽罐车避让不及撞上塌方体，后车连环追尾',
                impact: '桂林至柳州方向高速公路交通中断6.25小时，影响车辆约1200辆，造成拥堵15公里',
                weather: '连续强降雨，路面湿滑，能见度较差',
                economicLoss: 680000,
                processingTime: '6小时15分钟',
                responseLevel: 'Ⅱ级应急响应',
                status: '已完成',
                vehicles: '4辆车（1辆装载33t粗苯的槽罐车，1辆4.5t厢式货车，1辆载客30人客车，1辆小轿车）',
                roadCondition: '湿滑，有塌方体阻挡',
                trafficFlow: '中等，事故发生时车流密集',
                emergencyFacilities: '隧道内有应急电话、监控设备，但塌方影响部分设施',
                rescueConditions: '救援通道受阻，需清理塌方体后进入',
                timeline: [
                    { time: '10:03', event: '山体塌方发生，槽罐车撞上塌方体，连环追尾事故发生' },
                    { time: '10:08', event: '接到报警，启动Ⅱ级应急响应' },
                    { time: '10:15', event: '消防救援队伍到达现场' },
                    { time: '10:25', event: '医疗救护队伍到达，开始救治被困人员' },
                    { time: '10:45', event: '危化品处置专家到达，开始处置粗苯泄漏' },
                    { time: '11:30', event: '10名被困人员全部救出送医' },
                    { time: '13:15', event: '粗苯泄漏处置完毕，现场安全' },
                    { time: '14:30', event: '事故车辆清理完毕，开始清理塌方体' },
                    { time: '16:18', event: '道路恢复通行，应急响应结束' }
                ],
                keyDecisions: [
                    {
                        time: '10:08',
                        decision: '启动Ⅱ级应急响应',
                        decisionMaker: '李明华厅长',
                        basis: '山体塌方引发连环追尾，有人员被困，危化品泄漏',
                        execution: '立即启动，各部门快速响应',
                        effect: '有效'
                    },
                    {
                        time: '10:15',
                        decision: '优先救治被困人员',
                        decisionMaker: '现场指挥部',
                        basis: '保障人员生命安全是第一要务',
                        execution: '消防、医疗队伍同时到场救援',
                        effect: '有效'
                    },
                    {
                        time: '10:20',
                        decision: '启动危化品应急处置',
                        decisionMaker: '应急指挥中心',
                        basis: '槽罐车粗苯泄漏，存在环境污染和安全风险',
                        execution: '调派危化品处置专家和设备',
                        effect: '有效'
                    },
                    {
                        time: '10:30',
                        decision: '实施交通管制和疏导',
                        decisionMaker: '交警部门',
                        basis: '确保救援通道畅通，减少交通影响',
                        execution: '设置绕行路线，引导车辆分流',
                        effect: '有效'
                    }
                ],
                meetingRecords: [
                    {
                        id: 'meeting-001',
                        title: '山体塌方事故应急处置会议',
                        time: '10:20-11:10',
                        duration: '50分钟',
                        participants: '李明华厅长、张德国副厅长、王强组长、现场指挥员、危化品专家',
                        keyPoints: [
                            '确认山体塌方规模和连环追尾事故情况',
                            '调配救援力量、医疗资源和危化品处置设备',
                            '制定人员救援和危化品泄漏处置方案',
                            '协调消防、医疗、环保、交警等部门分工配合',
                            '建立现场安全防护区域，防止二次事故'
                        ]
                    }
                ],
                successExperiences: [
                    '应急响应速度快，5分钟内启动Ⅱ级响应',
                    '多部门协调配合良好，消防、医疗、环保、交警分工明确',
                    '危化品处置专业及时，有效控制了泄漏扩散',
                    '人员救援高效，10名被困人员全部安全救出',
                    '信息报送及时准确，决策科学合理',
                    '现场安全防护到位，未发生二次事故'
                ],
                existingProblems: [
                    '山体地质灾害监测预警不够及时',
                    '隧道出口地质灾害防护设施不完善',
                    '危化品运输车辆安全监管需要加强',
                    '恶劣天气条件下交通管制措施不够完善',
                    '应急物资储备点布局需要优化'
                ],
                improvements: [
                    '建立完善的地质灾害监测预警系统，加强雨季巡查',
                    '加强隧道出口等重点区域地质灾害防护工程建设',
                    '强化危化品运输车辆安全监管和应急处置能力',
                    '完善恶劣天气条件下的交通管制和疏导预案',
                    '优化应急物资储备布局，提高快速响应能力',
                    '加强多部门联合演练，提升协同作战能力'
                ]
            },
            'event-002': {
                id: 'event-002',
                title: '服务区餐厅厨房火灾',
                type: '火灾事故',
                level: 'Ⅲ级',
                eventTime: '2024-01-10 11:20',
                reportTime: '2024-01-10 11:25',
                location: '阳澄湖服务区',
                reporter: '服务区管理处',
                casualties: {
                    total: 0,
                    dead: 0,
                    injured: 0,
                    missing: 0,
                    trapped: 0,
                    details: '无人员伤亡'
                },
                vehicleDamage: {
                    scrapped: 0,
                    major: 0,
                    minor: 0,
                    estimatedLoss: '无',
                    details: '无车辆损失'
                },
                roadImpact: {
                    closureTime: '无',
                    affectedVehicles: '约50辆',
                    detourDistance: '无',
                    economicLoss: '约0.2万元',
                    details: '餐厅暂停营业，部分旅客需到其他服务区用餐'
                },
                totalLoss: '直接经济损失约3万元，间接损失约0.2万元，总计约3.2万元',
                description: '服务区餐厅厨房油烟机起火，火势较小，及时扑灭',
                cause: '厨房油烟机长期使用，油污积累过多，电路老化引发火灾',
                impact: '餐厅暂停营业1天，经济损失约3万元',
                weather: '晴天，无风',
                economicLoss: 30000,
                processingTime: '1小时15分钟',
                responseLevel: 'Ⅲ级应急响应',
                status: '已完成',
                timeline: [
                    { time: '11:20', event: '厨房起火，工作人员发现' },
                    { time: '11:22', event: '启动内部消防系统' },
                    { time: '11:25', event: '报告管理处，启动应急响应' },
                    { time: '11:30', event: '消防队到达现场' },
                    { time: '11:35', event: '火势扑灭，现场安全' },
                    { time: '12:35', event: '现场清理完毕，应急响应结束' }
                ],
                keyDecisions: [
                    {
                        time: '11:25',
                        decision: '启动Ⅲ级应急响应',
                        decisionMaker: '服务区主任',
                        basis: '火灾规模较小，但涉及公共场所',
                        execution: '立即启动内部应急程序',
                        effect: '有效'
                    },
                    {
                        time: '11:26',
                        decision: '疏散餐厅人员',
                        decisionMaker: '安全员',
                        basis: '确保人员安全',
                        execution: '有序疏散，无人员伤亡',
                        effect: '有效'
                    }
                ],
                meetingRecords: [],
                successExperiences: [
                    '内部消防系统发挥作用，及时控制火势',
                    '员工应急处置得当，疏散有序',
                    '与外部消防部门配合良好'
                ],
                existingProblems: [
                    '设备维护不及时，存在安全隐患',
                    '安全检查存在盲区',
                    '员工安全培训需要加强'
                ],
                improvements: [
                    '加强设备定期维护，建立完善的保养制度',
                    '完善安全检查制度，消除安全盲区',
                    '定期开展员工安全培训和应急演练',
                    '升级消防设备，提高自动化水平'
                ]
            }
        };

        return eventDataMap[eventId] || {
            id: eventId,
            title: '未知事件',
            type: '其他',
            level: 'Ⅳ级',
            status: '未知',
            casualties: { total: 0, dead: 0, injured: 0, missing: 0, trapped: 0, details: '无数据' },
            vehicleDamage: { scrapped: 0, major: 0, minor: 0, estimatedLoss: '无', details: '无数据' },
            roadImpact: { closureTime: '无', affectedVehicles: '无', detourDistance: '无', economicLoss: '无', details: '无数据' },
            totalLoss: '无数据',
            timeline: [],
            keyDecisions: [],
            meetingRecords: [],
            successExperiences: [],
            existingProblems: [],
            improvements: []
        };
    },

    // 显示事件详情模态框
    showEventDetailModal(eventData) {
        if (!eventData) return;

        const modalHTML = `
            <div class="modal-overlay" onclick="EventReview.closeModal()">
                <div class="modal-content event-detail-modal" onclick="event.stopPropagation()">
                    <div class="modal-header">
                        <h3><i class="fas fa-history"></i> ${eventData.title} - 回溯分析</h3>
                        <button class="close-button" onclick="EventReview.closeModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="event-detail-content">
                            ${this.generateBasicInfoSection(eventData)}
                            ${this.generateEventDetailsSection(eventData)}
                            ${this.generateResultsSection(eventData)}
                            ${this.generateTimelineSection(eventData)}
                            ${this.generateDecisionsSection(eventData)}
                            ${this.generateMeetingRecordsSection(eventData)}
                            ${this.generateLessonsSection(eventData)}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="EventReview.closeModal()">关闭</button>
                        <button class="btn btn-primary" onclick="EventReview.exportReport('${eventData.id}')">
                            <i class="fas fa-download"></i> 导出报告
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    },

    // 生成基本信息部分
    generateBasicInfoSection(eventData) {
        return `
            <div class="detail-section">
                <h4><i class="fas fa-info-circle"></i> 事件基本信息</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <label>事件名称：</label>
                        <span>${eventData.title}</span>
                    </div>
                    <div class="info-item">
                        <label>事件类型：</label>
                        <span>${eventData.type}</span>
                    </div>
                    <div class="info-item">
                        <label>发生时间：</label>
                        <span>${eventData.eventTime}</span>
                    </div>
                    <div class="info-item">
                        <label>接报时间：</label>
                        <span>${eventData.reportTime}</span>
                    </div>
                    <div class="info-item">
                        <label>处置时长：</label>
                        <span>${eventData.processingTime}</span>
                    </div>
                    <div class="info-item">
                        <label>响应等级：</label>
                        <span class="level-badge level-${eventData.level.replace('Ⅰ', '1').replace('Ⅱ', '2').replace('Ⅲ', '3').replace('Ⅳ', '4')}">${eventData.level}</span>
                    </div>
                    <div class="info-item full-width">
                        <label>事发地点：</label>
                        <span>${eventData.location}</span>
                    </div>
                    <div class="info-item full-width">
                        <label>最终结果：</label>
                        <span>应急处置成功，人员安全，道路恢复通行</span>
                    </div>
                </div>
            </div>
        `;
    },

    // 生成事件详情部分
    generateEventDetailsSection(eventData) {
        return `
            <div class="detail-section">
                <h4><i class="fas fa-exclamation-triangle"></i> 事件详情</h4>

                <div class="result-subsection">
                    <h5><i class="fas fa-car-crash"></i> 事故概况</h5>
                    <div class="info-grid">
                        <div class="info-item full-width">
                            <label>事故原因：</label>
                            <span>${eventData.cause}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>天气情况：</label>
                            <span>${eventData.weather}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>涉及车辆：</label>
                            <span>${eventData.vehicles}</span>
                        </div>
                    </div>
                </div>

                <div class="result-subsection">
                    <h5><i class="fas fa-road"></i> 事发环境</h5>
                    <div class="info-grid">
                        <div class="info-item full-width">
                            <label>路面状况：</label>
                            <span>${eventData.roadCondition}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>交通流量：</label>
                            <span>${eventData.trafficFlow}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>应急设施：</label>
                            <span>${eventData.emergencyFacilities}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>救援条件：</label>
                            <span>${eventData.rescueConditions}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    // 生成处置结果部分
    generateResultsSection(eventData) {
        return `
            <div class="detail-section">
                <h4><i class="fas fa-chart-bar"></i> 最终处置结果</h4>

                <div class="result-subsection">
                    <h5><i class="fas fa-user-injured"></i> 人员伤亡情况</h5>
                    <div class="casualties-grid">
                        <div class="casualty-item">
                            <span class="casualty-number">${eventData.casualties.dead}</span>
                            <span class="casualty-label">死亡</span>
                        </div>
                        <div class="casualty-item">
                            <span class="casualty-number">${eventData.casualties.injured}</span>
                            <span class="casualty-label">受伤</span>
                        </div>
                        <div class="casualty-item">
                            <span class="casualty-number">${eventData.casualties.missing}</span>
                            <span class="casualty-label">失踪</span>
                        </div>
                        <div class="casualty-item">
                            <span class="casualty-number">${eventData.casualties.trapped}</span>
                            <span class="casualty-label">被困</span>
                        </div>
                        <div class="casualty-item">
                            <span class="casualty-number">${eventData.casualties.total}</span>
                            <span class="casualty-label">涉及总人数</span>
                        </div>
                    </div>
                    <div class="casualty-details">
                        <p><strong>详细情况：</strong>${eventData.casualties.details}</p>
                    </div>
                </div>

                <div class="result-subsection">
                    <h5><i class="fas fa-car-crash"></i> 车辆损失情况</h5>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>报废车辆：</label>
                            <span>${eventData.vehicleDamage.scrapped}辆</span>
                        </div>
                        <div class="info-item">
                            <label>大修车辆：</label>
                            <span>${eventData.vehicleDamage.major}辆</span>
                        </div>
                        <div class="info-item">
                            <label>轻微损坏：</label>
                            <span>${eventData.vehicleDamage.minor}辆</span>
                        </div>
                        <div class="info-item">
                            <label>预估损失：</label>
                            <span>${eventData.vehicleDamage.estimatedLoss}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>损失详情：</label>
                            <span>${eventData.vehicleDamage.details}</span>
                        </div>
                    </div>
                </div>

                <div class="result-subsection">
                    <h5><i class="fas fa-road"></i> 道路影响情况</h5>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>封闭时长：</label>
                            <span>${eventData.roadImpact.closureTime}</span>
                        </div>
                        <div class="info-item">
                            <label>影响车辆：</label>
                            <span>${eventData.roadImpact.affectedVehicles}</span>
                        </div>
                        <div class="info-item">
                            <label>绕行距离：</label>
                            <span>${eventData.roadImpact.detourDistance}</span>
                        </div>
                        <div class="info-item">
                            <label>经济损失：</label>
                            <span>${eventData.roadImpact.economicLoss}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>影响详情：</label>
                            <span>${eventData.roadImpact.details}</span>
                        </div>
                    </div>
                </div>

                <div class="result-subsection">
                    <h5><i class="fas fa-calculator"></i> 总体损失评估</h5>
                    <div class="total-loss">
                        <p>${eventData.totalLoss}</p>
                    </div>
                </div>
            </div>
        `;
    },

    // 生成时间轴部分
    generateTimelineSection(eventData) {
        return `
            <div class="detail-section">
                <h4><i class="fas fa-clock"></i> 处置时间轴记录</h4>
                <div class="timeline">
                    ${eventData.timeline.map(item => `
                        <div class="timeline-item">
                            <div class="timeline-time">${item.time}</div>
                            <div class="timeline-content">${item.event}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    },

    // 生成关键决策部分
    generateDecisionsSection(eventData) {
        return `
            <div class="detail-section">
                <h4><i class="fas fa-gavel"></i> 关键决策回顾</h4>
                ${eventData.keyDecisions.map(decision => `
                    <div class="result-subsection">
                        <h5><i class="fas fa-lightbulb"></i> ${decision.time} - ${decision.decision}</h5>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>决策人：</label>
                                <span>${decision.decisionMaker}</span>
                            </div>
                            <div class="info-item">
                                <label>决策依据：</label>
                                <span>${decision.basis}</span>
                            </div>
                            <div class="info-item">
                                <label>执行情况：</label>
                                <span>${decision.execution}</span>
                            </div>
                            <div class="info-item">
                                <label>效果评价：</label>
                                <span class="decision-effect">${decision.effect}</span>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    },

    // 生成会议记录部分
    generateMeetingRecordsSection(eventData) {
        if (!eventData.meetingRecords || eventData.meetingRecords.length === 0) {
            return `
                <div class="detail-section">
                    <h4><i class="fas fa-microphone"></i> AI会议记录回顾</h4>
                    <p class="no-data">本次事件处置过程中无正式会议记录</p>
                </div>
            `;
        }

        return `
            <div class="detail-section">
                <h4><i class="fas fa-microphone"></i> AI会议记录回顾</h4>
                ${eventData.meetingRecords.map(meeting => `
                    <div class="result-subsection">
                        <h5><i class="fas fa-users"></i> ${meeting.title}</h5>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>会议时间：</label>
                                <span>${meeting.time}</span>
                            </div>
                            <div class="info-item">
                                <label>会议时长：</label>
                                <span>${meeting.duration}</span>
                            </div>
                            <div class="info-item full-width">
                                <label>参会人员：</label>
                                <span>${meeting.participants}</span>
                            </div>
                        </div>
                        <div class="meeting-content">
                            <div class="meeting-points">
                                <strong>关键要点：</strong>
                                <ul>
                                    ${meeting.keyPoints.map(point => `<li>${point}</li>`).join('')}
                                </ul>
                            </div>
                            <div class="meeting-audio">
                                <button class="btn btn-sm btn-secondary" onclick="EventReview.playMeetingAudio('${meeting.id}')">
                                    <i class="fas fa-play"></i> 播放录音
                                </button>
                                <button class="btn btn-sm btn-secondary" onclick="EventReview.downloadMeetingAudio('${meeting.id}')">
                                    <i class="fas fa-download"></i> 下载录音
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    },

    // 生成经验教训部分
    generateLessonsSection(eventData) {
        return `
            <div class="detail-section">
                <h4><i class="fas fa-lightbulb"></i> 经验教训总结</h4>

                <div class="result-subsection">
                    <h5><i class="fas fa-check-circle"></i> 成功经验</h5>
                    <ul class="lessons-list">
                        ${eventData.successExperiences.map(experience => `<li>${experience}</li>`).join('')}
                    </ul>
                </div>

                <div class="result-subsection">
                    <h5><i class="fas fa-exclamation-triangle"></i> 存在问题</h5>
                    <ul class="problems-list">
                        ${eventData.existingProblems.map(problem => `<li>${problem}</li>`).join('')}
                    </ul>
                </div>

                <div class="result-subsection">
                    <h5><i class="fas fa-arrow-up"></i> 改进建议</h5>
                    <ul class="improvements-list">
                        ${eventData.improvements.map(improvement => `<li>${improvement}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;
    },

    // 播放会议录音
    playMeetingAudio(meetingId) {
        CommandDispatch.showSuccessMessage('正在播放会议录音...');
    },

    // 下载会议录音
    downloadMeetingAudio(meetingId) {
        CommandDispatch.showSuccessMessage('会议录音下载已开始');
    },

    // 导出报告
    exportReport(eventId) {
        CommandDispatch.showSuccessMessage('事件回溯分析报告导出成功！');
    },

    // 关闭模态框
    closeModal() {
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            modal.remove();
        }
    }
};

// 应急评估模块
const EmergencyEvaluation = {
    // 初始化
    init() {
        this.initEventSelect();
    },

    // 初始化事件选择
    initEventSelect() {
        const eventSelect = document.getElementById('evaluationEventSelect');
        if (eventSelect) {
            eventSelect.addEventListener('change', () => {
                this.loadEventEvaluation();
            });

            // 自动选择第一个事件并显示内容
            if (eventSelect.options.length > 1) {
                eventSelect.selectedIndex = 1; // 选择第一个实际事件（跳过"请选择"选项）
                this.loadEventEvaluation();
            }
        }
    },

    // 加载事件评估
    loadEventEvaluation() {
        const eventSelect = document.getElementById('evaluationEventSelect');
        const selectedEventId = eventSelect.value;

        if (selectedEventId) {
            // 显示评估内容
            const evaluationContent = document.querySelector('.evaluation-main-content');
            if (evaluationContent) {
                evaluationContent.style.display = 'flex';
            }

            // 隐藏空状态
            const noEvaluation = document.querySelector('.no-evaluation-selected');
            if (noEvaluation) {
                noEvaluation.style.display = 'none';
            }

            // 更新基本信息
            this.updateBasicInfo(selectedEventId);

            CommandDispatch.showSuccessMessage(`已加载事件评估：${eventSelect.options[eventSelect.selectedIndex].text}`);
        }
    },

    // 更新基本信息
    updateBasicInfo(eventId) {
        // 从EventReview模块获取事件数据
        const eventData = EventReview.getEventData(eventId);
        if (!eventData) return;

        // 更新基本信息网格中的内容
        const basicInfoGrid = document.querySelector('.basic-info-grid');
        if (basicInfoGrid) {
            basicInfoGrid.innerHTML = `
                <div class="info-item">
                    <label>事件名称</label>
                    <span>${eventData.title}</span>
                </div>
                <div class="info-item">
                    <label>评估时间</label>
                    <span>2023-04-20 18:30</span>
                </div>
                <div class="info-item">
                    <label>评估人员</label>
                    <span>张德国副厅长</span>
                </div>
                <div class="info-item">
                    <label>处置时长</label>
                    <span>${eventData.processingTime}</span>
                </div>
                <div class="info-item">
                    <label>响应等级</label>
                    <span>${eventData.responseLevel}</span>
                </div>
                <div class="info-item">
                    <label>最终结果</label>
                    <span>成功处置，10名被困人员全部救出，粗苯泄漏得到有效控制，道路恢复通行</span>
                </div>
            `;
        }
    },

    // 创建新评估
    createNewEvaluation() {
        CommandDispatch.showSuccessMessage('创建新评估功能开发中...');
    },

    // 保存草稿
    saveDraft() {
        CommandDispatch.showSuccessMessage('评估草稿已保存！');
    },

    // 提交评估
    submitEvaluation() {
        // 简单的表单验证
        const requiredFields = ['responseSpeed', 'processingEffect', 'coordination', 'resourceAllocation', 'mainProblems', 'improvements', 'overallScore'];
        let isValid = true;

        for (let field of requiredFields) {
            const element = document.getElementById(field);
            if (element && !element.value.trim()) {
                isValid = false;
                element.style.borderColor = '#dc3545';
                break;
            } else if (element) {
                element.style.borderColor = '#dee2e6';
            }
        }

        if (isValid) {
            CommandDispatch.showSuccessMessage('应急评估报告已提交！');
        } else {
            CommandDispatch.showErrorMessage('请填写所有必填字段！');
        }
    },

    // 生成AI建议
    generateSuggestions() {
        CommandDispatch.showSuccessMessage('AI正在分析评估内容，生成预案修改建议...');

        // 模拟AI生成过程
        setTimeout(() => {
            CommandDispatch.showSuccessMessage('AI预案修改建议已生成！');
        }, 2000);
    },

    // 编辑建议
    editSuggestion(suggestionId) {
        CommandDispatch.showSuccessMessage(`编辑建议 ${suggestionId}`);
    },

    // 采纳建议
    applySuggestion(suggestionId) {
        CommandDispatch.showSuccessMessage(`已采纳建议 ${suggestionId}`);
    },

    // 提交AI建议
    submitSuggestions() {
        CommandDispatch.showSuccessMessage('AI预案修改建议已提交至预案管理部门！');
    },

    // 编辑建议（新的统一编辑功能）
    editSuggestions() {
        const textarea = document.getElementById('suggestionText');
        if (textarea) {
            if (textarea.readOnly) {
                textarea.readOnly = false;
                textarea.style.background = 'white';
                textarea.style.borderColor = '#0056b3';
                CommandDispatch.showSuccessMessage('现在可以编辑预案修改建议了');
            } else {
                textarea.readOnly = true;
                textarea.style.background = '#f8f9fa';
                textarea.style.borderColor = '#e9ecef';
                CommandDispatch.showSuccessMessage('预案修改建议已保存');
            }
        }
    },

    // 采纳建议（新的统一采纳功能）
    adoptSuggestions() {
        const textarea = document.getElementById('suggestionText');
        if (textarea && textarea.value.trim()) {
            CommandDispatch.showSuccessMessage('预案修改建议已采纳并提交至预案管理部门！');
        } else {
            CommandDispatch.showErrorMessage('请先生成或编辑预案修改建议');
        }
    },

    // 预览报告
    previewReport() {
        CommandDispatch.showSuccessMessage('预览评估报告功能开发中...');
    },

    // 导出评估
    exportEvaluation() {
        CommandDispatch.showSuccessMessage('评估报告导出成功！');
    }
};