/**
 * 顶部导航栏组件
 * 用于生成统一的导航栏，支持动态设置当前激活页面
 */

// 导航栏配置
const NAVIGATION_CONFIG = {
    systemTitle: '广西交通运输应急管理系统',
    pages: [
        {
            id: 'risk-map',
            title: '风险一张图',
            url: 'risk-map.html'
        },
        {
            id: 'emergency-map',
            title: '应急一张图',
            url: 'emergency-map.html'
        },
        {
            id: 'road-network',
            title: '路网运行',
            url: 'road-network.html'
        },
        {
            id: 'flood-prevention',
            title: '防汛防台',
            url: 'flood-prevention.html'
        },
        {
            id: 'emergency-drill',
            title: '应急演练',
            url: 'emergency-drill.html'
        },
        {
            id: 'command-dispatch',
            title: '指挥调度',
            url: 'command-dispatch.html'
        },
        {
            id: 'monitoring-warning',
            title: '监测预警',
            url: 'monitoring-warning.html'
        },
        {
            id: 'duty-watch',
            title: '值班值守',
            url: 'duty-watch.html'
        },
        {
            id: 'analysis-judgment',
            title: '分析研判',
            url: 'analysis-judgment.html'
        },
        {
            id: 'situation-plotting',
            title: '态势标绘',
            url: 'situation-plotting.html'
        },
        {
            id: 'system-management',
            title: '系统管理',
            url: 'system-management.html'
        }

    ]
};

/**
 * 创建导航栏HTML
 * @param {string} activePage - 当前激活的页面ID
 * @returns {string} 导航栏HTML字符串
 */
function createNavigationHTML(activePage) {
    const buttonsHTML = NAVIGATION_CONFIG.pages.map(page => {
        const isActive = page.id === activePage;
        return `<button class="tab-button ${isActive ? 'active' : ''}"
                        data-page="${page.id}"
                        onclick="navigateToPage('${page.url}')">${page.title}</button>`;
    }).join('\n                ');

    return `
        <header class="top-nav">
            <div class="system-title">${NAVIGATION_CONFIG.systemTitle}</div>
            <nav class="tab-navigation">
                ${buttonsHTML}
            </nav>
        </header>
    `;
}

/**
 * 渲染导航栏到指定容器
 * @param {string} containerId - 容器元素ID
 * @param {string} activePage - 当前激活的页面ID
 */
function renderNavigation(containerId, activePage) {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = createNavigationHTML(activePage);
    } else {
        console.error(`Navigation container with ID "${containerId}" not found`);
    }
}

/**
 * 初始化导航栏
 * @param {string} activePage - 当前激活的页面ID
 * @param {string} containerId - 容器元素ID，默认为'navigation-container'
 */
function initNavigation(activePage, containerId = 'navigation-container') {
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            renderNavigation(containerId, activePage);
        });
    } else {
        renderNavigation(containerId, activePage);
    }
}

/**
 * 页面导航函数
 * @param {string} url - 目标页面URL
 */
function navigateToPage(url) {
    // 检查是否是当前页面
    if (window.location.pathname.endsWith(url) ||
        (window.location.pathname === '/' && url === 'index.html')) {
        return; // 如果是当前页面，不进行跳转
    }

    // 跳转到目标页面
    window.location.href = url;
}

/**
 * 添加新的导航页面
 * @param {Object} pageConfig - 页面配置对象 {id, title, url}
 */
function addNavigationPage(pageConfig) {
    if (!pageConfig.id || !pageConfig.title || !pageConfig.url) {
        console.error('Invalid page configuration. Required: id, title, url');
        return;
    }

    // 检查是否已存在
    const existingPage = NAVIGATION_CONFIG.pages.find(page => page.id === pageConfig.id);
    if (existingPage) {
        console.warn(`Page with ID "${pageConfig.id}" already exists`);
        return;
    }

    NAVIGATION_CONFIG.pages.push(pageConfig);
    console.log(`Added new navigation page: ${pageConfig.title}`);
}

/**
 * 移除导航页面
 * @param {string} pageId - 页面ID
 */
function removeNavigationPage(pageId) {
    const index = NAVIGATION_CONFIG.pages.findIndex(page => page.id === pageId);
    if (index > -1) {
        const removedPage = NAVIGATION_CONFIG.pages.splice(index, 1)[0];
        console.log(`Removed navigation page: ${removedPage.title}`);
    } else {
        console.warn(`Page with ID "${pageId}" not found`);
    }
}

/**
 * 获取当前页面ID（根据URL自动判断）
 * @returns {string} 当前页面ID
 */
function getCurrentPageId() {
    const currentPath = window.location.pathname;
    const currentFile = currentPath.split('/').pop() || 'index.html';

    // 根据文件名匹配页面ID
    const currentPage = NAVIGATION_CONFIG.pages.find(page =>
        currentFile === page.url ||
        (currentFile === '' && page.url === 'index.html')
    );

    return currentPage ? currentPage.id : NAVIGATION_CONFIG.pages[0].id;
}

// 导出函数供全局使用
window.NavigationComponent = {
    init: initNavigation,
    render: renderNavigation,
    addPage: addNavigationPage,
    removePage: removeNavigationPage,
    getCurrentPageId: getCurrentPageId,
    navigateToPage: navigateToPage
};
