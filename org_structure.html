<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组织架构管理 - 应急管理系统</title>
    <!-- 引入样式 -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- 引入 OrgChart.js -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/orgchart/3.1.1/css/jquery.orgchart.min.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/unified_header.css">
    <style>
        html, body {
            height: 100%;
            margin: 0;
        }
        body {
            font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
        }
        /* Removed old layout styles below */
        /* .sidebar-menu-item { ... } */
        /* .main-content { ... } */
        /* .sidebar { ... } */
        /* body.sidebar-expanded ... */
        /* @media (max-width: 768px) { ... } */

        /* 组织架构树样式 */
        .organization-tree {
            padding: 20px;
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .tree-container {
            margin-top: 1rem;
        }

        .tree-node {
            margin-bottom: 0.5rem;
        }

        .node-content {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 0.375rem;
            border: 1px solid #e5e7eb;
            background-color: #f9fafb;
            position: relative;
        }

        .node-content:hover {
            background-color: #f3f4f6;
        }

        .node-content i {
            margin-right: 0.5rem;
            color: #4b5563;
        }

        .node-content span {
            flex-grow: 1;
            font-size: 0.875rem;
            font-weight: 500;
            color: #1f2937;
        }

        .node-actions {
            display: flex;
            gap: 0.5rem;
        }

        .node-actions button {
            background: none;
            border: none;
            cursor: pointer;
            color: #6b7280;
            padding: 0.25rem;
            border-radius: 0.25rem;
            transition: background-color 0.2s;
        }

        .node-actions button:hover {
            background-color: #e5e7eb;
            color: #4b5563;
        }

        .child-nodes {
            margin-left: 2rem;
            margin-top: 0.5rem;
            padding-left: 1rem;
            border-left: 1px dashed #d1d5db;
        }

        .root-node > .node-content {
            background-color: #e0e7ff;
            border-color: #c7d2fe;
        }

        .root-node > .node-content i {
            color: #4f46e5;
        }

        /* OrgChart 自定义样式 */
        #orgChart {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            height: calc(100vh - 250px);
            overflow: auto;
        }

        .orgchart {
            background: #fff;
        }

        .orgchart .node {
            width: 180px;
            padding: 8px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .orgchart .node .title {
            background-color: #4f46e5;
            color: #fff;
            padding: 8px;
            border-radius: 4px 4px 0 0;
            font-weight: 500;
        }

        .orgchart .node .content {
            padding: 8px;
            border: 1px solid #e5e7eb;
            border-top: none;
            border-radius: 0 0 4px 4px;
            background: #f9fafb;
        }

        /* Element Plus 树形选择器样式 */
        .el-tree-select {
            width: 100% !important;
        }

        /* 简洁的树形结构样式 */
        .org-tree {
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .tree-node {
            margin: 8px 0;
            padding-left: 24px;
            position: relative;
        }

        .tree-node::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            width: 16px;
            height: 1px;
            background: #d1d5db;
        }

        .tree-node::after {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 50%;
            width: 1px;
            background: #d1d5db;
        }

        .tree-node:last-child::after {
            bottom: auto;
            height: 50%;
        }

        .node-content {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            margin-bottom: 4px;
        }

        .node-content:hover {
            background: #f3f4f6;
        }

        .node-title {
            flex: 1;
            font-size: 14px;
            color: #374151;
        }

        .node-actions {
            display: flex;
            gap: 8px;
        }

        .node-actions button {
            padding: 4px;
            color: #6b7280;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .node-actions button:hover {
            background: #e5e7eb;
            color: #374151;
        }

        .children {
            position: relative;
            padding-left: 24px;
        }

        .children::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 12px;
            width: 1px;
            background: #d1d5db;
        }

        .root-node {
            padding-left: 0;
        }

        .root-node::before,
        .root-node::after {
            display: none;
        }

        .root-node > .node-content {
            background: #e0e7ff;
            border-color: #c7d2fe;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
    <!-- Removed old header -->

    <!-- Removed old aside -->

    <!-- Navbar Start -->
    <header class="top-nav">
        <div class="system-title">
            <strong>广西公路水路安全畅通与应急处置系统</strong>
        </div>
        <nav class="tab-navigation">
            <a href="new_index.html" class="tab-button">风险一张图</a>
            <a href="my_check_tasks.html" class="tab-button">风险隐患管理</a>
            <a href="plan_list.html" class="tab-button active">应急处置</a>
            <button class="tab-button" onclick="alert('应急演练功能暂未开放');">应急演练</button>
        </nav>
    </header>
    <!-- Navbar End -->

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container h-full" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area -->
        <!-- Removed old main wrapper, ensure content is inside the new main -->
        <main id="main-content" class="flex-grow pb-8 px-6 bg-gray-100">
        <div class="py-6">
            <!-- 页面标题 -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-bold text-gray-800">组织架构管理</h2>
                    <p class="text-gray-600 mt-1">管理应急组织和部门结构</p>
                </div>
                <div>
                    <button id="btnAddOrg" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <i class="fas fa-plus mr-2"></i> 新增组织
                    </button>
                </div>
            </div>

            <!-- 简洁的组织树形结构 -->
            <div class="org-tree">
                <div class="tree-node root-node">
                    <div class="node-content">
                        <span class="node-title">广西壮族自治区交通运输厅</span>
                        <div class="node-actions">
                            <button title="添加子组织"><i class="fas fa-plus"></i></button>
                            <button title="编辑"><i class="fas fa-edit"></i></button>
                        </div>
                    </div>
                    <div class="children">
                        <!-- 一级部门 -->
                        <div class="tree-node">
                            <div class="node-content">
                                <span class="node-title">直属事业单位及专项机构</span>
                                <div class="node-actions">
                                    <button title="添加子组织"><i class="fas fa-plus"></i></button>
                                    <button title="编辑"><i class="fas fa-edit"></i></button>
                                    <button title="删除"><i class="fas fa-trash"></i></button>
                                </div>
                            </div>
                            <div class="children">
                                <!-- 二级部门 -->
                                <div class="tree-node">
                                    <div class="node-content">
                                        <span class="node-title">自治区公路发展中心</span>
                                        <div class="node-actions">
                                            <button title="添加子组织"><i class="fas fa-plus"></i></button>
                                            <button title="编辑"><i class="fas fa-edit"></i></button>
                                            <button title="删除"><i class="fas fa-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="tree-node">
                                    <div class="node-content">
                                        <span class="node-title">自治区高速公路发展中心</span>
                                        <div class="node-actions">
                                            <button title="添加子组织"><i class="fas fa-plus"></i></button>
                                            <button title="编辑"><i class="fas fa-edit"></i></button>
                                            <button title="删除"><i class="fas fa-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="tree-node">
                                    <div class="node-content">
                                        <span class="node-title">自治区道路运输发展中心</span>
                                        <div class="node-actions">
                                            <button title="添加子组织"><i class="fas fa-plus"></i></button>
                                            <button title="编辑"><i class="fas fa-edit"></i></button>
                                            <button title="删除"><i class="fas fa-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 一级部门 -->
                        <div class="tree-node">
                            <div class="node-content">
                                <span class="node-title">市级交通运输局</span>
                                <div class="node-actions">
                                    <button title="添加子组织"><i class="fas fa-plus"></i></button>
                                    <button title="编辑"><i class="fas fa-edit"></i></button>
                                    <button title="删除"><i class="fas fa-trash"></i></button>
                                </div>
                            </div>
                            <div class="children">
                                <div class="tree-node">
                                    <div class="node-content">
                                        <span class="node-title">钦州市交通运输局</span>
                                        <div class="node-actions">
                                            <button title="添加子组织"><i class="fas fa-plus"></i></button>
                                            <button title="编辑"><i class="fas fa-edit"></i></button>
                                            <button title="删除"><i class="fas fa-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="tree-node">
                                    <div class="node-content">
                                        <span class="node-title">南宁市交通运输局</span>
                                        <div class="node-actions">
                                            <button title="添加子组织"><i class="fas fa-plus"></i></button>
                                            <button title="编辑"><i class="fas fa-edit"></i></button>
                                            <button title="删除"><i class="fas fa-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <div class="tree-node">
                                    <div class="node-content">
                                        <span class="node-title">玉林市交通运输局</span>
                                        <div class="node-actions">
                                            <button title="添加子组织"><i class="fas fa-plus"></i></button>
                                            <button title="编辑"><i class="fas fa-edit"></i></button>
                                            <button title="删除"><i class="fas fa-trash"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    </div> <!-- Close flex-container -->

    <!-- Modals should be outside the main flex container -->
    <!-- 组织添加/编辑模态框 -->
    <div class="fixed inset-0 z-[100] hidden" id="orgModal"> <!-- Increased z-index -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
        <div class="fixed inset-0 z-10 overflow-y-auto">
            <div class="flex min-h-full items-center justify-center p-4">
                <div class="bg-white rounded-lg shadow-xl w-full max-w-md transform transition-all">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900" id="modalTitle">新增组织</h3>
                    </div>
                    <div class="px-6 py-4">
                        <form id="orgForm">
                            <div class="mb-4">
                                <label for="orgName" class="block text-sm font-medium text-gray-700 mb-1">组织名称 <span class="text-red-500">*</span></label>
                                <input type="text" id="orgName" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                            </div>
                            <div class="mb-4">
                                <label for="orgParent" class="block text-sm font-medium text-gray-700 mb-1">上级组织</label>
                                <div id="parentSelector">
                                    <el-tree-select
                                        v-model="selectedParent"
                                        :data="orgOptions"
                                        default-expand-all
                                        check-strictly
                                        :props="{
                                            children: 'children',
                                            label: 'label',
                                            value: 'id'
                                        }"
                                        placeholder="请选择上级组织"
                                        class="block w-full"
                                    />
                                </div>
                            </div>
                            <div class="mb-4">
                                <label for="orgDescription" class="block text-sm font-medium text-gray-700 mb-1">职责描述</label>
                                <textarea id="orgDescription" rows="4" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200 flex justify-end">
                        <button id="btnCancel" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2">
                            取消
                        </button>
                        <button id="btnSave" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            保存
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="fixed inset-0 z-[100] hidden" id="deleteModal"> <!-- Increased z-index -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
        <div class="fixed inset-0 z-10 overflow-y-auto">
            <div class="flex min-h-full items-center justify-center p-4">
                <div class="bg-white rounded-lg shadow-xl w-full max-w-md transform transition-all">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">确认删除</h3>
                    </div>
                    <div class="px-6 py-4">
                        <p class="text-gray-700">确定要删除此组织及其下属组织吗？此操作无法撤销。</p>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200 flex justify-end">
                        <button id="btnDeleteCancel" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2">
                            取消
                        </button>
                        <button id="btnDeleteConfirm" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                            确认删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Load Libraries First -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>

    <!-- Page Specific Logic -->
    <script>
        // Vue 应用 - 用于树形选择器
        const app = Vue.createApp({
            data() {
                return {
                    selectedParent: '',
                    orgOptions: [{
                        id: '1',
                        label: '广西壮族自治区交通运输厅',
                        children: [{
                            id: '1.1',
                            label: '直属事业单位及专项机构',
                            children: [{
                                id: '1.1.1',
                                label: '自治区公路发展中心'
                            }, {
                                id: '1.1.2',
                                label: '自治区高速公路发展中心'
                            }, {
                                id: '1.1.3',
                                label: '自治区道路运输发展中心'
                            }]
                        }, {
                            id: '1.2',
                            label: '市级交通运输局',
                            children: [{
                                id: '1.2.1',
                                label: '钦州市交通运输局'
                            }, {
                                id: '1.2.2',
                                label: '南宁市交通运输局'
                            }, {
                                id: '1.2.3',
                                label: '玉林市交通运输局'
                            }]
                        }]
                    }]
                }
            }
        }).use(ElementPlus)

        app.mount('#parentSelector')

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // Removed old sidebar toggle logic

            // 模态框相关操作
            const orgModal = document.getElementById('orgModal');
            const deleteModal = document.getElementById('deleteModal');
            const btnAddOrg = document.getElementById('btnAddOrg');
            const btnCancel = document.getElementById('btnCancel');
            const btnSave = document.getElementById('btnSave');
            const btnDeleteCancel = document.getElementById('btnDeleteCancel');
            const btnDeleteConfirm = document.getElementById('btnDeleteConfirm');

            // 打开新增组织模态框
            btnAddOrg.addEventListener('click', function() {
                document.getElementById('modalTitle').textContent = '新增组织';
                document.getElementById('orgForm').reset();
                orgModal.classList.remove('hidden');
            });

            // 关闭模态框
            btnCancel.addEventListener('click', function() {
                orgModal.classList.add('hidden');
            });

            btnDeleteCancel.addEventListener('click', function() {
                deleteModal.classList.add('hidden');
            });

            // 绑定树节点的操作按钮事件
            document.querySelectorAll('.node-actions button').forEach(button => {
                button.addEventListener('click', function(e) {
                    const action = this.getAttribute('title');
                    const node = this.closest('.node-content');
                    const orgName = node.querySelector('.node-title').textContent;

                    if (action === '添加子组织') {
                        document.getElementById('modalTitle').textContent = '新增子组织';
                        document.getElementById('orgForm').reset();
                        orgModal.classList.remove('hidden');
                    } else if (action === '编辑') {
                        document.getElementById('modalTitle').textContent = '编辑组织';
                        document.getElementById('orgName').value = orgName;
                        orgModal.classList.remove('hidden');
                    } else if (action === '删除') {
                        deleteModal.classList.remove('hidden');
                    }
                    e.stopPropagation();
                });
            });

            // 保存按钮点击事件
            btnSave.addEventListener('click', function() {
                const orgName = document.getElementById('orgName').value;
                if (!orgName) {
                    alert('请输入组织名称');
                    return;
                }
                // 这里添加保存逻辑
                alert('保存成功');
                orgModal.classList.add('hidden');
            });

            // 确认删除按钮点击事件
            btnDeleteConfirm.addEventListener('click', function() {
                // 这里添加删除逻辑
                alert('删除成功');
                deleteModal.classList.add('hidden');
            });
        });
    </script>

    <!-- Load component HTML first -->
    <script src="js/navbarComponent.js"></script>
    <script src="js/sidebarComponent_emergency.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>
</body>
</html>