<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检查下发与模版管理 - 应急管理系统</title>
    <!-- 引入样式 -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/unified_header.css">
    <!-- 添加 Element Plus 样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
         html, body { /* Added for full height */
            height: 100%;
            margin: 0;
        }
         body {
            font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
            /* margin: 0; - Handled above */
        }
        /* Removed old .tab-button styles */

        /* Tab content display logic (Keep as is) */
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
         /* Tree Select/Table Styles (保持不变) */
         .el-tree-select { width: 100% !important; }
         .el-select-dropdown__wrap { max-height: 400px; }
         .el-tree-node__content { height: 32px; }
         .el-tree-node__label { font-size: 14px; }
         .el-table__row--level-1 .el-table__expand-icon { margin-left: 20px; }
    </style>
</head>
<body class="bg-gray-100 flex flex-col min-h-screen"> <!-- Added classes -->

    <!-- Navbar Start -->
    <header class="top-nav">
        <div class="system-title">
            <strong>广西公路水路安全畅通与应急处置系统</strong>
        </div>
        <nav class="tab-navigation">
            <a href="new_index.html" class="tab-button">风险一张图</a>
            <a href="my_check_tasks.html" class="tab-button active">风险隐患管理</a>
            <a href="plan_list.html" class="tab-button">应急处置</a>
            <button class="tab-button" onclick="alert('应急演练功能暂未开放');">应急演练</button>
        </nav>
    </header>
    <!-- Navbar End -->

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container h-full" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area -->
        <main id="main-content" class="flex-grow p-6 bg-gray-100 min-h-screen">
            <!-- Add py-6 wrapper like plan_list.html -->
            <div class="py-6">
                <!-- Page Title Section - Placed inside py-6 div -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800">检查下发与模版管理</h2>
                        <p class="text-gray-600 mt-1">下发检查任务并管理检查模版</p>
                    </div>
                    <div>
                        <!-- Placeholder for potential future buttons -->
                    </div>
                </div>

                <!-- Tab Navigation and Content - Moved inside py-6 div -->
                <div class="bg-white rounded-t-lg shadow-sm mb-0">
                    <div class="p-4 sm:px-6">
                        <nav class="flex space-x-4 overflow-x-auto pb-1">
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600 focus:outline-none active" data-tab="dispatch">
                                检查下发
                            </button>
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="template">
                                检查模版
                            </button>
                        </nav>
                    </div>
                </div>

                <div class="bg-white rounded-b-lg shadow-md pt-4">
                    <!-- 检查下发 Tab -->
                    <div id="dispatchTab" class="tab-content active px-6 pb-6">
                        <div class="flex justify-between items-center mb-4">
                           <h3 class="text-xl font-semibold text-gray-800">检查任务列表</h3>
                           <button id="btnDispatchCheck" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                               <i class="fas fa-paper-plane mr-2"></i> 下发检查任务
                           </button>
                       </div>
                       <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                           <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查名称</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查类别</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务单位</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">截止时间</th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">任务进度</th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                        </tr>
                                    </thead>
                                     <tbody class="bg-white divide-y divide-gray-200">
                                         <!-- Example dispatched check row 1 (In Progress) -->
                                         <tr class="hover:bg-gray-50 task-row" data-status="进行中">
                                             <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                             <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 task-name">2024年第三季度风险路段专项检查</td>
                                             <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">风险路段</td>
                                             <td class="px-6 py-4 text-sm text-gray-500 truncate max-w-xs" title="南宁市交通运输局, 钦州市交通运输局">南宁市交通运输局, 钦州市交通运输局</td>
                                             <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-09-30 17:00</td>
                                             <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-700 font-medium task-progress">50%</td>
                                             <td class="px-6 py-4 whitespace-nowrap text-center text-sm task-status"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">进行中</span></td>
                                             <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                 <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-progress" title="查看进度" data-id="task1"><i class="fas fa-chart-bar"></i></button>
                                                 <button class="text-yellow-600 hover:text-yellow-800 focus:outline-none mr-2 btn-urge" title="催办" data-id="task1"><i class="fas fa-bell"></i></button>
                                                 <!-- <button class="text-red-600 hover:text-red-800 focus:outline-none" title="撤销"><i class="fas fa-undo"></i></button> -->
                                             </td>
                                         </tr>
                                         <!-- Example dispatched check row 2 (Completed) -->
                                         <tr class="hover:bg-gray-50 task-row" data-status="已完成">
                                             <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                             <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 task-name">汛期前桥梁安全检查</td>
                                             <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">涉灾隐患点-桥梁</td>
                                             <td class="px-6 py-4 text-sm text-gray-500 truncate max-w-xs" title="自治区公路发展中心, 自治区高速公路发展中心">自治区公路发展中心, ...</td>
                                             <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-05-31 17:00</td>
                                             <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-green-600 font-medium task-progress">100%</td>
                                             <td class="px-6 py-4 whitespace-nowrap text-center text-sm task-status"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已完成</span></td>
                                             <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                 <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-progress" title="查看结果" data-id="task2"><i class="fas fa-clipboard-list"></i></button>
                                             </td>
                                         </tr>
                                     </tbody>
                                </table>
                           </div>
                            <!-- Pagination for dispatched checks -->
                            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                                <!-- Pagination content here -->
                                <p class="text-sm text-gray-700">分页占位符</p>
                            </div>
                       </div>
                    </div>

                    <!-- 检查模版 Tab -->
                    <div id="templateTab" class="tab-content px-6 pb-6">
                        <div class="mb-8">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-xl font-semibold text-gray-800">检查类别列表</h3>
                                <button id="btnAddCategory" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                    <i class="fas fa-plus mr-2"></i> 添加检查类别
                                </button>
                            </div>
                            <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                         <thead class="bg-gray-50">
                                             <tr>
                                                 <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类别名称</th>
                                                 <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">层级</th>
                                                 <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                             </tr>
                                         </thead>
                                         <tbody class="bg-white divide-y divide-gray-200">
                                             <!-- Category rows - Restore -->
                                              <tr class="hover:bg-gray-50 font-semibold">
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">风险路段</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">一级</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                      <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-category" data-id="cat1" data-name="风险路段" data-parent=""><i class="fas fa-edit"></i> 编辑</button>
                                                      <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-category" data-id="cat1"><i class="fas fa-trash-alt"></i> 删除</button>
                                                  </td>
                                              </tr>
                                              <tr class="hover:bg-gray-50">
                                                  <td class="pl-12 pr-6 py-4 whitespace-nowrap text-sm text-gray-900">山洪淹没区风险路段</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">二级</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                      <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-category" data-id="cat1.1" data-name="山洪淹没区风险路段" data-parent="cat1"><i class="fas fa-edit"></i> 编辑</button>
                                                      <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-category" data-id="cat1.1"><i class="fas fa-trash-alt"></i> 删除</button>
                                                  </td>
                                              </tr>
                                               <tr class="hover:bg-gray-50">
                                                  <td class="pl-12 pr-6 py-4 whitespace-nowrap text-sm text-gray-900">地质灾害风险路段</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">二级</td>
                                                   <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                      <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-category" data-id="cat1.2" data-name="地质灾害风险路段" data-parent="cat1"><i class="fas fa-edit"></i> 编辑</button>
                                                      <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-category" data-id="cat1.2"><i class="fas fa-trash-alt"></i> 删除</button>
                                                  </td>
                                              </tr>
                                               <tr class="hover:bg-gray-50 font-semibold">
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">工作管理机制隐患</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">一级</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                       <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-category" data-id="cat2" data-name="工作管理机制隐患" data-parent=""><i class="fas fa-edit"></i> 编辑</button>
                                                      <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-category" data-id="cat2"><i class="fas fa-trash-alt"></i> 删除</button>
                                                  </td>
                                              </tr>
                                              <tr class="hover:bg-gray-50 font-semibold">
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">基础保障设施隐患</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">一级</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                      <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-category" data-id="cat3" data-name="基础保障设施隐患" data-parent=""><i class="fas fa-edit"></i> 编辑</button>
                                                      <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-category" data-id="cat3"><i class="fas fa-trash-alt"></i> 删除</button>
                                                  </td>
                                              </tr>
                                              <tr class="hover:bg-gray-50">
                                                  <td class="pl-12 pr-6 py-4 whitespace-nowrap text-sm text-gray-900">防洪标识</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">二级</td>
                                                   <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                       <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-category" data-id="cat3.1" data-name="防洪标识" data-parent="cat3"><i class="fas fa-edit"></i> 编辑</button>
                                                      <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-category" data-id="cat3.1"><i class="fas fa-trash-alt"></i> 删除</button>
                                                  </td>
                                              </tr>
                                         </tbody>
                                     </table>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-xl font-semibold text-gray-800">填报项列表</h3>
                                <button id="btnAddField" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                    <i class="fas fa-plus mr-2"></i> 添加填报项
                                </button>
                            </div>
                            <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                         <thead class="bg-gray-50">
                                             <tr>
                                                 <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">字段名称</th>
                                                 <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">填写方式</th>
                                                 <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">逻辑说明</th>
                                                 <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                             </tr>
                                         </thead>
                                         <tbody class="bg-white divide-y divide-gray-200">
                                             <!-- Field rows - Restore -->
                                              <tr class="hover:bg-gray-50">
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">检查类别</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">下拉选择</td>
                                                  <td class="px-6 py-4 text-sm text-gray-500">主分类+子分类，如"风险路段-山洪淹没区"</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                      <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field1"><i class="fas fa-edit"></i> 编辑</button>
                                                      <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field1"><i class="fas fa-trash-alt"></i> 删除</button>
                                                  </td>
                                              </tr>
                                              <tr class="hover:bg-gray-50">
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市、区/县名称</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">下拉选择</td>
                                                  <td class="px-6 py-4 text-sm text-gray-500">系统自动关联登录账号组织机构所在地</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                      <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field2"><i class="fas fa-edit"></i> 编辑</button>
                                                      <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field2"><i class="fas fa-trash-alt"></i> 删除</button>
                                                  </td>
                                              </tr>
                                              <tr class="hover:bg-gray-50">
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">所属单位</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">下拉选择</td>
                                                  <td class="px-6 py-4 text-sm text-gray-500">系统自动关联登录账号组织机构</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                      <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field3"><i class="fas fa-edit"></i> 编辑</button>
                                                      <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field3"><i class="fas fa-trash-alt"></i> 删除</button>
                                                  </td>
                                              </tr>
                                              <tr class="hover:bg-gray-50">
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">隐患位置</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">文本输入</td>
                                                  <td class="px-6 py-4 text-sm text-gray-500">输入详细地址/路段桩号等</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                      <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field4"><i class="fas fa-edit"></i> 编辑</button>
                                                      <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field4"><i class="fas fa-trash-alt"></i> 删除</button>
                                                  </td>
                                              </tr>
                                              <tr class="hover:bg-gray-50">
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">隐患描述</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">文本域输入</td>
                                                  <td class="px-6 py-4 text-sm text-gray-500">详细描述隐患情况</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                      <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field5"><i class="fas fa-edit"></i> 编辑</button>
                                                      <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field5"><i class="fas fa-trash-alt"></i> 删除</button>
                                                  </td>
                                              </tr>
                                              <tr class="hover:bg-gray-50">
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">风险等级</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">下拉选择</td>
                                                  <td class="px-6 py-4 text-sm text-gray-500">高/中/低</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                      <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field6"><i class="fas fa-edit"></i> 编辑</button>
                                                      <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field6"><i class="fas fa-trash-alt"></i> 删除</button>
                                                  </td>
                                              </tr>
                                              <tr class="hover:bg-gray-50">
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">检查日期</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">日期选择</td>
                                                  <td class="px-6 py-4 text-sm text-gray-500">选择检查发生的日期</td>
                                                   <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                      <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field7"><i class="fas fa-edit"></i> 编辑</button>
                                                      <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field7"><i class="fas fa-trash-alt"></i> 删除</button>
                                                  </td>
                                              </tr>
                                              <tr class="hover:bg-gray-50">
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">现场照片</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">图片上传</td>
                                                  <td class="px-6 py-4 text-sm text-gray-500">可上传多张照片</td>
                                                   <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                      <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field8"><i class="fas fa-edit"></i> 编辑</button>
                                                      <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field8"><i class="fas fa-trash-alt"></i> 删除</button>
                                                  </td>
                                              </tr>
                                              <tr class="hover:bg-gray-50">
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">整改建议</td>
                                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">文本域输入</td>
                                                  <td class="px-6 py-4 text-sm text-gray-500">输入初步的整改建议（可选）</td>
                                                   <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                      <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field9"><i class="fas fa-edit"></i> 编辑</button>
                                                      <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field9"><i class="fas fa-trash-alt"></i> 删除</button>
                                                  </td>
                                              </tr>
                                         </tbody>
                                     </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 添加/编辑类别 Modal (Element Plus Dialog) -->
    <div id="categoryModalApp">
        <el-dialog
            v-model="dialogVisible"
            :title="isEditMode ? '编辑检查类别' : '添加检查类别'"
            width="40%"
            @closed="resetForm"
            :close-on-click-modal="false"
        >
            <el-form :model="categoryForm" ref="categoryFormRef" label-width="100px">
                 <el-form-item label="上级类别">
                    <el-tree-select
                        v-model="categoryForm.parentId"
                        :data="categoryTreeData"
                        check-strictly
                        :props="{ value: 'id', label: 'name', children: 'children' }"
                        placeholder="选择上级类别（可选，一级类别不选）"
                    />
                </el-form-item>
                <el-form-item label="类别名称" required prop="name">
                    <el-input v-model="categoryForm.name" placeholder="请输入类别名称"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                </span>
            </template>
        </el-dialog>
    </div>

     <!-- 添加/编辑填报项 Modal -->
    <div id="fieldModalApp">
        <el-dialog
            v-model="dialogVisible"
            :title="isEditMode ? '编辑填报项' : '添加填报项'"
            width="50%"
            @closed="resetForm"
            :close-on-click-modal="false"
        >
            <el-form :model="fieldForm" ref="fieldFormRef" label-width="100px">
                <el-form-item label="字段名称" required prop="name">
                    <el-input v-model="fieldForm.name" placeholder="请输入字段名称"></el-input>
                </el-form-item>
                <el-form-item label="填写方式" required prop="type">
                    <el-select v-model="fieldForm.type" placeholder="请选择填写方式">
                        <el-option label="文本输入" value="text"></el-option>
                        <el-option label="文本域输入" value="textarea"></el-option>
                        <el-option label="下拉选择" value="select"></el-option>
                        <el-option label="多选框" value="checkbox"></el-option>
                        <el-option label="单选框" value="radio"></el-option>
                        <el-option label="日期选择" value="date"></el-option>
                        <el-option label="日期时间选择" value="datetime"></el-option>
                        <el-option label="图片上传" value="image"></el-option>
                        <el-option label="文件上传" value="file"></el-option>
                        <!-- Add more types as needed -->
                    </el-select>
                </el-form-item>
                 <el-form-item v-if="['select', 'checkbox', 'radio'].includes(fieldForm.type)" label="选项内容" prop="options">
                     <el-input type="textarea" v-model="fieldForm.options" :rows="3" placeholder="请输入选项，每行一个"></el-input>
                     <div class="text-xs text-gray-500">如果填写方式是下拉、多选或单选，请在此处输入选项，每行一个。</div>
                 </el-form-item>
                 <el-form-item label="是否必填" prop="required">
                     <el-switch v-model="fieldForm.required"></el-switch>
                 </el-form-item>
                 <el-form-item label="逻辑说明" prop="description">
                    <el-input type="textarea" v-model="fieldForm.description" :rows="2" placeholder="请输入字段相关的逻辑说明（可选）"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                </span>
            </template>
        </el-dialog>
    </div>

    <!-- 下发检查任务 Modal -->
    <div id="dispatchModalApp">
        <el-dialog
            v-model="dialogVisible"
            title="下发检查任务"
            width="60%"
            @closed="resetForm"
            :close-on-click-modal="false"
        >
            <el-form :model="dispatchForm" ref="dispatchFormRef" label-width="100px">
                <el-form-item label="检查名称" required prop="name">
                    <el-input v-model="dispatchForm.name" placeholder="请输入检查任务的名称"></el-input>
                </el-form-item>
                <el-form-item label="检查类别" required prop="categories">
                     <el-tree-select
                        v-model="dispatchForm.categories"
                        :data="categoryOptions"
                        multiple
                        show-checkbox
                        check-strictly="false"
                        :props="{ value: 'id', label: 'name', children: 'children' }"
                        placeholder="请选择需要检查的类别"
                    />
                </el-form-item>
                 <el-form-item label="填报项" required prop="fields">
                    <el-select v-model="dispatchForm.fields" multiple placeholder="请选择需要填报的项目" style="width: 100%">
                        <el-option
                            v-for="item in fieldOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                     <div class="text-xs text-gray-500 mt-1">默认包含基础字段(如位置、描述)，请选择额外的填报项。</div>
                </el-form-item>
                 <el-form-item label="任务单位" required prop="targetUnits">
                     <el-tree-select
                        v-model="dispatchForm.targetUnits"
                        :data="orgUnitOptions"
                        multiple
                        show-checkbox
                        :props="{ value: 'value', label: 'label', children: 'children' }"
                        placeholder="请选择接收任务的单位"
                    />
                </el-form-item>
                 <el-form-item label="截止时间" required prop="deadline">
                    <el-date-picker
                        v-model="dispatchForm.deadline"
                        type="datetime"
                        placeholder="选择任务截止时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%;">
                    </el-date-picker>
                </el-form-item>
                 <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="dispatchForm.remarks" :rows="3" placeholder="请输入任务备注或说明"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitDispatch">下发任务</el-button>
                </span>
            </template>
        </el-dialog>
    </div>

    <!-- 查看进度 Modal - Updated Style -->
    <div id="viewProgressModalApp">
        <el-dialog
            v-model="dialogVisible"
            :title="'查看任务进度: ' + taskName"
            width="50%"
            :close-on-click-modal="false"
        >
            <!-- Replace el-table with a simple list -->
            <div class="border rounded-md">
                <div class="bg-gray-100 px-4 py-2 font-semibold flex justify-between border-b">
                    <span>单位名称</span>
                    <span>完成状态</span>
                </div>
                <div class="max-h-60 overflow-y-auto">
                     <div v-if="progressList.length === 0" class="px-4 py-3 text-gray-500 text-center">暂无进度数据</div>
                     <div v-for="(item, index) in progressList" :key="index" class="flex justify-between items-center px-4 py-2" :class="{'border-t': index > 0}">
                        <span class="text-gray-800">{{ item.unitName }}</span>
                        <span :class="getStatusTextColor(item.status)">{{ item.status }}</span>
                    </div>
                </div>
           </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary" @click="dialogVisible = false">关闭</el-button>
                </span>
            </template>
        </el-dialog>
    </div>

     <!-- 催办 Modal - New -->
    <div id="urgeModalApp">
        <el-dialog
            v-model="dialogVisible"
            title="确认催办"
            width="30%"
            :close-on-click-modal="false"
        >
            <p>确定要向 <strong>{{ taskName }}</strong> 任务中尚未完成的单位发送催办通知吗？</p>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="warning" @click="confirmUrge">确认催办</el-button>
                </span>
            </template>
        </el-dialog>
    </div>

    <!-- 查看结果摘要 Modal - New -->
    <div id="viewResultModalApp">
        <el-dialog
            v-model="dialogVisible"
            title="检查结果摘要"
            width="40%"
            :close-on-click-modal="false"
        >
            <div class="space-y-3 mb-6">
                <p><strong>任务名称:</strong> {{ taskName }}</p>
                <p><strong>风险点数量:</strong> <span class="font-semibold text-red-600">{{ riskPointCount }}</span></p>
                <p><strong>隐患数量:</strong> <span class="font-semibold text-yellow-600">{{ hazardCount }}</span></p>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">关闭</el-button>
                    <el-button type="primary" @click="goToHazardDetails">风险隐患详情</el-button>
                </span>
            </template>
        </el-dialog>
    </div>

    <!-- 删除确认模态框 (通用) -->
    <div id="deleteConfirmModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center hidden">
        <div class="bg-white p-8 rounded-lg">
            <h2 class="text-2xl font-semibold mb-4">确认删除</h2>
            <p>确定要删除该记录吗？</p>
            <div class="mt-4 flex justify-end">
                <button id="confirmDelete" class="bg-red-500 text-white px-4 py-2 rounded">确认</button>
                <button id="cancelDelete" class="bg-gray-300 text-gray-500 px-4 py-2 rounded ml-2">取消</button>
            </div>
        </div>
    </div>

    <!-- Load Libraries -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>

    <!-- Page Specific Scripts -->
    <script>
        // --- Data Definitions ---
        // Mock Category Data (shared between template config and dispatch modal)
        const categoryTreeData = [
            { id: 'cat1', name: '风险路段', children: [
                { id: 'cat1.1', name: '山洪淹没区风险路段' },
                { id: 'cat1.2', name: '地质灾害风险路段' }
            ]},
            { id: 'cat2', name: '工作管理机制隐患' },
            { id: 'cat3', name: '基础保障设施隐患', children: [
                 { id: 'cat3.1', name: '防洪标识' }
            ]},
        ];
        // Mock Org Unit Data (for dispatch modal)
         const orgUnitData = [
            { value: '1', label: '广西壮族自治区交通运输厅', children: [
                { value: '1.1', label: '直属事业单位及专项机构', children: [
                    { value: '1.1.1', label: '自治区公路发展中心' },
                    { value: '1.1.2', label: '自治区高速公路发展中心' },
                    { value: '1.1.3', label: '自治区道路运输发展中心' }
                ]},
                { value: '1.2', label: '市级交通运输局', children: [
                    { value: '1.2.1', label: '钦州市交通运输局' },
                    { value: '1.2.2', label: '南宁市交通运输局' },
                    { value: '1.2.3', label: '玉林市交通运输局' }
                ]}
            ]}
        ];
         // Mock Field List Data (for dispatch modal's field selector)
         const fieldListData = [
             { value: 'field4', label: '隐患位置' },
             { value: 'field5', label: '隐患描述' },
             { value: 'field6', label: '风险等级' },
             { value: 'field7', label: '检查日期' },
             { value: 'field8', label: '现场照片' },
             { value: 'field9', label: '整改建议' },
             // Add more fields as they are configured
         ];
         // Mock Task Progress Data (for View Progress Modal) - Added more examples
         const taskProgressData = {
             task1: {
                 name: "2024年第三季度风险路段专项检查",
                 progress: [
                     { unitName: '南宁市交通运输局', status: '已完成' },
                     { unitName: '钦州市交通运输局', status: '未开始' },
                     { unitName: '玉林市交通运输局', status: '进行中' },
                     { unitName: '北海市交通运输局', status: '未开始' } // Added more data
                 ]
             },
             task2: {
                 name: "汛期前桥梁安全检查",
                 progress: [
                     { unitName: '自治区公路发展中心', status: '已完成' },
                     { unitName: '自治区高速公路发展中心', status: '已完成' }
                 ]
             }
         };
         // Mock Task Result Summary Data (for View Result Modal) - New
          const taskResultData = {
              task2: {
                  name: "汛期前桥梁安全检查",
                  riskPointCount: 5, // Example data
                  hazardCount: 12     // Example data
              }
              // Add more completed tasks here
          };


        // --- Tab Switching Logic - Updated to match plan_list.html logic ---
        const tabButtons = document.querySelectorAll('.tab-btn'); // Use .tab-btn selector
        const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                const tabId = button.getAttribute('data-tab') + 'Tab';

                // Remove active styles from all buttons
                tabButtons.forEach(btn => {
                    btn.classList.remove('text-blue-600', 'border-blue-600', 'active');
                    btn.classList.add('text-gray-500', 'border-transparent');
                });
                // Hide all content panes
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });

                // Add active styles to the clicked button
                button.classList.remove('text-gray-500', 'border-transparent');
                button.classList.add('text-blue-600', 'border-blue-600', 'active');

                // Show the corresponding content pane
                const activeTabContent = document.getElementById(tabId);
                if (activeTabContent) {
                    activeTabContent.classList.add('active');
                }
            });
        });
         // Trigger click on the initially active tab if needed (optional, handled by default class)
         // const initialActiveTab = document.querySelector('.tab-btn.active');
         // if (initialActiveTab) { initialActiveTab.click(); } // Not strictly needed if default classes are set

        // --- Vue Apps for Modals ---
        // Category Modal App (Keep as is)
        const CategoryModalApp = {
            data() {
                return {
                    dialogVisible: false,
                    isEditMode: false,
                    categoryForm: {
                        id: null,
                        parentId: null,
                        name: ''
                    },
                    categoryTreeData: categoryTreeData
                }
            },
            methods: {
                openModal(isEdit = false, categoryData = null) {
                    this.isEditMode = isEdit;
                    if (isEdit && categoryData) {
                        this.categoryForm.id = categoryData.id;
                        this.categoryForm.parentId = categoryData.parent;
                        this.categoryForm.name = categoryData.name;
                    } else {
                        this.resetForm();
                    }
                    this.dialogVisible = true;
                },
                resetForm() {
                    this.categoryForm = { id: null, parentId: null, name: '' };
                    if (this.$refs.categoryFormRef) {
                        this.$refs.categoryFormRef.resetFields(); // Reset validation
                    }
                },
                submitForm() {
                     this.$refs.categoryFormRef.validate((valid) => {
                         if(valid){
                            console.log('Submitting category:', this.categoryForm);
                            // Add API call logic here
                            alert('操作成功 (模拟)');
                            this.dialogVisible = false;
                            // Refresh category table
                         } else {
                             console.log('Category form validation failed');
                             return false;
                         }
                     });
                }
            }
        };
        const categoryModalVm = Vue.createApp(CategoryModalApp);
        categoryModalVm.use(ElementPlus);
        const mountedCategoryModal = categoryModalVm.mount('#categoryModalApp');

        // Field Modal App (Keep as is)
        const FieldModalApp = {
            data() {
                return {
                    dialogVisible: false,
                    isEditMode: false,
                    fieldForm: {
                        id: null,
                        name: '',
                        type: 'text',
                        options: '',
                        required: false,
                        description: ''
                    }
                }
            },
             methods: {
                 openModal(isEdit = false, fieldData = null) {
                    this.isEditMode = isEdit;
                    if (isEdit && fieldData) {
                        this.fieldForm = { ...fieldData };
                    } else {
                        this.resetForm();
                    }
                    this.dialogVisible = true;
                },
                 resetForm() {
                     this.fieldForm = {
                         id: null, name: '', type: 'text', options: '',
                         required: false, description: ''
                     };
                     if (this.$refs.fieldFormRef) {
                        this.$refs.fieldFormRef.resetFields();
                    }
                 },
                 submitForm() {
                     this.$refs.fieldFormRef.validate((valid) => {
                         if(valid){
                            console.log('Submitting field:', this.fieldForm);
                             // Add API call logic here
                             alert('操作成功 (模拟)');
                             this.dialogVisible = false;
                             // Refresh field table
                         } else {
                            console.log('Field form validation failed');
                            return false;
                         }
                     });
                 }
            }
        };
        const fieldModalVm = Vue.createApp(FieldModalApp);
        fieldModalVm.use(ElementPlus);
        const mountedFieldModal = fieldModalVm.mount('#fieldModalApp');

        // Dispatch Modal App - Keep as is
         const DispatchModalApp = {
            data() {
                return {
                    dialogVisible: false,
                    dispatchForm: {
                        name: '',
                        categories: [],
                        fields: [],
                        targetUnits: [],
                        deadline: '',
                        remarks: ''
                    },
                    categoryOptions: categoryTreeData, // Use shared category data
                    fieldOptions: fieldListData,       // Use mock field list
                    orgUnitOptions: orgUnitData        // Use org unit data
                }
            },
            methods: {
                openModal() {
                    this.resetForm(); // Ensure form is clean when opening
                    this.dialogVisible = true;
                },
                resetForm() {
                    this.dispatchForm = {
                        name: '', categories: [], fields: [], targetUnits: [], deadline: '', remarks: ''
                    };
                     if (this.$refs.dispatchFormRef) {
                        this.$refs.dispatchFormRef.resetFields();
                    }
                },
                submitDispatch() {
                    this.$refs.dispatchFormRef.validate((valid) => {
                        if(valid){
                           console.log('Submitting dispatch request:', this.dispatchForm);
                           // TODO: Add API call logic here
                           alert('检查任务下发成功 (模拟)');
                           this.dialogVisible = false;
                           // Refresh dispatch list table
                        } else {
                           console.log('Dispatch form validation failed');
                           ElementPlus.ElMessage.error('请检查表单必填项!');
                           return false;
                        }
                    });
                }
            }
        };
        const dispatchModalVm = Vue.createApp(DispatchModalApp);
        dispatchModalVm.use(ElementPlus);
        const mountedDispatchModal = dispatchModalVm.mount('#dispatchModalApp');

         // View Progress Modal App - Updated Template Logic
         const ViewProgressModalApp = {
             data() {
                 return {
                     dialogVisible: false,
                     taskName: '',
                     progressList: []
                 };
             },
             methods: {
                 openModal(taskId, taskName) {
                     this.taskName = taskName;
                     // Fetch or use mock data based on taskId
                     const data = taskProgressData[taskId] || { name: taskName, progress: [] };
                     this.progressList = data.progress;
                     this.dialogVisible = true;
                 },
                 // Removed getStatusTagType, using simple text color now
                 getStatusTextColor(status) { // New method for text color
                     switch (status) {
                         case '已完成': return 'text-green-600 font-semibold';
                         case '进行中': return 'text-blue-600';
                         case '未开始': return 'text-gray-500';
                         default: return 'text-yellow-600'; // For unexpected statuses
                     }
                 }
             }
         };
         const viewProgressModalVm = Vue.createApp(ViewProgressModalApp);
         viewProgressModalVm.use(ElementPlus);
         const mountedViewProgressModal = viewProgressModalVm.mount('#viewProgressModalApp');

         // Urge Modal App - Keep as is
         const UrgeModalApp = {
             data() {
                 return {
                     dialogVisible: false,
                     taskName: '',
                     taskId: null
                 };
             },
             methods: {
                 openModal(taskId, taskName) {
                     this.taskId = taskId;
                     this.taskName = taskName;
                     this.dialogVisible = true;
                 },
                 confirmUrge() {
                     console.log(`Sending urge notification for task: ${this.taskName} (ID: ${this.taskId}`);
                     // TODO: Implement actual API call for urging
                     alert(`已向"${this.taskName}"任务的未完成单位发送催办通知 (模拟)`);
                     this.dialogVisible = false;
                 }
             }
         };
         const urgeModalVm = Vue.createApp(UrgeModalApp);
         urgeModalVm.use(ElementPlus);
         const mountedUrgeModal = urgeModalVm.mount('#urgeModalApp');

          // View Result Modal App - New
          const ViewResultModalApp = {
             data() {
                 return {
                     dialogVisible: false,
                     taskName: '',
                     taskId: null,
                     riskPointCount: '-', // Default placeholder
                     hazardCount: '-'    // Default placeholder
                 };
             },
             methods: {
                 openModal(taskId, taskName) {
                     this.taskId = taskId;
                     this.taskName = taskName;
                     // Fetch or use mock data based on taskId
                     const result = taskResultData[taskId] || { name: taskName, riskPointCount: 'N/A', hazardCount: 'N/A' };
                     this.riskPointCount = result.riskPointCount;
                     this.hazardCount = result.hazardCount;
                     this.dialogVisible = true;
                 },
                 goToHazardDetails() {
                     console.log(`Navigating to hazard list for task ID: ${this.taskId}`);
                     // Redirect to hazard check list page. Might pass taskId as query param later.
                     window.location.href = 'hazard_check_list.html';
                 }
             }
         };
         const viewResultModalVm = Vue.createApp(ViewResultModalApp);
         viewResultModalVm.use(ElementPlus);
         const mountedViewResultModal = viewResultModalVm.mount('#viewResultModalApp');


        // --- Event Listeners ---
        // Add dispatch button listener
        document.getElementById('btnDispatchCheck').addEventListener('click', () => mountedDispatchModal.openModal());

        // Keep existing listeners for template config buttons
        document.getElementById('btnAddCategory').addEventListener('click', () => mountedCategoryModal.openModal());
        document.getElementById('btnAddField').addEventListener('click', () => mountedFieldModal.openModal());

         // --- Delegation for Table Buttons - Updated ---
         document.getElementById('main-content').addEventListener('click', (event) => {
             const button = event.target.closest('button');
             if (!button) return;

             const row = button.closest('tr.task-row'); // Target only task rows
             const taskId = button.dataset.id;
             let taskName = '未知任务';
             let taskStatus = '';

             if(row && taskId){
                const nameCell = row.querySelector('.task-name');
                taskName = nameCell ? nameCell.textContent.trim() : taskName;
                 const statusCell = row.querySelector('.task-status span'); // Get span inside status cell
                taskStatus = statusCell ? statusCell.textContent.trim() : '';
             }

             // Dispatch Tab Buttons
             if (button.classList.contains('btn-view-progress')) {
                  if(taskId) {
                     if (taskStatus === '已完成') {
                         // Open Result Summary Modal for completed tasks
                         mountedViewResultModal.openModal(taskId, taskName);
                     } else {
                         // Open Progress Modal for ongoing tasks
                         mountedViewProgressModal.openModal(taskId, taskName);
                     }
                  }
             } else if (button.classList.contains('btn-urge')) {
                 if(taskId){
                    mountedUrgeModal.openModal(taskId, taskName);
                 }
             }
             // Template Tab - Category Buttons
             else if (button.classList.contains('btn-edit-category')) {
                 const categoryData = {
                     id: button.dataset.id, // Use button's data-id directly
                     name: button.dataset.name,
                     parent: button.dataset.parent || null
                 };
                 mountedCategoryModal.openModal(true, categoryData);
             } else if (button.classList.contains('btn-delete-category')) {
                  const categoryId = button.dataset.id;
                  if(confirm(`确定要删除类别 ID: ${categoryId} 吗？`)) {
                      console.log('Deleting category:', categoryId);
                      // Add delete API call
                  }
             }
             // Template Tab - Field Buttons
             else if (button.classList.contains('btn-edit-field')) {
                  const fieldId = button.dataset.id;
                  // Fetch or find field data by ID
                  const dummyFieldData = {
                      id: fieldId,
                      name: '隐患位置', type: 'text', options: '',
                      required: true, description: '输入详细地址/路段桩号等'
                  }; // Replace with real data
                  mountedFieldModal.openModal(true, dummyFieldData);
             } else if (button.classList.contains('btn-delete-field')) {
                  const fieldId = button.dataset.id;
                  if(confirm(`确定要删除填报项 ID: ${fieldId} 吗？`)) {
                      console.log('Deleting field:', fieldId);
                      // Add delete API call
                  }
             }
         });


    </script>

    <!-- Load component HTML first -->
    <script src="js/navbarComponent_risk.js"></script>
    <script src="js/sidebarComponent_risk.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>
</body>
</html>