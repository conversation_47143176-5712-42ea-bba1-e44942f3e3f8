*, *::before, *::after {
    box-sizing: border-box;
}

html {
    height: 100%; /* Ensure html takes full viewport height */
}

body {
    font-family: "Microsoft YaHei", SimHei, <PERSON>l, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #1a1a1a; /* Darker background for the whole page */
    color: #e0e0e0; /* Lighter text for dark theme */
    display: flex;
    flex-direction: column;
    height: 100%; /* Changed from min-height: 100vh to ensure fixed viewport height */
}

header {
    background-color: #004080; /* Darker official blue */
    color: white;
    padding: 12px 20px; /* Slightly reduced padding */
    text-align: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    z-index: 100; /* Ensure header is on top */
    position: relative; /* Ensure z-index works */
}

header h1 {
    margin: 0;
    font-size: 1.6em; /* Slightly reduced font size */
    font-weight: 500;
    margin-bottom: 10px; /* Add some space below the main title */
}

/* Tab Navigation Styles */
.tab-navigation {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 10px; /* Space below tabs before main content */
}

.tab-button {
    background-color: #0056b3; /* Slightly darker blue than header */
    color: white;
    border: 1px solid #004080;
    padding: 8px 18px;
    margin: 0 5px;
    border-radius: 5px 5px 0 0; /* Rounded top corners for tab feel */
    cursor: pointer;
    font-size: 0.95em;
    text-decoration: none; /* For <a> tags acting as buttons */
    transition: background-color 0.3s ease, border-color 0.3s ease;
    outline: none;
}

.tab-button:hover {
    background-color: #0069d9;
    border-color: #0056b3;
}

.tab-button.active {
    background-color: #007bff; /* Brighter blue for active tab */
    border-bottom-color: #007bff; /* Make bottom border same as background to blend with content area (illusion) */
    /* If you want a more distinct active tab, you could change bottom border or add a thicker top/side border */
    color: #fff;
    font-weight: bold;
}

main {
    display: flex;
    flex: 1;
    flex-direction: column; /* Ensure children stack vertically */
    position: relative; /* For absolute positioning of map and sidebars */
    overflow: hidden; /* Prevent scrollbars if map image is slightly larger */
}

#map-container {
    flex: 1; /* Ensure it takes available space in main flex container */
    position: relative; /* For absolute positioning of markers and lines within it */
    overflow: hidden; /* If map image could somehow overflow this */
}

#map-image {
    width: 100%;
    height: 100%;
    object-fit: cover; /* Cover the area, may crop slightly */
    display: block;
}

.sidebar-right {
    right: 15px;
    top: 15px;
    bottom: 15px; /* Adjust as above */
    width: 300px; /* Or percentage based */
}

.sidebar-right h2 {
    font-size: 1.15em;
    color: #90caff; /* Light blue for heading */
    border-bottom: 1px solid #0056b3;
    padding-bottom: 8px;
    margin-top: 0;
    margin-bottom: 15px;
}

.layer-control label {
    display: block;
    margin-bottom: 10px;
    font-size: 0.95em;
    color: #d0d0d0;
}
.layer-control label:hover {
    color: #ffffff;
}

.layer-control input[type="checkbox"] {
    margin-right: 8px;
    vertical-align: middle;
}

#info-panel {
    margin-top: 20px;
    font-size: 0.9em;
    background-color: rgba(0, 0, 0, 0.3); /* Darker info panel */
    color: #c0c0c0;
    padding: 12px;
    border-radius: 4px;
    min-height: 80px;
    border: 1px solid rgba(255,255,255,0.1);
}

.overview-item {
    margin-bottom: 18px;
    padding: 12px;
    border: 1px solid rgba(255,255,255,0.15);
    border-radius: 4px;
    background-color: rgba(255,255,255,0.05);
}

.overview-item h3 {
    font-size: 1em;
    color: #bbe1ff; /* Lighter blue for subheadings */
    margin-top: 0;
    margin-bottom: 8px;
}

.overview-item p {
    font-size: 0.85em;
    margin: 5px 0;
    color: #b0b0b0;
}

.overview-item button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 7px 14px;
    font-size: 0.8em;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.overview-item button:hover {
    background-color: #0056b3;
}

footer {
    background-color: #111; /* Very dark footer */
    color: #888;
    text-align: center;
    padding: 10px 0;
    font-size: 0.8em;
    z-index: 100; /* Ensure footer is on top */
    position: relative; /* Ensure z-index works */
}

/* --- --- --- --- --- --- --- --- --- --- --- --- --- --- --- --- --- --- */
/* Base for Modal (Single Centered Modal) */
/* --- --- --- --- --- --- --- --- --- --- --- --- --- --- --- --- --- --- */
.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1000; /* Sit on top */
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 700px; /* Default width, adjust as needed */
    max-width: 90vw; /* Max width relative to viewport width */
    max-height: 85vh; /* Max height relative to viewport height */
    overflow: hidden; /* Content will scroll if it overflows */
    background-color: #282c34; /* Modal background color */
    border: 1px solid #444;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.5);
    pointer-events: auto; /* Modal should capture clicks */
}

.modal.modal-wide {
    width: 900px; /* Wider width for specific modals */
    /* max-width will still be inherited from .modal (e.g., 90vw) */
}

.modal.modal-extra-wide {
    width: 1100px; /* Even wider for event modals */
    /* max-width will still be inherited from .modal (e.g., 90vw) */
}

.modal .modal-content { /* Common content style - less changes here */
    /* background-color: #282c34; /* Moved to .modal */
    color: #d0d0d0;
    padding: 25px;
    /* border: 1px solid #444; /* Moved to .modal */
    /* border-radius: 8px; /* Moved to .modal */
    /* box-shadow: 0 5px 15px rgba(0,0,0,0.5); /* Moved to .modal */
    position: relative;
    height: 100%;
    max-height: inherit;
    overflow-y: auto;
    box-sizing: border-box;
}

/* Close button and title styles can largely remain the same from previous version */
.modal .close-button {
    color: #aaa;
    /* float: right; /* No longer needed with absolute positioning */
    font-size: 30px;
    font-weight: bold;
    line-height: 1;
    position: absolute;
    top: 10px;
    right: 15px;
    z-index: 1001; /* Ensure close button is on top of modal content */
}

.modal .close-button:hover,
.modal .close-button:focus {
    color: #fff;
    text-decoration: none;
    cursor: pointer;
}

#modal-title { /* Unified title styling */
    margin-top: 0;
    color: #61afef;
    font-size: 1.7em;
    border-bottom: 1px solid #444;
    padding-bottom: 10px;
    margin-bottom: 20px;
    padding-right: 30px; /* Add padding to prevent title text from going under the close button */
}

#modal-body {
    margin-top: 15px;
    line-height: 1.7;
}

#modal-body h4 {
    font-size: 1.3em;
    color: #98c379; /* Light green for subheadings in modal */
    margin-top: 15px;
    margin-bottom: 8px;
}
#modal-body p, #modal-body ul {
    font-size: 1.15em;
    margin-bottom: 12px;
}
#modal-body ul {
    list-style-type: disc;
    padding-left: 25px;
}

/* Styles for map markers (to be added by JS) */
.map-marker {
    position: absolute;
    width: 18px; /* Increased size */
    height: 18px; /* Increased size */
    border-radius: 50%;
    cursor: pointer;
    z-index: 5;
    border: 2px solid white; /* Slightly thicker border */
    box-shadow: 0 0 10px rgba(0,0,0,0.8); /* Adjusted shadow */
    transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
    display: none; /* Initially hidden, shown by JS if active */
}

.map-marker:hover {
    transform: scale(1.4); /* Increased hover scale */
    box-shadow: 0 0 15px rgba(255,255,255,0.6);
}

/* Styles for simulated line segments on the map */
.map-line {
    position: absolute;
    height: 4px; /* Thickness of the line */
    cursor: pointer;
    z-index: 4; /* Below point markers, above map image */
    box-shadow: 0 0 6px rgba(0,0,0,0.5);
    transform-origin: 0 50%; /* Rotate around the start of the line */
    display: none; /* Initially hidden, shown by JS if active */
    transition: box-shadow 0.2s ease-out;
}

.map-line:hover {
    box-shadow: 0 0 10px rgba(255,255,255,0.5);
}

/* Specific marker/line colors - Ensure these align with checkbox values in HTML */
.marker-risks, .line-risks /* Risks can be points or lines if needed */ {
    background-color: #e06c75; /* Red */
    border-color: #e06c75; /* For points */
}
.marker-rescue, .line-rescue {
    background-color: #61afef; /* Blue */
    border-color: #61afef;
}
.marker-projects, .line-projects {
    background-color: #98c379; /* Green */
    border-color: #98c379;
}
.marker-congestion, .line-congestion /* Congestion will primarily be lines */ {
    background-color: #e5c07b; /* Yellow */
    border-color: #e5c07b;
}
.marker-events, .line-events {
    background-color: #f0a060; /* Orange for events */
    border-color: #f0a060;
}

.marker-supplies {
    background-color: #60f070; /* A distinct green for supplies */
    border-color: #60f070;
}

/* New line types */
.line-highways { background-color: #c678dd; } /* Purple for highways */
.line-waterways { background-color: #56b6c2; } /* Teal for waterways */
.line-roads { background-color: #abb2bf; } /* Gray for important roads */
.line-railways { background-color: #be5046; } /* Darker red/brown for railways */

/* Top Controls Bar Styles */
.top-controls-bar {
    display: flex;
    flex-wrap: wrap; /* Allow wrapping if too many items */
    align-items: center;
    padding: 10px 15px;
    background-color: #2c313a; /* A slightly different dark shade */
    border-bottom: 1px solid #444;
    gap: 15px; /* Gap between main groups */
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px; /* Gap between filter elements */
}

.top-controls-bar select,
.top-controls-bar input[type="text"],
.top-controls-bar button {
    padding: 6px 10px;
    border-radius: 4px;
    border: 1px solid #555;
    background-color: #383c44;
    color: #e0e0e0;
    font-size: 0.9em;
}

.top-controls-bar input[type="text"] {
    min-width: 200px; /* Give search input some width */
}

.top-controls-bar button {
    background-color: #007bff;
    cursor: pointer;
}
.top-controls-bar button:hover {
    background-color: #0056b3;
}

.layer-control-group {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 5px 15px; /* Row and column gap */
}

.layer-control-group label {
    display: inline-flex; /* Align checkbox and text */
    align-items: center;
    margin-bottom: 0; /* Override previous block display margin */
    font-size: 0.9em;
    color: #d0d0d0;
    padding: 5px;
}
.layer-control-group label:hover {
    color: #ffffff;
}
.layer-control-group input[type="checkbox"] {
    margin-right: 5px;
}

.layer-control-hr-top {
    display: none; /* Hide the hr if not needed in horizontal layout, or style differently */
    /* Or style it as a vertical separator if desired */
    /* border-left: 1px solid #555; height: 20px; margin: 0 10px; */
}

/* Map Legend Styles - Modified for bottom horizontal display */
.map-legend {
    /* position: absolute; */ /* Removed for flow layout */
    /* top: 450px; */ /* Removed */
    /* left: 20px; */ /* Removed */
    width: 100%; /* Take full width */
    padding: 10px 0;
    background-color: #2c313a; /* Match top controls bar or other theme color */
    color: #f0f0f0;
    text-align: center; /* Center the legend items container (the ul) */
    border-top: 1px solid #444;
    box-sizing: border-box;
    margin-top: auto; /* Push to bottom if main is flex column */
}

.map-legend h4 {
    display: none; /* Hide title if legend is horizontal and self-explanatory */
    /* Or style differently if title is still desired */
    /* margin-top: 0; margin-bottom: 10px; font-size: 1.1em; color: #90caff; ... */
}

.map-legend ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: inline-block; /* To allow centering of the ul itself */
}

.map-legend li {
    /* margin-bottom: 6px; */ /* Remove bottom margin for horizontal */
    display: inline-flex; /* Align symbol and text, and allow horizontal layout */
    align-items: center;
    font-size: 0.9em;
    margin: 0 10px; /* Space between horizontal legend items */
}

.map-legend .legend-symbol {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border-radius: 50%; /* Default for points */
    display: inline-block;
}

.map-legend .legend-symbol.line {
    height: 4px; /* Match line thickness */
    border-radius: 2px; /* Slightly rounded ends for line symbols */
    width: 20px; /* Example width for legend line */
}

/* --- --- --- --- --- --- --- --- --- --- --- --- --- --- --- --- --- --- */
/* REVISED: Styles for the PRIMARY Modal (#details-modal) - THIS SECTION WILL BE REMOVED */
/* --- --- --- --- --- --- --- --- --- --- --- --- --- --- --- --- --- --- */
/*
#details-modal {
    flex-shrink: 0;
    width: 650px;
    max-width: 48%;
}
*/