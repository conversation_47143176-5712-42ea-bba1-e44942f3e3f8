<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>指挥调度 - 广西交通运输应急管理系统</title>

    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 引入公共样式 -->
    <link rel="stylesheet" href="css/emergency-common.css">
    <link rel="stylesheet" href="new_style.css">

    <!-- 引入指挥调度专用样式 -->
    <link rel="stylesheet" href="css/command-dispatch.css">

    <!-- 引入导航栏组件 -->
    <script src="js/navigation-component.js"></script>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏容器 -->
        <div id="navigation-container"></div>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="command-layout">
                <!-- 左侧菜单栏 -->
                <div class="command-sidebar">
                    <nav class="sidebar-nav">
                        <button class="sidebar-tab active" data-tab="event-report">
                            <i class="fas fa-file-alt"></i>
                            <span>事件接报</span>
                        </button>
                        <button class="sidebar-tab" data-tab="field-command">
                            <i class="fas fa-users"></i>
                            <span>现场指挥协调</span>
                        </button>
                        <button class="sidebar-tab" data-tab="event-review">
                            <i class="fas fa-history"></i>
                            <span>事件回溯分析</span>
                        </button>
                        <button class="sidebar-tab" data-tab="emergency-evaluation">
                            <i class="fas fa-chart-line"></i>
                            <span>应急评估</span>
                        </button>
                    </nav>
                </div>

                <!-- 右侧内容区域 -->
                <div class="command-content">
                <!-- 事件接报标签页 -->
                <div id="event-report" class="tab-pane active">
                    <div class="event-report-container">
                        <!-- 事件接报子导航 -->
                        <div class="sub-nav">
                            <button class="sub-tab active" data-subtab="event-submit">
                                <i class="fas fa-upload"></i>
                                事件上报
                            </button>
                            <button class="sub-tab" data-subtab="received-events">
                                <i class="fas fa-inbox"></i>
                                收到事件
                            </button>
                        </div>

                        <!-- 子标签页内容 -->
                        <div class="sub-content">
                            <!-- 事件上报内容 -->
                            <div id="event-submit" class="sub-pane active">
                                <div class="event-submit-form">
                                    <h3><i class="fas fa-edit"></i> 事件信息录入</h3>
                                    <form id="eventForm">
                                        <!-- 基础信息部分 -->
                                        <div class="form-section">
                                            <h4><i class="fas fa-info-circle"></i> 基础信息</h4>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="eventTitle">事件标题 <span class="required">*</span></label>
                                                    <input type="text" id="eventTitle" name="eventTitle" placeholder="请输入事件标题" required>
                                                </div>
                                                <div class="form-group">
                                                    <label for="eventType">事件类型 <span class="required">*</span></label>
                                                    <select id="eventType" name="eventType" required onchange="toggleAccidentFields()">
                                                        <option value="">请选择事件类型</option>
                                                        <option value="traffic-accident">道路交通事故</option>
                                                        <option value="water-accident">水路交通事故</option>
                                                        <option value="construction-accident">工程建设事故</option>
                                                        <option value="natural-disaster">自然灾害</option>
                                                        <option value="public-health">公共卫生事件</option>
                                                        <option value="social-security">社会安全事件</option>
                                                        <option value="other">其他事件</option>
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="accidentType">事故类型</label>
                                                    <select id="accidentType" name="accidentType">
                                                        <option value="">请选择事故类型</option>
                                                        <option value="collision">碰撞事故</option>
                                                        <option value="rollover">翻车事故</option>
                                                        <option value="fire">火灾事故</option>
                                                        <option value="explosion">爆炸事故</option>
                                                        <option value="hazmat">危化品事故</option>
                                                        <option value="collapse">坍塌事故</option>
                                                        <option value="other">其他</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="eventTime">发生时间 <span class="required">*</span></label>
                                                    <input type="datetime-local" id="eventTime" name="eventTime" required>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="adminRegion">行政辖区 <span class="required">*</span></label>
                                                    <select id="adminRegion" name="adminRegion" required>
                                                        <option value="">请选择行政辖区</option>
                                                        <option value="nanning">南宁市</option>
                                                        <option value="liuzhou">柳州市</option>
                                                        <option value="guilin">桂林市</option>
                                                        <option value="wuzhou">梧州市</option>
                                                        <option value="beihai">北海市</option>
                                                        <option value="fangchenggang">防城港市</option>
                                                        <option value="qinzhou">钦州市</option>
                                                        <option value="guigang">贵港市</option>
                                                        <option value="yulin">玉林市</option>
                                                        <option value="baise">百色市</option>
                                                        <option value="hezhou">贺州市</option>
                                                        <option value="hechi">河池市</option>
                                                        <option value="laibin">来宾市</option>
                                                        <option value="chongzuo">崇左市</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="county">所属区县</label>
                                                    <input type="text" id="county" name="county" placeholder="请输入所属区县">
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="eventLevel">事件等级 <span class="required">*</span></label>
                                                    <select id="eventLevel" name="eventLevel" required>
                                                        <option value="">请选择事件等级</option>
                                                        <option value="level1">Ⅰ级（特别重大）</option>
                                                        <option value="level2">Ⅱ级（重大）</option>
                                                        <option value="level3">Ⅲ级（较大）</option>
                                                        <option value="level4">Ⅳ级（一般）</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="reporter">填报人 <span class="required">*</span></label>
                                                    <input type="text" id="reporter" name="reporter" placeholder="请输入填报人姓名" required>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="longitude">经度</label>
                                                    <input type="number" id="longitude" name="longitude" step="0.000001" placeholder="请输入经度坐标">
                                                </div>
                                                <div class="form-group">
                                                    <label for="latitude">纬度</label>
                                                    <input type="number" id="latitude" name="latitude" step="0.000001" placeholder="请输入纬度坐标">
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="reporterUnit">填报人单位 <span class="required">*</span></label>
                                                    <input type="text" id="reporterUnit" name="reporterUnit" placeholder="请输入填报人所属单位" required>
                                                </div>
                                                <div class="form-group">
                                                    <label for="submitter">上报人</label>
                                                    <input type="text" id="submitter" name="submitter" placeholder="请输入上报人姓名">
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="submitterPhone">上报人电话</label>
                                                    <input type="tel" id="submitterPhone" name="submitterPhone" placeholder="请输入上报人联系电话">
                                                </div>
                                                <div class="form-group">
                                                    <label for="submitterUnit">上报人单位</label>
                                                    <input type="text" id="submitterUnit" name="submitterUnit" placeholder="请输入上报人所属单位">
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="managementUnit">路段管辖单位</label>
                                                    <input type="text" id="managementUnit" name="managementUnit" placeholder="请输入路段管辖单位">
                                                </div>
                                                <div class="form-group">
                                                    <label for="contactPhone">联系电话</label>
                                                    <input type="tel" id="contactPhone" name="contactPhone" placeholder="请输入联系电话">
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="impactScope">影响范围 <span class="required">*</span></label>
                                                    <textarea id="impactScope" name="impactScope" placeholder="请描述影响范围" required></textarea>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="eventLocation">事故详细地址 <span class="required">*</span></label>
                                                    <textarea id="eventLocation" name="eventLocation" placeholder="请输入详细地址" required></textarea>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="eventDescription">事件描述 <span class="required">*</span></label>
                                                    <textarea id="eventDescription" name="eventDescription" placeholder="请详细描述事件经过" required></textarea>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="eventCause">事件原因 <span class="required">*</span></label>
                                                    <textarea id="eventCause" name="eventCause" placeholder="请描述事件发生原因" required></textarea>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="measures">已采取的应急处置措施 <span class="required">*</span></label>
                                                    <textarea id="measures" name="measures" placeholder="请描述已采取的应急处置措施" required></textarea>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="forces">投入的应急力量 <span class="required">*</span></label>
                                                    <textarea id="forces" name="forces" placeholder="请描述投入的应急力量" required></textarea>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="support">需上级应急指挥机构支持事项</label>
                                                    <textarea id="support" name="support" placeholder="请描述需要上级支持的事项"></textarea>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 道路交通事故专项字段 -->
                                        <div class="form-section" id="trafficAccidentFields" style="display: none;">
                                            <h4><i class="fas fa-car-crash"></i> 道路交通事故专项信息</h4>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="routeName">路线名称</label>
                                                    <input type="text" id="routeName" name="routeName" placeholder="请输入路线名称">
                                                </div>
                                                <div class="form-group">
                                                    <label for="sectionName">路段名称</label>
                                                    <input type="text" id="sectionName" name="sectionName" placeholder="请输入路段名称">
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="mileagePost">公里桩号</label>
                                                    <input type="text" id="mileagePost" name="mileagePost" placeholder="请输入公里桩号">
                                                </div>
                                                <div class="form-group">
                                                    <label for="direction">方向</label>
                                                    <select id="direction" name="direction">
                                                        <option value="">请选择方向</option>
                                                        <option value="up">上行</option>
                                                        <option value="down">下行</option>
                                                        <option value="both">双向</option>
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="trafficImpact">是否影响通行</label>
                                                    <select id="trafficImpact" name="trafficImpact">
                                                        <option value="">请选择</option>
                                                        <option value="blocked">完全阻断</option>
                                                        <option value="partial">部分阻断</option>
                                                        <option value="slow">缓慢通行</option>
                                                        <option value="normal">不影响通行</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="vehicleType">事故车型</label>
                                                    <input type="text" id="vehicleType" name="vehicleType" placeholder="请输入事故车型">
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="casualties">人员伤亡情况</label>
                                                    <textarea id="casualties" name="casualties" placeholder="请详细描述人员伤亡情况"></textarea>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="trafficImpactTrend">影响范围及事态发展趋势</label>
                                                    <textarea id="trafficImpactTrend" name="trafficImpactTrend" placeholder="请描述影响范围和发展趋势"></textarea>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="recoveryTime">预计恢复时间</label>
                                                    <input type="datetime-local" id="recoveryTime" name="recoveryTime">
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 水路交通事故专项字段 -->
                                        <div class="form-section" id="waterAccidentFields" style="display: none;">
                                            <h4><i class="fas fa-ship"></i> 水路交通事故专项信息</h4>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="waterway">航道名称</label>
                                                    <input type="text" id="waterway" name="waterway" placeholder="请输入航道名称">
                                                </div>
                                                <div class="form-group">
                                                    <label for="vesselName">船舶名称</label>
                                                    <input type="text" id="vesselName" name="vesselName" placeholder="请输入船舶名称">
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="vesselType">船舶类型</label>
                                                    <select id="vesselType" name="vesselType">
                                                        <option value="">请选择船舶类型</option>
                                                        <option value="cargo">货船</option>
                                                        <option value="passenger">客船</option>
                                                        <option value="fishing">渔船</option>
                                                        <option value="tanker">油轮</option>
                                                        <option value="other">其他</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="vesselTonnage">船舶吨位</label>
                                                    <input type="number" id="vesselTonnage" name="vesselTonnage" placeholder="请输入船舶吨位">
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="waterCasualties">人员伤亡情况</label>
                                                    <textarea id="waterCasualties" name="waterCasualties" placeholder="请详细描述人员伤亡情况"></textarea>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="cargoInfo">货物信息</label>
                                                    <textarea id="cargoInfo" name="cargoInfo" placeholder="请描述货物类型、数量等信息"></textarea>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="environmentalImpact">环境影响</label>
                                                    <textarea id="environmentalImpact" name="environmentalImpact" placeholder="请描述对环境的影响"></textarea>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 工程建设事故专项字段 -->
                                        <div class="form-section" id="constructionAccidentFields" style="display: none;">
                                            <h4><i class="fas fa-hard-hat"></i> 工程建设事故专项信息</h4>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="projectName">工程项目名称</label>
                                                    <input type="text" id="projectName" name="projectName" placeholder="请输入工程项目名称">
                                                </div>
                                                <div class="form-group">
                                                    <label for="constructionUnit">施工单位</label>
                                                    <input type="text" id="constructionUnit" name="constructionUnit" placeholder="请输入施工单位">
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="constructionPhase">施工阶段</label>
                                                    <select id="constructionPhase" name="constructionPhase">
                                                        <option value="">请选择施工阶段</option>
                                                        <option value="foundation">基础施工</option>
                                                        <option value="structure">主体结构</option>
                                                        <option value="decoration">装饰装修</option>
                                                        <option value="installation">设备安装</option>
                                                        <option value="other">其他</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="accidentCategory">事故类别</label>
                                                    <select id="accidentCategory" name="accidentCategory">
                                                        <option value="">请选择事故类别</option>
                                                        <option value="fall">高处坠落</option>
                                                        <option value="collapse">坍塌</option>
                                                        <option value="object-strike">物体打击</option>
                                                        <option value="machinery">机械伤害</option>
                                                        <option value="electric">触电</option>
                                                        <option value="fire-explosion">火灾爆炸</option>
                                                        <option value="other">其他</option>
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="constructionCasualties">人员伤亡情况</label>
                                                    <textarea id="constructionCasualties" name="constructionCasualties" placeholder="请详细描述人员伤亡情况"></textarea>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="propertyLoss">财产损失情况</label>
                                                    <textarea id="propertyLoss" name="propertyLoss" placeholder="请描述财产损失情况"></textarea>
                                                </div>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="safetyMeasures">安全措施落实情况</label>
                                                    <textarea id="safetyMeasures" name="safetyMeasures" placeholder="请描述安全措施落实情况"></textarea>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-actions">
                                            <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                                <i class="fas fa-undo"></i> 重置
                                            </button>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-paper-plane"></i> 上报上级
                                            </button>
                                        </div>
                                    </form>

                                    <!-- 上报状态 -->
                                    <div class="report-status" id="reportStatus" style="display: none;">
                                        <h4><i class="fas fa-info-circle"></i> 上报状态</h4>
                                        <div class="status-item">
                                            <span class="status-label">上级通报：</span>
                                            <span class="status-value" id="reportSent">⏰ 准备上报</span>
                                        </div>
                                        <div class="status-item">
                                            <span class="status-label">上级确认：</span>
                                            <span class="status-value" id="superiorConfirm">⏰ 等待确认</span>
                                        </div>
                                        <div class="status-item" id="superiorComment" style="display: none;">
                                            <span class="status-label">上级批示：</span>
                                            <span class="status-value" id="commentText"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 收到事件内容 -->
                            <div id="received-events" class="sub-pane">
                                <div class="received-events-container">
                                    <div class="events-header">
                                        <h3><i class="fas fa-inbox"></i> 收到事件管理</h3>
                                        <div class="events-filter">
                                            <button class="filter-btn active" data-filter="all">全部</button>
                                            <button class="filter-btn" data-filter="pending">待确认</button>
                                            <button class="filter-btn" data-filter="confirmed">已确认</button>
                                            <button class="filter-btn" data-filter="processing">处置中</button>
                                            <button class="filter-btn" data-filter="completed">已完成</button>
                                        </div>
                                    </div>

                                    <div class="events-list" id="eventsList">
                                        <!-- 事件列表将通过JavaScript动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 现场指挥协调标签页 -->
                <div id="field-command" class="tab-pane">
                    <div class="field-command-container">
                        <!-- 现场指挥协调主内容 -->
                        <div class="command-main-content">
                            <!-- 上半部分 -->
                            <div class="top-row">
                                <!-- 左上：视频会商 -->
                                <div class="video-conference-section" style="height: 100% !important; min-height: 900px !important;">
                                <!-- 视频会商头部 -->
                                <div class="section-header">
                                    <h3><i class="fas fa-video"></i> 视频会商系统</h3>
                                    <div class="conference-controls">
                                        <button class="btn btn-primary" onclick="FieldCommand.startConference()">
                                            <i class="fas fa-play"></i> 开始会商
                                        </button>
                                        <button class="btn btn-secondary" onclick="FieldCommand.joinConference()">
                                            <i class="fas fa-sign-in-alt"></i> 加入会商
                                        </button>
                                    </div>
                                </div>

                                <!-- 视频网格 -->
                                <div class="video-grid-container" style="display: flex !important; flex-direction: column !important; gap: 20px !important; height: 800px !important;">
                                    <!-- 主视频区域 -->
                                    <div class="main-video" style="height: 420px !important; width: 100% !important;">
                                        <div class="video-placeholder">
                                            <i class="fas fa-video-slash"></i>
                                            <p>等待会商开始...</p>
                                        </div>
                                        <div class="video-info">
                                            <span class="participant-name">主会场</span>
                                            <span class="video-status">离线</span>
                                        </div>
                                    </div>

                                    <!-- 参会者视频网格 -->
                                    <div class="participants-video-grid" style="display: grid !important; grid-template-columns: repeat(2, 1fr) !important; grid-template-rows: repeat(3, 1fr) !important; gap: 15px !important; height: 600px !important;">
                                        <div class="participant-video">
                                            <div class="video-placeholder small">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div class="participant-info">
                                                <span class="name">李明华厅长</span>
                                                <span class="status offline">离线</span>
                                            </div>
                                        </div>
                                        <div class="participant-video">
                                            <div class="video-placeholder small">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div class="participant-info">
                                                <span class="name">张德国副厅长</span>
                                                <span class="status offline">离线</span>
                                            </div>
                                        </div>
                                        <div class="participant-video">
                                            <div class="video-placeholder small">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div class="participant-info">
                                                <span class="name">王强组长</span>
                                                <span class="status offline">离线</span>
                                            </div>
                                        </div>
                                        <div class="participant-video">
                                            <div class="video-placeholder small">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div class="participant-info">
                                                <span class="name">现场指挥部</span>
                                                <span class="status offline">离线</span>
                                            </div>
                                        </div>
                                        <div class="participant-video">
                                            <div class="video-placeholder small">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div class="participant-info">
                                                <span class="name">消防救援支队</span>
                                                <span class="status offline">离线</span>
                                            </div>
                                        </div>
                                        <div class="participant-video">
                                            <div class="video-placeholder small">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div class="participant-info">
                                                <span class="name">交警支队</span>
                                                <span class="status offline">离线</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 会商工具栏 -->
                                <div class="conference-toolbar">
                                    <button class="tool-btn" onclick="FieldCommand.toggleMute()">
                                        <i class="fas fa-microphone"></i>
                                        <span>静音</span>
                                    </button>
                                    <button class="tool-btn" onclick="FieldCommand.toggleVideo()">
                                        <i class="fas fa-video"></i>
                                        <span>视频</span>
                                    </button>
                                    <button class="tool-btn" onclick="FieldCommand.shareScreen()">
                                        <i class="fas fa-desktop"></i>
                                        <span>共享</span>
                                    </button>
                                    <button class="tool-btn" onclick="FieldCommand.startRecording()">
                                        <i class="fas fa-record-vinyl"></i>
                                        <span>录制</span>
                                    </button>
                                </div>
                                </div>

                                <!-- 右上：通讯联系管理 -->
                                <div class="communication-section">
                                    <div class="section-header">
                                        <h3><i class="fas fa-phone"></i> 通讯联系管理</h3>
                                        <div class="search-box">
                                            <input type="text" placeholder="搜索联系人..." id="contactSearch">
                                            <i class="fas fa-search"></i>
                                        </div>
                                    </div>

                                    <div class="contacts-container">
                                    <!-- 领导小组 -->
                                    <div class="contact-group">
                                        <div class="group-header" onclick="FieldCommand.toggleGroup('leadership')">
                                            <h4><i class="fas fa-crown"></i> 领导小组</h4>
                                            <i class="fas fa-chevron-down toggle-icon"></i>
                                        </div>
                                        <div class="contact-list" id="leadership">
                                            <div class="contact-item">
                                                <div class="contact-avatar">
                                                    <i class="fas fa-user-tie"></i>
                                                </div>
                                                <div class="contact-info">
                                                    <div class="name">李明华</div>
                                                    <div class="unit">广西壮族自治区交通运输厅</div>
                                                    <div class="position">厅长</div>
                                                    <div class="phone">138****8888</div>
                                                </div>
                                                <div class="contact-actions">
                                                    <button class="btn btn-sm btn-success" onclick="FieldCommand.joinConference('李明华')">
                                                        <i class="fas fa-video"></i> 接入
                                                    </button>
                                                    <button class="btn btn-sm btn-primary" onclick="FieldCommand.makeCall('138****8888', '李明华')">
                                                        <i class="fas fa-phone"></i> 呼叫
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="contact-item">
                                                <div class="contact-avatar">
                                                    <i class="fas fa-user-tie"></i>
                                                </div>
                                                <div class="contact-info">
                                                    <div class="name">张德国</div>
                                                    <div class="unit">广西壮族自治区交通运输厅</div>
                                                    <div class="position">副厅长</div>
                                                    <div class="phone">139****9999</div>
                                                </div>
                                                <div class="contact-actions">
                                                    <button class="btn btn-sm btn-success" onclick="FieldCommand.joinConference('张德国')">
                                                        <i class="fas fa-video"></i> 接入
                                                    </button>
                                                    <button class="btn btn-sm btn-primary" onclick="FieldCommand.makeCall('139****9999', '张德国')">
                                                        <i class="fas fa-phone"></i> 呼叫
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 应急工作组 -->
                                    <div class="contact-group">
                                        <div class="group-header" onclick="FieldCommand.toggleGroup('workgroups')">
                                            <h4><i class="fas fa-users-cog"></i> 应急工作组</h4>
                                            <i class="fas fa-chevron-down toggle-icon"></i>
                                        </div>
                                        <div class="contact-list" id="workgroups">
                                            <div class="contact-item">
                                                <div class="contact-avatar">
                                                    <i class="fas fa-clipboard-list"></i>
                                                </div>
                                                <div class="contact-info">
                                                    <div class="name">陈军</div>
                                                    <div class="unit">广西壮族自治区交通运输厅</div>
                                                    <div class="position">综合协调组组长</div>
                                                    <div class="phone">136****6666</div>
                                                </div>
                                                <div class="contact-actions">
                                                    <button class="btn btn-sm btn-success" onclick="FieldCommand.joinConference('陈军')">
                                                        <i class="fas fa-video"></i> 接入
                                                    </button>
                                                    <button class="btn btn-sm btn-primary" onclick="FieldCommand.makeCall('136****6666', '陈军')">
                                                        <i class="fas fa-phone"></i> 呼叫
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="contact-item">
                                                <div class="contact-avatar">
                                                    <i class="fas fa-hard-hat"></i>
                                                </div>
                                                <div class="contact-info">
                                                    <div class="name">王强</div>
                                                    <div class="unit">广西壮族自治区交通运输厅</div>
                                                    <div class="position">应急指挥组组长</div>
                                                    <div class="phone">135****5555</div>
                                                </div>
                                                <div class="contact-actions">
                                                    <button class="btn btn-sm btn-success" onclick="FieldCommand.joinConference('王强')">
                                                        <i class="fas fa-video"></i> 接入
                                                    </button>
                                                    <button class="btn btn-sm btn-primary" onclick="FieldCommand.makeCall('135****5555', '王强')">
                                                        <i class="fas fa-phone"></i> 呼叫
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="contact-item">
                                                <div class="contact-avatar">
                                                    <i class="fas fa-truck"></i>
                                                </div>
                                                <div class="contact-info">
                                                    <div class="name">孙运输</div>
                                                    <div class="unit">广西壮族自治区交通运输厅</div>
                                                    <div class="position">运输保障组组长</div>
                                                    <div class="phone">134****4444</div>
                                                </div>
                                                <div class="contact-actions">
                                                    <button class="btn btn-sm btn-success" onclick="FieldCommand.joinConference('孙运输')">
                                                        <i class="fas fa-video"></i> 接入
                                                    </button>
                                                    <button class="btn btn-sm btn-primary" onclick="FieldCommand.makeCall('134****4444', '孙运输')">
                                                        <i class="fas fa-phone"></i> 呼叫
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 专家库 -->
                                    <div class="contact-group">
                                        <div class="group-header" onclick="FieldCommand.toggleGroup('experts')">
                                            <h4><i class="fas fa-user-graduate"></i> 专家库</h4>
                                            <i class="fas fa-chevron-down toggle-icon"></i>
                                        </div>
                                        <div class="contact-list" id="experts">
                                            <div class="contact-item">
                                                <div class="contact-avatar">
                                                    <i class="fas fa-graduation-cap"></i>
                                                </div>
                                                <div class="contact-info">
                                                    <div class="name">孙教授</div>
                                                    <div class="unit">广西大学土木建筑工程学院</div>
                                                    <div class="position">交通工程专家</div>
                                                    <div class="phone">133****3333</div>
                                                </div>
                                                <div class="contact-actions">
                                                    <button class="btn btn-sm btn-success" onclick="FieldCommand.joinConference('孙教授')">
                                                        <i class="fas fa-video"></i> 接入
                                                    </button>
                                                    <button class="btn btn-sm btn-primary" onclick="FieldCommand.makeCall('133****3333', '孙教授')">
                                                        <i class="fas fa-phone"></i> 呼叫
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="contact-item">
                                                <div class="contact-avatar">
                                                    <i class="fas fa-tools"></i>
                                                </div>
                                                <div class="contact-info">
                                                    <div class="name">吴高工</div>
                                                    <div class="unit">广西交通设计集团有限公司</div>
                                                    <div class="position">道路救援专家</div>
                                                    <div class="phone">132****2222</div>
                                                </div>
                                                <div class="contact-actions">
                                                    <button class="btn btn-sm btn-success" onclick="FieldCommand.joinConference('吴高工')">
                                                        <i class="fas fa-video"></i> 接入
                                                    </button>
                                                    <button class="btn btn-sm btn-primary" onclick="FieldCommand.makeCall('132****2222', '吴高工')">
                                                        <i class="fas fa-phone"></i> 呼叫
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 外部协作部门 -->
                                    <div class="contact-group">
                                        <div class="group-header" onclick="FieldCommand.toggleGroup('external')">
                                            <h4><i class="fas fa-handshake"></i> 外部协作部门</h4>
                                            <i class="fas fa-chevron-down toggle-icon"></i>
                                        </div>
                                        <div class="contact-list" id="external">
                                            <div class="contact-item">
                                                <div class="contact-avatar">
                                                    <i class="fas fa-fire-extinguisher"></i>
                                                </div>
                                                <div class="contact-info">
                                                    <div class="name">李队长</div>
                                                    <div class="unit">广西消防救援总队</div>
                                                    <div class="position">消防救援支队队长</div>
                                                    <div class="phone">131****1111</div>
                                                </div>
                                                <div class="contact-actions">
                                                    <button class="btn btn-sm btn-success" onclick="FieldCommand.joinConference('李队长')">
                                                        <i class="fas fa-video"></i> 接入
                                                    </button>
                                                    <button class="btn btn-sm btn-primary" onclick="FieldCommand.makeCall('131****1111', '李队长')">
                                                        <i class="fas fa-phone"></i> 呼叫
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="contact-item">
                                                <div class="contact-avatar">
                                                    <i class="fas fa-ambulance"></i>
                                                </div>
                                                <div class="contact-info">
                                                    <div class="name">王医生</div>
                                                    <div class="unit">广西医科大学第一附属医院</div>
                                                    <div class="position">120急救中心主任</div>
                                                    <div class="phone">130****0000</div>
                                                </div>
                                                <div class="contact-actions">
                                                    <button class="btn btn-sm btn-success" onclick="FieldCommand.joinConference('王医生')">
                                                        <i class="fas fa-video"></i> 接入
                                                    </button>
                                                    <button class="btn btn-sm btn-primary" onclick="FieldCommand.makeCall('130****0000', '王医生')">
                                                        <i class="fas fa-phone"></i> 呼叫
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="contact-item">
                                                <div class="contact-avatar">
                                                    <i class="fas fa-car"></i>
                                                </div>
                                                <div class="contact-info">
                                                    <div class="name">赵警官</div>
                                                    <div class="unit">广西壮族自治区公安厅交警总队</div>
                                                    <div class="position">交警高速支队队长</div>
                                                    <div class="phone">129****9999</div>
                                                </div>
                                                <div class="contact-actions">
                                                    <button class="btn btn-sm btn-success" onclick="FieldCommand.joinConference('赵警官')">
                                                        <i class="fas fa-video"></i> 接入
                                                    </button>
                                                    <button class="btn btn-sm btn-primary" onclick="FieldCommand.makeCall('129****9999', '赵警官')">
                                                        <i class="fas fa-phone"></i> 呼叫
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>

                            <!-- 下半部分 -->
                            <div class="bottom-row">
                                <!-- 左下：AI智能指挥记录 -->
                                <div class="ai-record-section">
                                    <div class="section-header">
                                        <h3><i class="fas fa-microphone"></i> AI智能指挥记录</h3>
                                        <div class="record-controls">
                                            <button class="btn btn-primary btn-sm" onclick="FieldCommand.startRecording()" id="recordBtn">
                                                <i class="fas fa-microphone"></i> 开始录音
                                            </button>
                                            <button class="btn btn-secondary btn-sm" onclick="FieldCommand.pauseRecording()" id="pauseBtn" disabled>
                                                <i class="fas fa-pause"></i> 暂停
                                            </button>
                                            <button class="btn btn-danger btn-sm" onclick="FieldCommand.stopRecording()" id="stopBtn" disabled>
                                                <i class="fas fa-stop"></i> 停止
                                            </button>
                                        </div>
                                    </div>

                                    <div class="ai-record-content">
                                        <!-- 实时转录区域 -->
                                        <div class="transcription-panel">
                                            <h4><i class="fas fa-comments"></i> 实时语音转录</h4>
                                            <div class="transcription-display" id="transcriptionDisplay">
                                                <div class="transcription-item">
                                                    <span class="speaker">李明华厅长</span>
                                                    <span class="time">15:42</span>
                                                    <div class="content">现在开始G75高速事故应急指挥会议，请各部门汇报最新情况。</div>
                                                </div>
                                                <div class="transcription-item">
                                                    <span class="speaker">王强组长</span>
                                                    <span class="time">15:43</span>
                                                    <div class="content">现场情况汇报：事故涉及5辆车，3人轻伤已送医，现场正在进行清障作业。</div>
                                                </div>
                                                <div class="transcription-item current">
                                                    <span class="speaker">正在识别...</span>
                                                    <span class="time">15:45</span>
                                                    <div class="content typing">正在转录中...</div>
                                                </div>
                                            </div>

                                            <!-- 录音状态 -->
                                            <div class="recording-status">
                                                <div class="status-indicator">
                                                    <div class="recording-dot"></div>
                                                    <span>录音中</span>
                                                </div>
                                                <div class="recording-time">00:03:25</div>
                                                <div class="audio-level">
                                                    <div class="level-bar"></div>
                                                    <div class="level-bar"></div>
                                                    <div class="level-bar"></div>
                                                    <div class="level-bar"></div>
                                                    <div class="level-bar active"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 右下：AI智能纪要 -->
                                <div class="meeting-summary-section">
                                    <div class="section-header">
                                        <h3><i class="fas fa-file-alt"></i> AI智能纪要</h3>
                                        <div class="summary-actions">
                                            <button class="btn btn-primary btn-sm" onclick="FieldCommand.editSummary()">
                                                <i class="fas fa-edit"></i> 编辑
                                            </button>
                                            <button class="btn btn-secondary btn-sm" onclick="FieldCommand.exportSummary()">
                                                <i class="fas fa-download"></i> 导出
                                            </button>
                                        </div>
                                    </div>

                                    <div class="summary-container">
                                        <div class="summary-content" id="summaryContent">
                                            <div class="summary-section">
                                                <h5>📋 会议基本信息</h5>
                                                <p><strong>会议时间：</strong>2024-12-19 15:42-进行中</p>
                                                <p><strong>主持人：</strong>李明华 (自治区交通运输厅厅长)</p>
                                                <p><strong>参会人员：</strong>张德国、王强、李队长等</p>
                                            </div>

                                            <div class="summary-section">
                                                <h5>🚨 现场情况汇报</h5>
                                                <ul>
                                                    <li>事故涉及5辆车辆相撞</li>
                                                    <li>3人轻伤，已送医治疗</li>
                                                    <li>现场正在进行清障作业</li>
                                                    <li>消防救援力量已到位</li>
                                                </ul>
                                            </div>

                                            <div class="summary-section">
                                                <h5>🤝 部门协调分工</h5>
                                                <ul>
                                                    <li>应急指挥组：负责现场指挥</li>
                                                    <li>消防救援：协助清障作业</li>
                                                    <li>医疗救护：伤员救治</li>
                                                </ul>
                                            </div>

                                            <div class="summary-section">
                                                <h5>📝 主要决策事项</h5>
                                                <ul>
                                                    <li>启动Ⅱ级应急响应</li>
                                                    <li>成立现场指挥部</li>
                                                    <li>优先完成人员救援</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="event-review" class="tab-pane">
                    <div class="event-review-container">
                        <!-- 事件筛选区域 -->
                        <div class="review-header">
                            <div class="review-filters">
                                <div class="filter-group">
                                    <label>时间范围：</label>
                                    <select class="filter-select">
                                        <option value="recent">近一个月</option>
                                        <option value="quarter">近三个月</option>
                                        <option value="year">近一年</option>
                                        <option value="custom">自定义</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label>事件类型：</label>
                                    <select class="filter-select">
                                        <option value="all">全部类型</option>
                                        <option value="traffic">交通事故</option>
                                        <option value="fire">火灾事故</option>
                                        <option value="natural">自然灾害</option>
                                        <option value="other">其他事件</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label>响应等级：</label>
                                    <select class="filter-select">
                                        <option value="all">全部等级</option>
                                        <option value="level1">Ⅰ级响应</option>
                                        <option value="level2">Ⅱ级响应</option>
                                        <option value="level3">Ⅲ级响应</option>
                                        <option value="level4">Ⅳ级响应</option>
                                    </select>
                                </div>
                                <button class="btn btn-primary search-btn">
                                    <i class="fas fa-search"></i> 查询
                                </button>
                            </div>
                        </div>

                        <!-- 事件列表 -->
                        <div class="review-events-list">
                            <div class="events-grid">
                                <!-- 事件卡片1 -->
                                <div class="review-event-card" onclick="EventReview.viewEventDetail('event-001')">
                                    <div class="event-card-header">
                                        <div class="event-type-badge traffic">交通事故</div>
                                        <div class="event-level level-2">Ⅱ级</div>
                                    </div>
                                    <h4 class="event-card-title">泉南高速吴家屯隧道山体塌方事故</h4>
                                    <div class="event-card-meta">
                                        <div class="meta-item">
                                            <i class="fas fa-calendar"></i>
                                            <span>2023-04-20 10:03</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-clock"></i>
                                            <span>处置时长：6小时15分</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <span>泉南高速广西桂林至柳州段改扩建工程吴家屯隧道出口（柳州端）</span>
                                        </div>
                                    </div>
                                    <div class="event-card-summary">
                                        <p>连续强降雨致山体塌方，槽罐车撞击引发连环追尾，10人被困，粗苯泄漏，启动Ⅱ级应急响应</p>
                                    </div>
                                    <div class="event-card-footer">
                                        <span class="status-completed">已完成</span>
                                        <span class="view-detail">查看详情 <i class="fas fa-arrow-right"></i></span>
                                    </div>
                                </div>

                                <!-- 事件卡片2 -->
                                <div class="review-event-card" onclick="EventReview.viewEventDetail('event-002')">
                                    <div class="event-card-header">
                                        <div class="event-type-badge fire">火灾事故</div>
                                        <div class="event-level level-3">Ⅲ级</div>
                                    </div>
                                    <h4 class="event-card-title">服务区餐厅厨房火灾</h4>
                                    <div class="event-card-meta">
                                        <div class="meta-item">
                                            <i class="fas fa-calendar"></i>
                                            <span>2024-01-10 11:20</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-clock"></i>
                                            <span>处置时长：1小时15分</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <span>阳澄湖服务区</span>
                                        </div>
                                    </div>
                                    <div class="event-card-summary">
                                        <p>厨房油烟机起火，无人员伤亡，及时扑灭，启动Ⅲ级应急响应</p>
                                    </div>
                                    <div class="event-card-footer">
                                        <span class="status-completed">已完成</span>
                                        <span class="view-detail">查看详情 <i class="fas fa-arrow-right"></i></span>
                                    </div>
                                </div>

                                <!-- 事件卡片3 -->
                                <div class="review-event-card" onclick="EventReview.viewEventDetail('event-003')">
                                    <div class="event-card-header">
                                        <div class="event-type-badge natural">自然灾害</div>
                                        <div class="event-level level-2">Ⅱ级</div>
                                    </div>
                                    <h4 class="event-card-title">强降雨引发路段积水</h4>
                                    <div class="event-card-meta">
                                        <div class="meta-item">
                                            <i class="fas fa-calendar"></i>
                                            <span>2024-01-08 16:45</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-clock"></i>
                                            <span>处置时长：4小时20分</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <span>G2京沪高速K890-K895</span>
                                        </div>
                                    </div>
                                    <div class="event-card-summary">
                                        <p>强降雨导致5公里路段积水，实施交通管制，启动Ⅱ级应急响应</p>
                                    </div>
                                    <div class="event-card-footer">
                                        <span class="status-completed">已完成</span>
                                        <span class="view-detail">查看详情 <i class="fas fa-arrow-right"></i></span>
                                    </div>
                                </div>

                                <!-- 事件卡片4 -->
                                <div class="review-event-card" onclick="EventReview.viewEventDetail('event-004')">
                                    <div class="event-card-header">
                                        <div class="event-type-badge traffic">交通事故</div>
                                        <div class="event-level level-4">Ⅳ级</div>
                                    </div>
                                    <h4 class="event-card-title">单车侧翻事故</h4>
                                    <div class="event-card-meta">
                                        <div class="meta-item">
                                            <i class="fas fa-calendar"></i>
                                            <span>2024-01-05 09:15</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-clock"></i>
                                            <span>处置时长：45分钟</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <span>G40沪陕高速K567+200</span>
                                        </div>
                                    </div>
                                    <div class="event-card-summary">
                                        <p>货车侧翻占用应急车道，无人员伤亡，启动Ⅳ级应急响应</p>
                                    </div>
                                    <div class="event-card-footer">
                                        <span class="status-completed">已完成</span>
                                        <span class="view-detail">查看详情 <i class="fas fa-arrow-right"></i></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="emergency-evaluation" class="tab-pane">
                    <div class="evaluation-container">
                        <!-- 评估事件筛选区域 -->
                        <div class="evaluation-header">
                            <div class="evaluation-filters">
                                <div class="filter-group">
                                    <label>选择事件：</label>
                                    <select class="filter-select" id="evaluationEventSelect" onchange="EmergencyEvaluation.loadEventEvaluation()">
                                        <option value="">请选择要评估的事件</option>
                                        <option value="event-001">泉南高速吴家屯隧道山体塌方事故</option>
                                        <option value="event-002">服务区餐厅厨房火灾</option>
                                        <option value="event-003">强降雨引发路段积水</option>
                                        <option value="event-004">单车侧翻事故</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label>评估状态：</label>
                                    <select class="filter-select">
                                        <option value="all">全部状态</option>
                                        <option value="pending">待评估</option>
                                        <option value="completed">已完成</option>
                                        <option value="draft">草稿</option>
                                    </select>
                                </div>
                                <button class="btn btn-primary" onclick="EmergencyEvaluation.createNewEvaluation()">
                                    <i class="fas fa-plus"></i> 新建评估
                                </button>
                            </div>
                        </div>

                        <!-- 评估内容区域 -->
                        <div class="evaluation-content" id="evaluationContent">
                            <div class="evaluation-main-content">
                                <!-- 评估基本信息 -->
                                <div class="evaluation-section">
                                    <div class="section-header">
                                        <h3><i class="fas fa-info-circle"></i> 评估基本信息</h3>
                                        <div class="evaluation-actions">
                                            <button class="btn btn-secondary btn-sm" onclick="EmergencyEvaluation.saveDraft()">
                                                <i class="fas fa-save"></i> 保存草稿
                                            </button>
                                            <button class="btn btn-success btn-sm" onclick="EmergencyEvaluation.submitEvaluation()">
                                                <i class="fas fa-check"></i> 提交评估
                                            </button>
                                        </div>
                                    </div>
                                    <div class="basic-info-grid">
                                        <div class="info-item">
                                            <label>事件名称</label>
                                            <span>泉南高速吴家屯隧道山体塌方事故</span>
                                        </div>
                                        <div class="info-item">
                                            <label>评估时间</label>
                                            <span>2023-04-20 18:30</span>
                                        </div>
                                        <div class="info-item">
                                            <label>评估人员</label>
                                            <span>张德国副厅长</span>
                                        </div>
                                        <div class="info-item">
                                            <label>处置时长</label>
                                            <span>6小时15分钟</span>
                                        </div>
                                        <div class="info-item">
                                            <label>响应等级</label>
                                            <span>Ⅱ级应急响应</span>
                                        </div>
                                        <div class="info-item">
                                            <label>最终结果</label>
                                            <span>成功处置，10名被困人员全部救出，粗苯泄漏得到有效控制，道路恢复通行</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 处置效果评估 -->
                                <div class="evaluation-section">
                                    <div class="section-header">
                                        <h3><i class="fas fa-tachometer-alt"></i> 处置效果评估</h3>
                                    </div>

                                    <!-- 人员救援效果 -->
                                    <div class="effect-category">
                                        <h4><i class="fas fa-user-shield"></i> 人员救援效果</h4>
                                        <div class="effect-items">
                                            <div class="effect-item">
                                                <span class="effect-label">救援成功率</span>
                                                <span class="effect-value">100% - 所有10名被困人员成功救出</span>
                                            </div>
                                            <div class="effect-item">
                                                <span class="effect-label">伤亡控制</span>
                                                <span class="effect-value">良好 - 10人受伤，无死亡</span>
                                            </div>
                                            <div class="effect-item">
                                                <span class="effect-label">救援时效</span>
                                                <span class="effect-value">及时 - 平均救援时间87分钟</span>
                                            </div>
                                            <div class="effect-item">
                                                <span class="effect-label">医疗救治</span>
                                                <span class="effect-value">有效 - 伤员得到及时救治，无生命危险</span>
                                            </div>
                                        </div>
                                        <div class="comprehensive-rating">
                                            <span class="rating-label">综合评价：</span>
                                            <div class="star-rating">
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <span class="rating-text">优秀</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 事故处置效果 -->
                                    <div class="effect-category">
                                        <h4><i class="fas fa-car-crash"></i> 事故处置效果</h4>
                                        <div class="effect-items">
                                            <div class="effect-item">
                                                <span class="effect-label">现场安全</span>
                                                <span class="effect-value">良好 - 现场安全管控到位，无二次事故</span>
                                            </div>
                                            <div class="effect-item">
                                                <span class="effect-label">危化品处置</span>
                                                <span class="effect-value">及时 - 粗苯泄漏得到有效控制和清理</span>
                                            </div>
                                            <div class="effect-item">
                                                <span class="effect-label">山体塌方清理</span>
                                                <span class="effect-value">高效 - 塌方体清理完毕，隧道结构安全</span>
                                            </div>
                                            <div class="effect-item">
                                                <span class="effect-label">交通恢复</span>
                                                <span class="effect-value">及时 - 6小时15分钟后恢复通行</span>
                                            </div>
                                        </div>
                                        <div class="comprehensive-rating">
                                            <span class="rating-label">综合评价：</span>
                                            <div class="star-rating">
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="far fa-star"></i>
                                                <span class="rating-text">良好</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 指挥协调效果 -->
                                    <div class="effect-category">
                                        <h4><i class="fas fa-headset"></i> 指挥协调效果</h4>
                                        <div class="effect-items">
                                            <div class="effect-item">
                                                <span class="effect-label">响应速度</span>
                                                <span class="effect-value">优秀 - 5分钟内启动Ⅱ级响应</span>
                                            </div>
                                            <div class="effect-item">
                                                <span class="effect-label">部门协调</span>
                                                <span class="effect-value">优秀 - 消防、环保、交警等多部门协调有序</span>
                                            </div>
                                            <div class="effect-item">
                                                <span class="effect-label">信息沟通</span>
                                                <span class="effect-value">及时 - 山体塌方和危化品泄漏信息及时上报</span>
                                            </div>
                                            <div class="effect-item">
                                                <span class="effect-label">决策质量</span>
                                                <span class="effect-value">优秀 - 人员救援和危化品处置决策科学合理</span>
                                            </div>
                                        </div>
                                        <div class="comprehensive-rating">
                                            <span class="rating-label">综合评价：</span>
                                            <div class="star-rating">
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <span class="rating-text">优秀</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 资源调配评估 -->
                                <div class="evaluation-section">
                                    <div class="section-header">
                                        <h3><i class="fas fa-truck"></i> 资源调配评估</h3>
                                    </div>

                                    <div class="simple-text-content">
                                        <p><strong>消防救援力量：</strong>调配6辆消防车、30名消防员、2辆危化品处置车，平均到达时间12分钟，人员救援和危化品处置及时有效，利用率评价优秀。</p>

                                        <p><strong>医疗救护力量：</strong>调配5辆救护车、15名医护人员，平均到达时间22分钟，10名伤员及时救治，利用率评价优秀。</p>

                                        <p><strong>交警执法力量：</strong>调配12名交警、3辆执法车，平均到达时间18分钟，交通管制及时，疏导有序，利用率评价良好。</p>

                                        <p><strong>山体清理设备：</strong>调配4台挖掘机、3辆清障车、2台吊车，平均到达时间55分钟，到达较慢但清理效果好，利用率评价良好。</p>

                                        <p><strong>环保处置力量：</strong>调配3辆环保监测车、10名环保专家，平均到达时间40分钟，环境监测及时有效，粗苯泄漏处置专业，利用率评价优秀。</p>

                                        <p><strong>总体评价：</strong>资源调配基本到位，各类救援力量配置合理，响应及时，但清障设备到达时间偏长，需要优化布局。</p>
                                    </div>
                                </div>

                                <!-- 组织指挥评估 -->
                                <div class="evaluation-section">
                                    <div class="section-header">
                                        <h3><i class="fas fa-users-cog"></i> 组织指挥评估</h3>
                                    </div>

                                    <div class="simple-text-content">
                                        <p><strong>领导小组：</strong>决策效率优秀，快速决策、指挥有力；统筹协调良好，能够统筹各方资源，整体评价优秀。</p>

                                        <p><strong>领导小组办公室：</strong>信息汇总及时，山体塌方和危化品泄漏信息收集整理及时；上传下达准确，指令传达准确无误，整体评价良好。</p>

                                        <p><strong>应急工作组：</strong>综合协调组协调有序、配合良好；应急指挥组现场指挥得力；运输保障组绕行组织及时，整体评价优秀。</p>

                                        <p><strong>沟通协调效果：</strong>内部沟通顺畅，各工作组沟通顺畅；外部协调良好，与消防、环保、医疗等外部部门协调良好；信息共享及时有效，整体评价良好。</p>
                                    </div>
                                </div>

                                <!-- 损失影响评估 -->
                                <div class="evaluation-section">
                                    <div class="section-header">
                                        <h3><i class="fas fa-chart-line"></i> 损失影响评估</h3>
                                    </div>

                                    <div class="simple-text-content">
                                        <p><strong>人员损失：</strong>10人受伤，无死亡；医疗费用约15万元，误工损失约3万元，小计18万元。</p>

                                        <p><strong>财产损失：</strong>车辆损失约45万元（槽罐车、客车、厢式货车、轿车），货物损失约8万元（粗苯泄漏损失），道路设施约12万元（隧道口设施损毁），小计65万元。</p>

                                        <p><strong>社会影响：</strong>交通延误约1200车次受影响，经济损失约15万元（时间+燃油成本），泉南高速主干道中断6.25小时，社会影响较大，小计15万元。</p>

                                        <p><strong>处置成本：</strong>救援成本约18万元（消防、医疗、环保），人员成本约8万元，设备成本约12万元（挖掘机、清障车等），小计38万元。</p>

                                        <p><strong>总计损失：</strong>约136万元。</p>
                                    </div>
                                </div>

                                <!-- 预案修改建议 -->
                                <div class="evaluation-section">
                                    <div class="section-header">
                                        <h3><i class="fas fa-robot"></i> 预案修改建议（AI生成+可编辑）</h3>
                                        <div class="ai-actions">
                                            <button class="btn btn-primary btn-sm" onclick="EmergencyEvaluation.editSuggestions()">
                                                <i class="fas fa-edit"></i> 编辑
                                            </button>
                                            <button class="btn btn-success btn-sm" onclick="EmergencyEvaluation.adoptSuggestions()">
                                                <i class="fas fa-check"></i> 采纳
                                            </button>
                                        </div>
                                    </div>

                                    <div class="unified-suggestions">
                                        <div class="suggestions-content">
                                            <h4><i class="fas fa-brain"></i> AI生成预案修改意见</h4>
                                            <p class="ai-subtitle">📄 基于本次事件的预案优化建议</p>

                                            <div class="suggestions-text-area">
                                                <textarea id="suggestionText" rows="20" readonly>广西壮族自治区公路交通突发事件应急预案修改建议

修改依据：泉南高速吴家屯隧道山体塌方事故处置实践
评估时间：2023-04-20
评估单位：自治区交通运输厅

一、地质灾害监测预警机制
现行条款：第2.3条 监测预警
存在问题：缺乏针对隧道出口等重点区域的地质灾害专项监测
修改建议：增加"建立隧道出口、高边坡等重点区域地质灾害专项监测系统，雨季加强巡查频次"

二、危化品运输事故处置
现行条款：第4.5条 危化品事故处置
存在问题：危化品泄漏处置专业力量调配时间较长
修改建议：在主要高速路段设置危化品应急处置点，配备专业设备和人员，确保30分钟内到达

三、山体塌方清理机制
新增条款建议：第4.6条 地质灾害清理
修改理由：本次事故因山体塌方引发，需要专门的清理机制
修改建议：建立地质灾害快速清理机制，配备专业清理设备，制定清理作业安全规范

四、多车连环事故救援
现行条款：第5.2条 人员救援
存在问题：多车连环事故中被困人员救援程序不够细化
修改建议：细化多车连环事故救援程序，明确救援优先级和安全防护措施

五、恶劣天气应急管理
现行条款：第3.4条 恶劣天气应对
存在问题：连续强降雨条件下的预防措施不够完善
修改建议：建立连续强降雨预警机制，制定分级管控措施，包括限速、限行、预警发布等

六、应急资源配置优化
现行条款：第6.1条 应急资源保障
存在问题：清障设备布局不合理，响应时间较长
修改建议：优化应急资源布局，在重点路段增设应急物资储备点，提高快速响应能力

七、环境保护应急措施
现行条款：第7.2条 环境保护
存在问题：危化品泄漏环境影响评估和处置措施需要加强
修改建议：完善危化品泄漏环境应急处置程序，建立环境损害评估机制</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 应急响应启动模态框 -->
    <div id="responseModal" class="modal">
        <div class="modal-content response-modal">
            <button class="close-button" onclick="closeResponseModal()">&times;</button>
            <h3><i class="fas fa-rocket"></i> 应急响应启动</h3>
            <div id="responseModalContent">
                <!-- 内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="js/command-dispatch.js"></script>
    <script>
        // 初始化导航栏
        NavigationComponent.init('command-dispatch');

        // 控制专项字段显示
        function toggleAccidentFields() {
            const eventType = document.getElementById('eventType').value;
            const trafficFields = document.getElementById('trafficAccidentFields');
            const waterFields = document.getElementById('waterAccidentFields');
            const constructionFields = document.getElementById('constructionAccidentFields');

            // 隐藏所有专项字段
            trafficFields.style.display = 'none';
            waterFields.style.display = 'none';
            constructionFields.style.display = 'none';

            // 根据事件类型显示对应的专项字段
            switch(eventType) {
                case 'traffic-accident':
                    trafficFields.style.display = 'block';
                    break;
                case 'water-accident':
                    waterFields.style.display = 'block';
                    break;
                case 'construction-accident':
                    constructionFields.style.display = 'block';
                    break;
            }
        }

        // 重置表单函数
        function resetForm() {
            document.getElementById('eventForm').reset();
            toggleAccidentFields(); // 重置后隐藏专项字段
        }

        // 初始化指挥调度系统
        document.addEventListener('DOMContentLoaded', function() {
            CommandDispatch.init();
            FieldCommand.init();
            EmergencyEvaluation.init();

            // 设置当前时间为发生时间的默认值
            const now = new Date();
            const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
            document.getElementById('eventTime').value = localDateTime;
        });
    </script>
</body>
</html>
