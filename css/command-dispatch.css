/* 指挥调度系统专用样式 */

/* 主要内容区域重写 */
.main-content {
    flex-direction: row;
    padding: 0;
    gap: 0;
    height: calc(100vh - 52px);
}

/* 指挥调度布局 */
.command-layout {
    display: flex;
    width: 100%;
    height: 100%;
}

/* 左侧菜单栏 */
.command-sidebar {
    width: 250px;
    background: linear-gradient(180deg, #0056b3 0%, #004494 100%);
    color: white;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 4px rgba(0,0,0,0.1);
    flex-shrink: 0;
}

.sidebar-nav {
    flex: 1;
    padding: 25px 0;
}

.sidebar-tab {
    width: 100%;
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 16px;
    text-align: left;
    border-left: 3px solid transparent;
}

.sidebar-tab:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.sidebar-tab.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border-left-color: white;
    font-weight: 600;
}

.sidebar-tab i {
    font-size: 18px;
    width: 20px;
}

/* 内容区域 */
.command-content {
    flex: 1;
    padding: 25px;
    overflow-y: auto;
    background: #f8f9fa;
}

.tab-pane {
    display: none;
    height: 100%;
}

.tab-pane.active {
    display: block;
}

/* 事件接报容器 */
.event-report-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* 子导航 */
.sub-nav {
    background: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 25px;
    display: flex;
    gap: 15px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.sub-tab {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #6c757d;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 500;
}

.sub-tab:hover {
    background: #e9ecef;
    color: #495057;
    transform: translateY(-1px);
}

.sub-tab.active {
    background: #0056b3;
    color: white;
    border-color: #0056b3;
    box-shadow: 0 3px 6px rgba(0, 86, 179, 0.3);
}

/* 子内容区域 */
.sub-content {
    flex: 1;
    overflow-y: auto;
}

.sub-pane {
    display: none;
}

.sub-pane.active {
    display: block;
}

/* 事件上报表单 */
.event-submit-form {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    width: 100%;
    box-sizing: border-box;
}

.event-submit-form h3 {
    color: #0056b3;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 22px;
    font-weight: 600;
}

.form-row {
    display: flex;
    gap: 25px;
    margin-bottom: 20px;
    width: 100%;
}

.form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0; /* 防止flex子项溢出 */
}

.form-group label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
    font-size: 16px;
}

.required {
    color: #dc3545;
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 14px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #fafafa;
    width: 100%;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #0056b3;
    box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.15);
    background: white;
}

.form-group textarea {
    resize: vertical;
    min-height: 90px;
    font-family: inherit;
    line-height: 1.5;
}

.form-group input[readonly] {
    background: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

/* 表单分组样式 */
.form-section {
    margin-bottom: 35px;
    padding: 25px;
    background: #fafafa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.form-section h4 {
    color: #0056b3;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 18px;
    font-weight: 600;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.form-section:first-child {
    background: white;
    border: none;
    padding: 0;
}

.form-section:first-child h4 {
    color: #333;
    border-bottom: 2px solid #0056b3;
    margin-bottom: 25px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 2px solid #e9ecef;
}

/* 按钮样式增强 */
.btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #0056b3 0%, #004494 100%);
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 14px;
}

/* 上报状态 */
.report-status {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    margin-top: 25px;
    border: 2px solid #e9ecef;
}

.report-status h4 {
    color: #0056b3;
    margin-bottom: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 18px;
}

.status-item {
    display: flex;
    margin-bottom: 12px;
    align-items: center;
}

.status-label {
    font-weight: 600;
    min-width: 120px;
    color: #495057;
    font-size: 16px;
}

.status-value {
    color: #6c757d;
    font-size: 16px;
}

/* 收到事件管理 */
.received-events-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
}

.events-header {
    padding: 25px 30px;
    border-bottom: 2px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.events-header h3 {
    color: #0056b3;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 22px;
    font-weight: 600;
}

.events-filter {
    display: flex;
    gap: 12px;
}

.filter-btn {
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    color: #6c757d;
    padding: 10px 18px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 15px;
    font-weight: 500;
}

.filter-btn:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.filter-btn.active {
    background: #0056b3;
    color: white;
    border-color: #0056b3;
    box-shadow: 0 3px 6px rgba(0, 86, 179, 0.3);
}

.events-list {
    padding: 25px 30px;
}

/* 事件卡片 */
.event-card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.event-card:hover {
    box-shadow: 0 6px 12px rgba(0,0,0,0.1);
    transform: translateY(-3px);
    border-color: #0056b3;
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 18px;
}

.event-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.event-meta {
    font-size: 15px;
    color: #6c757d;
    display: flex;
    gap: 20px;
}

.event-status {
    padding: 8px 16px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: 600;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-confirmed {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-processing {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-completed {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

.response-status {
    margin: 18px 0;
    padding: 20px;
    background: white;
    border-radius: 10px;
    border: 2px solid #dee2e6;
}

.response-status h5 {
    margin: 0 0 12px 0;
    color: #0056b3;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.response-info {
    font-size: 15px;
    color: #495057;
    line-height: 1.6;
}

.event-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 13px;
    border-radius: 4px;
}

/* 统计卡片 */
.statistics-container {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
}

.statistics-container h3 {
    color: #0056b3;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 22px;
    font-weight: 600;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 25px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 18px;
    transition: all 0.3s ease;
}

/* 简化文本内容样式 */
.simple-text-content {
    background: white;
    padding: 25px;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    line-height: 1.8;
}

.simple-text-content p {
    margin-bottom: 15px;
    font-size: 20px;
    color: #333;
}

.simple-text-content p:last-child {
    margin-bottom: 0;
}

.simple-text-content strong {
    color: #0056b3;
    font-weight: 600;
}

/* 统一建议样式 */
.unified-suggestions {
    background: white;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.suggestions-content {
    padding: 25px;
}

.suggestions-content h4 {
    color: #0056b3;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 22px;
    font-weight: 600;
}

.ai-subtitle {
    color: #6c757d;
    margin-bottom: 20px;
    font-size: 18px;
}

.suggestions-text-area {
    margin-top: 15px;
}

.suggestions-text-area textarea {
    width: 100%;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    font-size: 18px;
    line-height: 1.6;
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    color: #333;
    resize: vertical;
    min-height: 400px;
}

.suggestions-text-area textarea:focus {
    outline: none;
    border-color: #0056b3;
    box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.15);
    background: white;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.12);
    border-color: #0056b3;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #0056b3 0%, #004494 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    color: #0056b3;
    line-height: 1;
}

.stat-label {
    font-size: 16px;
    color: #6c757d;
    margin-top: 8px;
    font-weight: 500;
}

/* 占位符内容 */
.placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #6c757d;
    text-align: center;
}

.placeholder-content i {
    font-size: 4em;
    margin-bottom: 20px;
    opacity: 0.5;
}

.placeholder-content h3 {
    margin-bottom: 10px;
    color: #495057;
}

/* 应急响应模态框 */
.response-modal {
    max-width: 600px;
    width: 90%;
}

.response-modal h3 {
    color: #0056b3;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.response-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.event-info,
.plan-selection,
.confirmation {
    padding: 20px;
    background: #2c3e50;
    border-radius: 8px;
    border: 1px solid #34495e;
}

.event-info h4,
.plan-selection h4,
.confirmation h4 {
    margin: 0 0 15px 0;
    color: #3498db;
    font-size: 18px;
    font-weight: 600;
}

.plan-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.plan-header h4 {
    margin: 0;
}

.event-info p,
.confirmation p {
    margin: 8px 0;
    font-size: 16px;
    color: #ecf0f1;
    line-height: 1.5;
}

.event-info p strong,
.confirmation p strong {
    color: #3498db;
    font-weight: 600;
}

.plan-option {
    margin: 15px 0;
    padding: 18px;
    border: 2px solid #34495e;
    border-radius: 8px;
    background: #34495e;
    transition: all 0.3s ease;
}

.plan-option:hover {
    border-color: #3498db;
    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
    background: #3c5a78;
}

.plan-option input[type="radio"] {
    margin-right: 12px;
    transform: scale(1.2);
    accent-color: #3498db;
}

.plan-option label {
    cursor: pointer;
    display: block;
    font-size: 16px;
    color: #ecf0f1;
    line-height: 1.5;
}

.plan-option label strong {
    color: #3498db;
    font-size: 17px;
    font-weight: 600;
    display: block;
    margin-bottom: 8px;
}

.plan-option label small {
    color: #bdc3c7;
    line-height: 1.6;
    font-size: 14px;
    display: block;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #34495e;
}

/* 事件详情模态框 */
.event-details {
    padding: 20px;
    background: #2c3e50;
    border-radius: 8px;
    border: 1px solid #34495e;
}

.event-details h4 {
    margin: 0 0 20px 0;
    color: #3498db;
    font-size: 20px;
    font-weight: 600;
    border-bottom: 2px solid #34495e;
    padding-bottom: 10px;
}

.detail-section {
    margin-bottom: 20px;
}

.detail-section h5 {
    color: #3498db;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.detail-section h5 i {
    font-size: 14px;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.detail-item {
    background: #34495e;
    padding: 12px 15px;
    border-radius: 6px;
    border-left: 3px solid #3498db;
}

.detail-item .label {
    color: #bdc3c7;
    font-size: 14px;
    margin-bottom: 5px;
    font-weight: 500;
}

.detail-item .value {
    color: #ecf0f1;
    font-size: 16px;
    font-weight: 600;
}

.detail-item .value.status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
}

.detail-item .value.status.pending {
    background: #f39c12;
    color: white;
}

.detail-item .value.status.confirmed {
    background: #27ae60;
    color: white;
}

.detail-item .value.status.processing {
    background: #3498db;
    color: white;
}

.detail-item .value.status.completed {
    background: #95a5a6;
    color: white;
}

.detail-description {
    background: #34495e;
    padding: 15px;
    border-radius: 6px;
    border-left: 3px solid #e74c3c;
}

.detail-description .label {
    color: #bdc3c7;
    font-size: 14px;
    margin-bottom: 8px;
    font-weight: 500;
}

.detail-description .value {
    color: #ecf0f1;
    font-size: 15px;
    line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .command-layout {
        flex-direction: column;
    }

    .command-sidebar {
        width: 100%;
        height: auto;
    }

    .sidebar-nav {
        display: flex;
        flex-direction: row;
        padding: 15px 20px;
        gap: 10px;
        overflow-x: auto;
    }

    .sidebar-tab {
        white-space: nowrap;
        padding: 12px 20px;
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }
}

@media (max-width: 768px) {
    .main-content {
        height: auto;
    }

    .command-content {
        padding: 20px;
    }

    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .events-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
        padding: 20px;
    }

    .event-header {
        flex-direction: column;
        gap: 10px;
    }

    .events-list {
        padding: 20px;
    }

    .event-card {
        padding: 20px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .sidebar-tab {
        font-size: 14px;
        padding: 10px 15px;
    }
}

/* 事件详情模态框覆盖层 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(3px);
}

.modal-overlay .modal-content {
    background-color: #2c3e50;
    border-radius: 12px;
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    animation: modalSlideIn 0.3s ease-out;
    position: relative;
    text-align: left !important;
}

.modal-overlay .modal-content * {
    text-align: left !important;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-overlay .modal-header {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: white;
    padding: 20px 25px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #34495e;
    text-align: left !important;
}

.modal-overlay .modal-header * {
    text-align: left !important;
}

.modal-overlay .modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #ecf0f1;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-close {
    color: #bdc3c7;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
    background: none;
    border: none;
    padding: 0;
}

.modal-close:hover {
    color: #ecf0f1;
}

.modal-overlay .modal-body {
    padding: 25px;
    color: #ecf0f1;
    text-align: left !important;
}

.modal-overlay .modal-body * {
    text-align: left !important;
}

/* 预案列表样式 */
.plans-list {
    max-height: 60vh;
    overflow-y: auto;
}

.plan-category {
    margin-bottom: 25px;
}

.plan-category h4 {
    color: #3498db;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    border-bottom: 2px solid #34495e;
    padding-bottom: 8px;
}

.plan-items {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.plan-item {
    background: #34495e;
    padding: 18px;
    border-radius: 8px;
    border-left: 4px solid #3498db;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.plan-item:hover {
    background: #3c5a78;
    transform: translateX(5px);
}

.plan-name {
    font-size: 16px;
    font-weight: 600;
    color: #ecf0f1;
    margin-bottom: 8px;
}

.plan-info {
    display: flex;
    gap: 15px;
    align-items: center;
}

.plan-level {
    background: #e74c3c;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.plan-scope {
    color: #bdc3c7;
    font-size: 14px;
}

.plan-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.plan-actions .btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .plan-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .plan-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

/* 现场指挥协调布局优化 - 减少留白，提高协调性 */
.field-command-container {
    display: flex;
    flex-direction: column;
    padding: 15px;
    gap: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.command-main-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.top-row {
    display: flex;
    gap: 20px;
    min-height: 580px;
}

.bottom-row {
    display: flex;
    gap: 20px;
    min-height: 400px;
}

.video-conference-section {
    flex: 3;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    min-height: 580px;
}

.communication-section {
    flex: 2;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    min-height: 500px;
}

.ai-record-section {
    flex: 3;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    min-height: 400px;
}

.meeting-summary-section {
    flex: 2;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    min-height: 400px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 1px solid #e9ecef;
    flex-shrink: 0;
}

.section-header h3 {
    color: #0056b3;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;
    font-weight: 600;
}

/* 视频会商样式优化 - 让视频更方正，增加长度 */
.video-conference-section .video-grid-container {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1;
    min-height: 0;
}

.video-conference-section .main-video {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-radius: 12px;
    position: relative;
    height: 280px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6px 20px rgba(44, 62, 80, 0.3);
    overflow: hidden;
    width: 100%;
}

.video-conference-section .participants-video-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    grid-template-rows: repeat(2, 1fr) !important;
    gap: 15px !important;
    height: 240px !important;
}

.video-conference-section .participant-video {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
    min-height: 110px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 10px rgba(0,0,0,0.15);
    transition: transform 0.2s ease;
}

.video-conference-section .participant-video:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* 调整视频会商区域整体高度 */
.video-conference-section {
    flex: 3;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    min-height: 580px;
}

/* 调整上半部分高度 */
.top-row {
    display: flex;
    gap: 20px;
    min-height: 580px;
}

/* 强制覆盖视频会商样式 - 更方正的比例 */
.video-conference-section .participants-video-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    grid-template-rows: repeat(2, 1fr) !important;
    gap: 15px !important;
    height: 240px !important;
}

.video-conference-section .main-video {
    height: 280px !important;
}

.video-conference-section .participant-video {
    min-height: 110px !important;
}

/* 优化视频占位符 */
.video-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #bdc3c7;
    font-size: 20px;
}

.video-placeholder i {
    font-size: 56px;
    margin-bottom: 15px;
    opacity: 0.7;
}

.video-placeholder p {
    margin: 10px 0 0 0;
    font-size: 18px;
}

.video-placeholder.small {
    font-size: 16px;
}

.video-placeholder.small i {
    font-size: 32px;
    margin-bottom: 8px;
}

/* 调整视频信息显示 */
.video-info, .participant-info {
    position: absolute;
    bottom: 12px;
    left: 12px;
    right: 12px;
    background: rgba(0,0,0,0.85);
    color: white;
    padding: 10px 14px;
    border-radius: 8px;
    font-size: 14px;
    backdrop-filter: blur(4px);
}

.participant-name, .participant-info .name {
    font-weight: 600;
    display: block;
    font-size: 14px;
    margin-bottom: 3px;
}

.video-status, .participant-info .status {
    font-size: 12px;
    color: #e74c3c;
}

.participant-info .status.online {
    color: #27ae60;
}

.conference-toolbar {
    background: #f8f9fa;
    padding: 18px 25px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-shrink: 0;
}

.conference-toolbar .tool-btn {
    background: white;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    min-width: 80px;
}

.conference-toolbar .tool-btn:hover {
    background: #e9ecef;
    border-color: #0056b3;
    color: #0056b3;
    transform: translateY(-1px);
}

.conference-toolbar .tool-btn i {
    font-size: 18px;
}

/* 通讯联系管理样式优化 - 减少内边距 */
.contacts-container {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
}

.contact-group {
    margin-bottom: 20px;
}

.group-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px 18px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #e9ecef;
}

.group-header:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.group-header h4 {
    margin: 0;
    color: #0056b3;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.toggle-icon {
    transition: transform 0.3s ease;
    color: #6c757d;
    font-size: 14px;
}

.toggle-icon.rotated {
    transform: rotate(180deg);
}

.contact-list {
    padding: 15px 0;
    border-left: 3px solid #0056b3;
    margin-left: 18px;
    padding-left: 20px;
    background: linear-gradient(to right, rgba(0, 86, 179, 0.02) 0%, transparent 100%);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f8f9fa;
    transition: all 0.2s ease;
}

.contact-item:last-child {
    border-bottom: none;
}

.contact-item:hover {
    background: rgba(0, 86, 179, 0.05);
    border-radius: 8px;
    padding-left: 10px;
    padding-right: 10px;
}

.contact-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #0056b3, #004494);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
    box-shadow: 0 2px 4px rgba(0, 86, 179, 0.3);
}

.contact-info {
    flex: 1;
    min-width: 0;
}

.contact-info .name {
    font-weight: 600;
    color: #333;
    font-size: 16px;
    margin-bottom: 3px;
    line-height: 1.2;
}

.contact-info .unit {
    color: #0056b3;
    font-size: 14px;
    margin-bottom: 3px;
    line-height: 1.2;
    font-weight: 500;
}

.contact-info .position {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 3px;
    line-height: 1.2;
}

.contact-info .phone {
    color: #0056b3;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.2;
}

.contact-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.contact-actions .btn-sm {
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 6px;
    min-width: auto;
}

/* 搜索框优化 */
.search-box {
    position: relative;
    width: 250px;
}

.search-box input {
    width: 100%;
    padding: 12px 16px 12px 40px;
    border: 1px solid #dee2e6;
    border-radius: 25px;
    font-size: 14px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #0056b3;
    background: white;
    box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.1);
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 14px;
}

/* AI智能记录样式优化 - 减少内边距 */
.ai-record-content {
    padding: 20px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.transcription-panel {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

.transcription-panel h4 {
    color: #0056b3;
    margin: 0 0 12px 0;
    font-size: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.transcription-display {
    flex: 1;
    overflow-y: auto;
    padding-right: 8px;
}

.transcription-item {
    margin-bottom: 12px;
    padding: 12px;
    background: white;
    border-radius: 6px;
    border-left: 3px solid #0056b3;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    transition: all 0.2s ease;
}

.transcription-item:hover {
    transform: translateX(3px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.transcription-item.current {
    border-left-color: #e74c3c;
    background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.transcription-item .speaker {
    font-weight: 600;
    color: #0056b3;
    font-size: 16px;
}

.transcription-item .time {
    color: #6c757d;
    font-size: 14px;
    margin-left: 10px;
}

.transcription-item .content {
    margin-top: 8px;
    color: #333;
    line-height: 1.6;
    font-size: 16px;
}

.transcription-item .content.typing {
    color: #e74c3c;
    font-style: italic;
}

.recording-status {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 12px 15px;
    border-radius: 8px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 15px;
    flex-shrink: 0;
    box-shadow: 0 2px 6px rgba(44, 62, 80, 0.3);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
}

.recording-dot {
    width: 12px;
    height: 12px;
    background: #e74c3c;
    border-radius: 50%;
    animation: recordingPulse 1.5s infinite;
}

@keyframes recordingPulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
}

.recording-time {
    font-weight: 600;
    font-size: 18px;
    font-family: 'Courier New', monospace;
}

.audio-level {
    display: flex;
    gap: 3px;
    align-items: end;
}

.level-bar {
    width: 4px;
    height: 12px;
    background: rgba(255,255,255,0.3);
    border-radius: 2px;
    transition: all 0.2s ease;
}

.level-bar.active {
    background: #27ae60;
    height: 18px;
}

/* 会议纪要样式优化 - 减少内边距 */
.summary-container {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
}

.summary-content {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

.summary-section {
    margin-bottom: 15px;
}

.summary-section h5 {
    color: #0056b3;
    margin: 0 0 8px 0;
    font-size: 17px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.summary-section p {
    margin: 0 0 6px 0;
    color: #333;
    font-size: 16px;
    line-height: 1.5;
}

.summary-section li {
    color: #333;
    font-size: 16px;
}

/* ==================== 应急评估样式 ==================== */
.evaluation-container {
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.evaluation-header {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.evaluation-filters {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: 600;
    color: #333;
    font-size: 16px;
}

.evaluation-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    min-height: 600px;
    padding: 20px;
}

.evaluation-main-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.evaluation-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
    margin-bottom: 20px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.section-header h3,
.section-header h4 {
    margin: 0;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
}

.evaluation-actions,
.ai-actions {
    display: flex;
    gap: 10px;
}

/* 基本信息网格 */
.basic-info-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    background: white;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.basic-info-grid .info-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.basic-info-grid .info-item label {
    font-weight: 600;
    color: #666;
    font-size: 18px;
}

.basic-info-grid .info-item span {
    font-size: 20px;
    color: #333;
    font-weight: 500;
}

/* 处置效果评估 */
.effect-category,
.resource-category,
.command-category,
.loss-category,
.conclusion-category,
.ai-suggestions-category {
    background: white;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.effect-category h4,
.resource-category h4,
.command-category h4,
.loss-category h4,
.conclusion-category h4 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 22px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.effect-item,
.resource-item,
.command-item,
.loss-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid #e9ecef;
}

.effect-header,
.resource-header,
.command-header,
.loss-header {
    margin-bottom: 15px;
}

.effect-header h5,
.resource-header h5,
.command-header h5,
.loss-header h5 {
    margin: 0;
    color: #333;
    font-size: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.effect-items,
.resource-details,
.command-details,
.loss-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.effect-item .effect-label,
.resource-stat .stat-label,
.command-stat .stat-label,
.loss-stat .stat-label {
    font-weight: 600;
    color: #555;
    margin-right: 10px;
    min-width: 120px;
    font-size: 18px;
}

.effect-item .effect-value,
.resource-stat .stat-value,
.command-stat .stat-value,
.loss-stat .stat-value {
    color: #333;
    flex: 1;
    font-size: 18px;
}

.effect-item,
.resource-stat,
.command-stat,
.loss-stat {
    display: flex;
    align-items: center;
    padding: 12px 0;
}

.stat-value.success {
    color: #28a745;
    font-weight: 600;
}

.stat-value.warning {
    color: #ffc107;
    font-weight: 600;
}

/* 星级评价 */
.comprehensive-rating,
.resource-rating,
.command-rating {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 10px;
}

.rating-label {
    font-weight: 600;
    color: #555;
    font-size: 18px;
}

.star-rating {
    display: flex;
    align-items: center;
    gap: 3px;
}

.star-rating i {
    color: #ffc107;
    font-size: 20px;
}

.star-rating.large i {
    font-size: 24px;
}

.rating-text {
    margin-left: 10px;
    font-weight: 600;
    color: #333;
    font-size: 18px;
}

/* 资源调配总体评价 */
.resource-overall-rating {
    margin-top: 20px;
    padding: 15px;
    background: #e8f4fd;
    border-radius: 8px;
    border: 1px solid #bee5eb;
}

/* 损失统计 */
.loss-subtotal {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e9ecef;
    font-weight: 600;
}

.subtotal-label {
    color: #666;
}

.subtotal-value {
    color: #dc3545;
    font-size: 18px;
}

.loss-total {
    margin-top: 20px;
    padding: 20px;
    background: #fff3cd;
    border-radius: 8px;
    border: 1px solid #ffeaa7;
    text-align: center;
}

.total-summary {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
}

.total-label {
    font-size: 20px;
    font-weight: 700;
    color: #333;
}

.total-value {
    font-size: 24px;
    font-weight: 700;
    color: #dc3545;
}

/* 综合评估结论 */
.conclusion-item {
    margin-bottom: 20px;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.success-item {
    background: #d4edda;
    border-color: #c3e6cb;
}

.warning-item {
    background: #fff3cd;
    border-color: #ffeaa7;
}

.improvement-item {
    background: #d1ecf1;
    border-color: #bee5eb;
}

.conclusion-header h5 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.conclusion-content ul {
    margin: 0;
    padding-left: 0;
    list-style: none;
}

.conclusion-content li {
    padding: 8px 0;
    font-size: 16px;
    line-height: 1.5;
}

.overall-score {
    margin-top: 20px;
    padding: 20px;
    background: #e8f4fd;
    border-radius: 8px;
    border: 1px solid #bee5eb;
    text-align: center;
}

.score-display {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
}

.score-label {
    font-size: 18px;
    font-weight: 700;
    color: #333;
}

.score-rating {
    display: flex;
    align-items: center;
    gap: 10px;
}

.score-text {
    font-size: 18px;
    font-weight: 700;
    color: #0056b3;
}

/* AI预案修改建议 */
.ai-header {
    margin-bottom: 20px;
}

.ai-header h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.ai-subtitle {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.suggestion-document {
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.document-header {
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.document-header h5 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
    font-weight: 700;
    text-align: center;
}

.document-meta {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-size: 14px;
    color: #666;
}

.suggestion-section {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.suggestion-section:last-child {
    border-bottom: none;
}

.suggestion-section h6 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

.suggestion-item.editable {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
    position: relative;
}

.suggestion-content p {
    margin: 0 0 10px 0;
    line-height: 1.6;
    font-size: 14px;
}

.suggestion-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

/* 空状态 */
.no-evaluation-selected {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #6c757d;
    text-align: center;
}

.no-evaluation-selected i {
    font-size: 64px;
    margin-bottom: 20px;
    color: #0056b3;
    opacity: 0.7;
}

.no-evaluation-selected h3 {
    font-size: 24px;
    margin-bottom: 10px;
    color: #333;
}

.no-evaluation-selected p {
    font-size: 16px;
    color: #6c757d;
}

/* ==================== 事件回溯分析样式 ==================== */
.event-review-container {
    padding: 25px 30px;
    height: 100%;
    overflow-y: auto;
}

.review-header {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
}

.review-filters {
    display: flex;
    align-items: center;
    gap: 25px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: 600;
    color: #333;
    font-size: 15px;
    white-space: nowrap;
}

.filter-select {
    padding: 10px 15px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    color: #333;
    min-width: 140px;
    transition: all 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #0056b3;
    box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.1);
}

.search-btn {
    padding: 10px 20px;
    background: #0056b3;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-btn:hover {
    background: #004494;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 86, 179, 0.3);
}

/* 事件网格布局 */
.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
    gap: 25px;
    padding: 10px 0;
}

/* 事件卡片样式 */
.review-event-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.review-event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #0056b3;
}

.event-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.event-type-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.event-type-badge.traffic {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.event-type-badge.fire {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.event-type-badge.natural {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.event-type-badge.other {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

.event-level {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 700;
    text-align: center;
    min-width: 45px;
}

.event-level.level-1 {
    background: #dc3545;
    color: white;
}

.event-level.level-2 {
    background: #fd7e14;
    color: white;
}

.event-level.level-3 {
    background: #ffc107;
    color: #212529;
}

.event-level.level-4 {
    background: #28a745;
    color: white;
}

.event-card-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 15px 0;
    line-height: 1.4;
}

.event-card-meta {
    margin-bottom: 15px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 14px;
    color: #6c757d;
}

.meta-item i {
    width: 16px;
    color: #0056b3;
}

.event-card-summary {
    margin-bottom: 20px;
}

.event-card-summary p {
    margin: 0;
    color: #555;
    font-size: 14px;
    line-height: 1.5;
}

.event-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.view-detail {
    color: #0056b3;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

.review-event-card:hover .view-detail {
    color: #004494;
    transform: translateX(3px);
}

/* ==================== 事件详情模态框样式 - 强化左对齐 ==================== */
.event-detail-modal {
    max-width: 1200px;
    width: 95%;
    max-height: 90vh;
    background: #2c3e50;
    color: white;
    text-align: left !important;
}

.event-detail-modal * {
    text-align: left !important;
}

.event-detail-modal .modal-header {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    padding: 20px 25px;
    border-bottom: 2px solid #34495e;
    text-align: left !important;
}

.event-detail-modal .modal-header * {
    text-align: left !important;
}

.event-detail-modal .modal-header h3 {
    color: white;
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    text-align: left !important;
}

.event-detail-modal .modal-body {
    padding: 0;
    max-height: calc(90vh - 140px);
    overflow-y: auto;
    text-align: left !important;
}

.event-detail-modal .modal-body * {
    text-align: left !important;
}

.event-detail-content {
    padding: 25px;
    text-align: left !important;
}

.event-detail-content * {
    text-align: left !important;
}

.detail-section {
    background: #34495e;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #4a5f7a;
    text-align: left !important;
}

.detail-section * {
    text-align: left !important;
}

.detail-section h4 {
    color: #3498db;
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 10px;
    border-bottom: 2px solid #4a5f7a;
    text-align: left !important;
}

.detail-section h5 {
    color: #3498db;
    margin: 15px 0 10px 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    text-align: left !important;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px 30px;
    align-items: start;
    text-align: left !important;
}

.info-grid * {
    text-align: left !important;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 5px;
    text-align: left !important;
    justify-content: flex-start;
}

.info-item * {
    text-align: left !important;
}

.info-item.full-width {
    grid-column: 1 / -1;
    text-align: left !important;
}

.info-item label {
    font-weight: 600;
    color: #bdc3c7;
    white-space: nowrap;
    font-size: 16px;
    flex-shrink: 0;
    text-align: left !important;
    min-width: 140px;
    margin-right: 10px;
}

.info-item span {
    color: white;
    font-size: 16px;
    line-height: 1.4;
    text-align: left !important;
    flex: 1;
    word-break: break-word;
}

.level-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 700;
    text-align: center !important;
    display: inline-block;
}

.level-badge.level-1 {
    background: #dc3545;
    color: white;
}

.level-badge.level-2 {
    background: #fd7e14;
    color: white;
}

.level-badge.level-3 {
    background: #ffc107;
    color: #212529;
}

.level-badge.level-4 {
    background: #28a745;
    color: white;
}

/* 伤亡情况网格 - 强化左对齐 */
.casualties-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 15px;
    margin-top: 10px;
    text-align: left !important;
}

.casualties-grid * {
    text-align: left !important;
}

.casualty-item {
    background: #2c3e50;
    border: 2px solid #4a5f7a;
    border-radius: 10px;
    padding: 15px;
    text-align: left !important;
    transition: all 0.3s ease;
}

.casualty-item * {
    text-align: left !important;
}

.casualty-item:hover {
    border-color: #3498db;
    transform: translateY(-2px);
}

.casualty-number {
    display: block;
    font-size: 28px;
    font-weight: 700;
    color: #3498db;
    margin-bottom: 8px;
    text-align: left !important;
}

.casualty-label {
    font-size: 14px;
    color: #bdc3c7;
    font-weight: 600;
    text-align: left !important;
}

.casualty-details {
    margin-top: 15px;
    padding: 15px;
    background: #2c3e50;
    border-radius: 8px;
    border: 1px solid #4a5f7a;
    text-align: left !important;
}

.casualty-details * {
    text-align: left !important;
}

.casualty-details p {
    margin: 0;
    color: white;
    font-size: 16px;
    line-height: 1.5;
    text-align: left !important;
}

/* 子部分样式 - 强化左对齐 */
.result-subsection {
    margin-bottom: 20px;
    padding: 15px;
    background: #2c3e50;
    border-radius: 8px;
    border: 1px solid #4a5f7a;
    text-align: left !important;
}

.result-subsection * {
    text-align: left !important;
}

.total-loss {
    background: #2c3e50;
    border: 2px solid #3498db;
    border-radius: 8px;
    padding: 15px;
    margin-top: 10px;
    text-align: left !important;
}

.total-loss * {
    text-align: left !important;
}

.total-loss p {
    margin: 0;
    color: white;
    font-weight: 600;
    font-size: 17px;
    text-align: left !important;
}

/* 时间轴样式 - 强化左对齐 */
.timeline {
    position: relative;
    padding-left: 30px;
    text-align: left !important;
}

.timeline * {
    text-align: left !important;
}

.timeline::before {
    content: '';
    position: absolute;
    
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #3498db;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
    background: #2c3e50;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #4a5f7a;
    text-align: left !important;
}

.timeline-item * {
    text-align: left !important;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 20px;
    width: 12px;
    height: 12px;
    background: #3498db;
    border-radius: 50%;
    border: 3px solid #34495e;
}

.timeline-time {
    font-weight: 700;
    color: #3498db;
    font-size: 16px;
    margin-bottom: 8px;
    text-align: left !important;
}

.timeline-content {
    color: white;
    font-size: 16px;
    line-height: 1.5;
    text-align: left !important;
}

/* 决策效果样式 */
.decision-effect {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-align: center !important;
    display: inline-block;
}

/* 会议记录样式 - 强化左对齐 */
.meeting-content {
    margin-top: 15px;
    text-align: left !important;
}

.meeting-content * {
    text-align: left !important;
}

.meeting-points {
    margin-bottom: 15px;
    text-align: left !important;
}

.meeting-points * {
    text-align: left !important;
}

.meeting-points ul {
    margin: 10px 0 0 20px;
    color: white;
    text-align: left !important;
}

.meeting-points li {
    margin-bottom: 8px;
    font-size: 16px;
    line-height: 1.5;
    text-align: left !important;
}

.meeting-audio {
    display: flex;
    gap: 10px;
    text-align: left !important;
}

.meeting-audio .btn {
    padding: 8px 16px;
    font-size: 14px;
}

.no-data {
    color: #bdc3c7;
    font-style: italic;
    text-align: left !important;
    padding: 20px;
    font-size: 16px;
}

/* 列表样式 - 强化左对齐 */
.lessons-list,
.problems-list,
.improvements-list {
    margin: 0;
    padding-left: 20px;
    text-align: left !important;
}

.lessons-list *,
.problems-list *,
.improvements-list * {
    text-align: left !important;
}

.lessons-list li,
.problems-list li,
.improvements-list li {
    color: white;
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 10px;
    text-align: left !important;
}

.lessons-list li::marker {
    color: #3498db;
}

.problems-list li::marker {
    color: #bdc3c7;
}

.improvements-list li::marker {
    color: #3498db;
}

/* 模态框底部 - 强化左对齐 */
.event-detail-modal .modal-footer {
    background: #34495e;
    padding: 15px 25px;
    border-top: 2px solid #4a5f7a;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    text-align: left !important;
}

.event-detail-modal .modal-footer * {
    text-align: left !important;
}

.event-detail-modal .modal-footer .btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center !important;
}

.event-detail-modal .modal-footer .btn-secondary {
    background: #6c757d;
    color: white;
}

.event-detail-modal .modal-footer .btn-secondary:hover {
    background: #5a6268;
}

.event-detail-modal .modal-footer .btn-primary {
    background: #0056b3;
    color: white;
}

.event-detail-modal .modal-footer .btn-primary:hover {
    background: #004494;
}

.summary-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.record-controls {
    display: flex;
    gap: 12px;
}

/* 响应式设计优化 - 适配新的视频尺寸 */
@media (max-width: 1400px) {
    .top-row {
        min-height: 520px;
    }

    .bottom-row {
        min-height: 350px;
    }

    .video-conference-section .main-video {
        height: 240px !important;
    }

    .video-conference-section .participants-video-grid {
        height: 200px !important;
    }

    .video-conference-section .participant-video {
        min-height: 90px !important;
    }

    .video-conference-section {
        min-height: 520px;
    }

    .communication-section {
        min-height: 520px;
    }

    .ai-record-section {
        min-height: 350px;
    }

    .meeting-summary-section {
        min-height: 350px;
    }
}

@media (max-width: 1200px) {
    .field-command-container {
        padding: 12px;
        gap: 15px;
    }

    .command-main-content {
        gap: 15px;
    }

    .top-row, .bottom-row {
        gap: 15px;
        min-height: 450px;
    }

    .video-grid-container {
        padding: 15px;
        gap: 15px;
    }

    .video-conference-section .main-video {
        height: 220px !important;
    }

    .video-conference-section .participants-video-grid {
        height: 180px !important;
    }

    .video-conference-section .participant-video {
        min-height: 80px !important;
    }

    .section-header {
        padding: 14px 16px;
    }

    .section-header h3 {
        font-size: 15px;
    }

    .contacts-container {
        padding: 15px;
    }

    .ai-record-content {
        padding: 15px;
    }

    .summary-container {
        padding: 15px;
    }
}

@media (max-width: 1024px) {
    .command-main-content {
        flex-direction: column;
    }

    .top-row {
        flex-direction: column;
        min-height: auto;
    }

    .bottom-row {
        flex-direction: column;
        min-height: auto;
    }

    .video-conference-section,
    .communication-section,
    .ai-record-section,
    .meeting-summary-section {
        flex: none;
        min-height: 400px;
    }

    .video-conference-section .main-video {
        height: 200px !important;
    }

    .video-conference-section .participants-video-grid {
        height: 140px !important;
        grid-template-columns: repeat(6, 1fr) !important;
        grid-template-rows: 1fr !important;
    }

    .video-conference-section .participant-video {
        min-height: 120px !important;
    }
}

@media (max-width: 768px) {
    .field-command-container {
        padding: 10px;
        gap: 12px;
    }

    .video-conference-section,
    .communication-section,
    .ai-record-section,
    .meeting-summary-section {
        min-height: 350px;
    }

    .video-conference-section .main-video {
        height: 180px !important;
    }

    .video-conference-section .participants-video-grid {
        height: 120px !important;
        grid-template-columns: repeat(3, 1fr) !important;
        grid-template-rows: repeat(2, 1fr) !important;
    }

    .video-conference-section .participant-video {
        min-height: 50px !important;
    }

    .section-header {
        padding: 12px 15px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .section-header h3 {
        font-size: 14px;
    }

    .conference-controls,
    .record-controls,
    .summary-actions {
        flex-direction: column;
        gap: 6px;
        width: 100%;
    }

    .conference-controls .btn,
    .record-controls .btn,
    .summary-actions .btn {
        width: 100%;
        font-size: 12px;
        padding: 8px 12px;
    }

    .search-box {
        width: 100%;
    }

    .contact-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        padding: 10px 0;
    }

    .contact-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .conference-toolbar {
        flex-wrap: wrap;
        gap: 8px;
        padding: 12px 15px;
    }

    .conference-toolbar .tool-btn {
        flex: 1;
        min-width: 60px;
        font-size: 11px;
        padding: 8px 10px;
    }

    .transcription-item {
        padding: 10px;
    }

    .transcription-item .content {
        font-size: 13px;
    }

    .transcription-item .speaker {
        font-size: 13px;
    }

    .recording-status {
        flex-direction: column;
        gap: 8px;
        text-align: center;
        padding: 10px 12px;
    }

    .summary-section h5 {
        font-size: 14px;
    }

    .summary-section p,
    .summary-section li {
        font-size: 13px;
    }

    .contacts-container {
        padding: 12px;
    }

    .ai-record-content {
        padding: 12px;
    }

    .summary-container {
        padding: 12px;
    }

    .transcription-panel {
        padding: 12px;
    }

    .summary-content {
        padding: 12px;
    }
}

@media (max-width: 480px) {
    .video-conference-section .main-video {
        height: 160px !important;
    }

    .video-conference-section .participants-video-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        grid-template-rows: repeat(3, 1fr) !important;
        height: 150px !important;
    }

    .video-conference-section .participant-video {
        min-height: 45px !important;
    }

    .video-placeholder.small i {
        font-size: 20px;
    }

    .contact-avatar {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }

    .contact-info .name {
        font-size: 13px;
    }

    .contact-info .position,
    .contact-info .phone {
        font-size: 11px;
    }

    .contact-actions .btn-sm {
        padding: 4px 8px;
        font-size: 10px;
    }

    .video-conference-section,
    .communication-section,
    .ai-record-section,
    .meeting-summary-section {
        min-height: 300px;
    }

    .transcription-item .content {
        font-size: 12px;
    }

    .transcription-item .speaker {
        font-size: 12px;
    }

    .summary-section p,
    .summary-section li {
        font-size: 12px;
    }
}

.status-indicator span {
    font-size: 16px;
}
